# Star Power Astrology Application - Base Agent Prompt Template

## Project Context

You are working on the **Star Power Astrology Application**, a sophisticated system that combines astrological analysis with news article processing and celebrity identification. This is a production-ready codebase with strict quality requirements and a critical "crown jewel" algorithm that must be preserved exactly.

## Critical Information

### Crown Jewel Algorithm
- **File**: `services/astrology_engine/va_circuit_detector.py`
- **Hash**: `901031e85045a96c62d67f4e9a465cc064b9fad8c7c5a6b31a17d404a8474d62`
- **Line Count**: 683 lines
- **Status**: MUST NEVER BE MODIFIED - This is the core VA (Vibrational Astrology) algorithm
- **Validation**: Always verify hash integrity after any changes to the codebase

### Architecture Overview
```
services/
├── astrology_engine/     # Crown jewel VA algorithm + Swiss Ephemeris integration
├── news_pipeline/        # GNews API + content extraction + celebrity identification
├── llm_processor/        # Multi-model LLM (Ollama primary, OpenAI fallback)
├── database/            # PostgreSQL + Redis with environment-aware configuration
└── api_gateway/         # FastAPI routing and service coordination (TODO)
```

## Setup and Validation Checklist

### 1. Environment Setup
```bash
# Run the automated setup (always start here)
source .augment/setup.sh

# Verify virtual environment is active
which python  # Should show venv/bin/python

# Verify core dependencies
python -c "import swisseph, openai, ollama, psycopg2, redis, fastapi; print('✅ Core deps OK')"
```

### 2. Service Validation
```bash
# Check database services
docker-compose -f docker-compose.dev.yml ps

# Test database connectivity
python -c "
import os
os.environ['ENVIRONMENT'] = 'development'
from services.database import ConnectionManager
manager = ConnectionManager()
config = manager.get_database_config()
print('✅ Database config loaded')
"

# Test LLM services
python -c "
from services.llm_processor import LLMOrchestrator
orchestrator = LLMOrchestrator()
print('✅ LLM orchestrator initialized')
"
```

### 3. Crown Jewel Validation
```bash
# CRITICAL: Always validate crown jewel integrity
python -c "
from services.astrology_engine import VACircuitDetector
import hashlib
with open('services/astrology_engine/va_circuit_detector.py', 'r') as f:
    content = f.read()
hash_value = hashlib.sha256(content.encode()).hexdigest()
expected = '901031e85045a96c62d67f4e9a465cc064b9fad8c7c5a6b31a17d404a8474d62'
assert hash_value == expected, f'❌ CROWN JEWEL COMPROMISED: {hash_value}'
print('✅ Crown jewel integrity verified')
"
```

### 4. Test Suite Execution
```bash
# Run full test suite (142+ tests should pass)
python -m pytest tests/ -v

# Expected results:
# - 142+ tests passing
# - 1 test skipped (normal)
# - 0 failures
# - All performance targets met

# Run specific test categories
python -m pytest tests/unit/ -v                    # Unit tests
python -m pytest tests/integration/ -v             # Integration tests  
python -m pytest tests/performance/ -v             # Performance tests
```

## Development Guidelines

### Code Quality Standards
- **Module Size**: All modules <300 lines (except crown jewel VA algorithm)
- **Service Boundaries**: Maintain clear separation between services
- **Error Handling**: Implement 5-level error handling hierarchy
- **Performance**: VA analysis must complete in <2 seconds
- **Testing**: Maintain >85% test coverage

### Technology Stack (MANDATORY - NO SUBSTITUTIONS)
- **Python**: 3.8+ (runtime)
- **Database**: PostgreSQL (primary) + Redis (caching)
- **LLM**: Ollama (primary) + OpenAI API (fallback)
- **Ephemeris**: Swiss Ephemeris with test mode fallback
- **API**: FastAPI with async/await patterns
- **Testing**: pytest with async support

### Environment Configuration
```bash
# Development environment variables
ENVIRONMENT=development
DB_HOST=localhost
DB_PORT=5432
DB_NAME=starpower_dev
DB_USER=starpower
DB_PASSWORD=starpower_dev_pass
REDIS_URL=redis://localhost:6379/0
OLLAMA_HOST=http://localhost:11434

# Required API keys (update in .env)
OPENAI_API_KEY=your_openai_api_key_here
GNEWS_API_KEY=your_gnews_api_key_here
```

## Common Task Patterns

### Adding New Features
1. **Plan**: Review existing service architecture
2. **Design**: Ensure <300 line module limit
3. **Implement**: Follow async/await patterns
4. **Test**: Write comprehensive unit + integration tests
5. **Validate**: Run full test suite + crown jewel check

### Debugging Issues
1. **Environment**: Verify all services running
2. **Dependencies**: Check virtual environment activation
3. **Configuration**: Validate environment variables
4. **Services**: Test database/LLM connectivity
5. **Tests**: Run specific test categories to isolate issues

### Performance Optimization
1. **Baseline**: Run performance tests to establish current metrics
2. **Profile**: Use memory-profiler and psutil for analysis
3. **Optimize**: Focus on VA algorithm performance (<2s target)
4. **Validate**: Ensure crown jewel integrity maintained
5. **Test**: Verify all performance targets still met

## Quality Gates (MUST PASS)

### Before Any Commit
- [ ] Crown jewel hash validation passes
- [ ] All tests pass (142+ passing, 1 skipped)
- [ ] No import errors in core modules
- [ ] Performance targets met (<2s VA analysis)
- [ ] Code quality checks pass (black, flake8, mypy)

### Before Any PR
- [ ] Full test suite passes
- [ ] Integration tests pass
- [ ] Performance benchmarks meet targets
- [ ] Documentation updated
- [ ] Environment setup validated

## Emergency Procedures

### Crown Jewel Compromise
If crown jewel hash validation fails:
1. **STOP ALL WORK IMMEDIATELY**
2. **DO NOT COMMIT ANY CHANGES**
3. Restore from last known good state
4. Re-validate hash integrity
5. Investigate what caused the change

### Service Failures
```bash
# Reset database services
docker-compose -f docker-compose.dev.yml down -v
docker-compose -f docker-compose.dev.yml up -d

# Reset Python environment
rm -rf venv/
source .augment/setup.sh

# Reset Ollama
ollama stop
ollama serve &
ollama pull dolphin-mixtral
```

## Success Metrics

A successful development session should maintain:
- ✅ Crown jewel integrity (hash unchanged)
- ✅ All tests passing (142+ tests)
- ✅ Performance targets met (<2s VA analysis)
- ✅ Service connectivity (PostgreSQL, Redis, Ollama)
- ✅ Code quality standards (formatting, linting, typing)

## Key Files to Monitor

### Critical Files (Never Modify)
- `services/astrology_engine/va_circuit_detector.py` - Crown jewel algorithm

### Configuration Files
- `.env` - Environment variables
- `requirements.txt` - Python dependencies
- `docker-compose.dev.yml` - Development services

### Test Files
- `tests/unit/test_va_circuit_detector.py` - Crown jewel tests
- `tests/performance/test_va_performance.py` - Performance validation
- `tests/test_database_system.py` - Database integration tests

### Documentation
- `README.md` - Project overview
- `.augment/instructions.md` - Detailed setup instructions
- `star-power-sdlc-claude/` - Complete SDLC documentation

Remember: This is a production-ready system with strict quality requirements. Always validate setup, run tests, and verify crown jewel integrity before and after any changes.
