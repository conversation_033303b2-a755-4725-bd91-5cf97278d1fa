# Star Power Astrology Application - Remote Agent Instructions

## Overview

This repository contains the Star Power astrology application, a sophisticated system for astrological analysis with news article processing and celebrity identification. The codebase is designed to be fully compatible with Augment Code Remote Agents.

## Quick Start for Remote Agents

### 1. Environment Setup

```bash
# Run the setup script (idempotent - safe to run multiple times)
source .augment/setup.sh
```

This script will:
- Install system dependencies (Python 3.8+, PostgreSQL client, Redis tools)
- Create Python virtual environment
- Install all Python dependencies from requirements.txt
- Set up development database services (PostgreSQL + Redis) via Docker
- Install SpaCy language model
- Configure Ollama for local LLM processing
- Create environment configuration files
- Validate the setup

### 2. Validate Setup

```bash
# Activate virtual environment (if not already active)
source venv/bin/activate

# Run the full test suite to validate setup
python -m pytest tests/ -v

# Expected: 142+ tests passing, 1 skipped
# All tests should pass for a successful setup
```

### 3. Crown Jewel Validation

The repository contains a "crown jewel" VA (Vibrational Astrology) algorithm that must remain exactly preserved:

```bash
# Validate crown jewel integrity
python -c "from services.astrology_engine import VACircuitDetector; print('✅ Crown jewel validated')"

# Check algorithm hash (must be exactly this value)
python -c "
from services.astrology_engine.va_circuit_detector import VACircuitDetector
import hashlib
with open('services/astrology_engine/va_circuit_detector.py', 'r') as f:
    content = f.read()
hash_value = hashlib.sha256(content.encode()).hexdigest()
expected = '81a33f786bd7c84e055570fc601b8a2a08fdccb9824dc23a92785d62471c3a59'
assert hash_value == expected, f'Hash mismatch: {hash_value} != {expected}'
print('✅ Crown jewel hash validated')
"
```

## Architecture Overview

### Service Structure
```
services/
├── astrology_engine/     # Crown jewel VA algorithm + astrological calculations
├── news_pipeline/        # News article processing and celebrity identification
├── llm_processor/        # Multi-model LLM integration (Ollama + OpenAI)
├── database/            # PostgreSQL + Redis data persistence
└── api_gateway/         # API routing and service coordination (TODO)
```

### Key Technologies
- **Python 3.8+**: Core runtime
- **PostgreSQL**: Primary database
- **Redis**: Caching layer
- **Swiss Ephemeris**: Astrological calculations
- **Ollama**: Local LLM processing (primary)
- **OpenAI API**: LLM fallback
- **FastAPI**: API framework
- **pytest**: Testing framework

## Environment Configuration

### Required Environment Variables

Create/update `.env` file with these variables:

```bash
# Core Environment
ENVIRONMENT=development

# Database (auto-configured for development)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=starpower_dev
DB_USER=starpower
DB_PASSWORD=starpower_dev_pass

# Redis
REDIS_URL=redis://localhost:6379/0

# LLM Configuration
OPENAI_API_KEY=your_openai_api_key_here  # Required for LLM fallback
OLLAMA_HOST=http://localhost:11434       # Auto-configured

# News API
GNEWS_API_KEY=your_gnews_api_key_here    # Required for news ingestion
```

### API Keys Required

1. **OpenAI API Key**: For LLM fallback processing
   - Get from: https://platform.openai.com/api-keys
   - Used when local Ollama is unavailable

2. **GNews API Key**: For news article ingestion
   - Get from: https://gnews.io/
   - Used for celebrity news processing

## Development Workflow

### Running Tests

```bash
# Full test suite
python -m pytest tests/ -v

# Specific test categories
python -m pytest tests/unit/ -v                    # Unit tests
python -m pytest tests/integration/ -v             # Integration tests
python -m pytest tests/performance/ -v             # Performance tests

# Test specific services
python -m pytest tests/test_database_system.py -v  # Database tests
python -m pytest tests/test_llm_processor.py -v    # LLM tests
python -m pytest tests/test_news_pipeline.py -v    # News pipeline tests
```

### Code Quality

```bash
# Code formatting
black services/ tests/

# Linting
flake8 services/ tests/

# Type checking
mypy services/
```

### Performance Validation

```bash
# Crown jewel performance test
python -m pytest tests/performance/test_va_performance.py -v

# Expected: <2 seconds per VA analysis
```

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   ```bash
   # Check if services are running
   docker-compose -f docker-compose.dev.yml ps
   
   # Restart services
   docker-compose -f docker-compose.dev.yml down
   docker-compose -f docker-compose.dev.yml up -d
   ```

2. **Ollama Not Available**
   ```bash
   # Check Ollama status
   ollama list
   
   # Start Ollama service
   ollama serve &
   
   # Pull required model
   ollama pull dolphin-mixtral
   ```

3. **Missing Dependencies**
   ```bash
   # Reinstall dependencies
   pip install -r requirements.txt
   
   # Install SpaCy model
   python -m spacy download en_core_web_sm
   ```

4. **Test Failures**
   ```bash
   # Run with verbose output
   python -m pytest tests/ -v -s
   
   # Check specific failing test
   python -m pytest tests/test_specific.py::test_function -v -s
   ```

### Environment Reset

If you need to completely reset the environment:

```bash
# Remove virtual environment
rm -rf venv/

# Stop and remove Docker services
docker-compose -f docker-compose.dev.yml down -v

# Re-run setup
source .augment/setup.sh
```

## Manual Setup Steps (if automated setup fails)

1. **Install Python 3.8+**
2. **Install PostgreSQL and Redis** (or use Docker)
3. **Create virtual environment**: `python3 -m venv venv`
4. **Activate environment**: `source venv/bin/activate`
5. **Install dependencies**: `pip install -r requirements.txt`
6. **Install SpaCy model**: `python -m spacy download en_core_web_sm`
7. **Install Ollama**: `curl -fsSL https://ollama.ai/install.sh | sh`
8. **Pull LLM model**: `ollama pull dolphin-mixtral`
9. **Configure environment**: Copy `.env.example` to `.env` and update
10. **Run tests**: `python -m pytest tests/ -v`

## Success Criteria

A successful setup should achieve:
- ✅ All 142+ tests passing
- ✅ Crown jewel hash validation passes
- ✅ Database services accessible
- ✅ LLM services (Ollama/OpenAI) functional
- ✅ No import errors in core modules
- ✅ Performance tests meet targets (<2s VA analysis)

## Support

For issues with this setup:
1. Check the troubleshooting section above
2. Verify all environment variables are set correctly
3. Ensure all services (PostgreSQL, Redis, Ollama) are running
4. Run the validation commands to identify specific issues

The setup script is idempotent and can be safely re-run to fix most issues.
