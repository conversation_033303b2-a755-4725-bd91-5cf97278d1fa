#!/bin/bash
set -e

# Star Power Astrology Application - Remote Agent Setup Script
# This script bootstraps a fresh cloud environment for Augment Code Remote Agents

echo "🌟 Star Power Astrology Application - Remote Agent Setup"
echo "======================================================="

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root (not recommended for development)
if [[ $EUID -eq 0 ]]; then
   log_warning "Running as root. Consider using a non-root user for development."
fi

# Step 1: System Dependencies
log_info "Installing system dependencies..."

# Update package manager
if command -v apt-get &> /dev/null; then
    sudo apt-get update
    sudo apt-get install -y \
        python3 \
        python3-pip \
        python3-venv \
        python3-dev \
        build-essential \
        curl \
        wget \
        git \
        postgresql-client \
        redis-tools \
        libpq-dev \
        libxml2-dev \
        libxslt1-dev \
        zlib1g-dev \
        libjpeg-dev \
        libffi-dev \
        libssl-dev
elif command -v yum &> /dev/null; then
    sudo yum update -y
    sudo yum install -y \
        python3 \
        python3-pip \
        python3-devel \
        gcc \
        gcc-c++ \
        make \
        curl \
        wget \
        git \
        postgresql \
        redis \
        libpq-devel \
        libxml2-devel \
        libxslt-devel \
        zlib-devel \
        libjpeg-devel \
        libffi-devel \
        openssl-devel
else
    log_error "Unsupported package manager. Please install dependencies manually."
    exit 1
fi

log_success "System dependencies installed"

# Step 2: Python Environment Setup
log_info "Setting up Python environment..."

# Check Python version
PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
REQUIRED_VERSION="3.8"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    log_error "Python $REQUIRED_VERSION or higher required. Found: $PYTHON_VERSION"
    exit 1
fi

log_success "Python version check passed: $PYTHON_VERSION"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    log_info "Creating Python virtual environment..."
    python3 -m venv venv
    log_success "Virtual environment created"
else
    log_info "Virtual environment already exists"
fi

# Activate virtual environment
source venv/bin/activate
log_success "Virtual environment activated"

# Upgrade pip
log_info "Upgrading pip..."
pip install --upgrade pip

# Step 3: Python Dependencies
log_info "Installing Python dependencies..."
pip install -r requirements.txt
log_success "Python dependencies installed"

# Step 4: SpaCy Language Model
log_info "Installing SpaCy language model..."
python -m spacy download en_core_web_sm
log_success "SpaCy language model installed"

# Step 5: Database Services Setup
log_info "Setting up database services..."

# PostgreSQL setup
if command -v docker &> /dev/null; then
    log_info "Docker detected. Setting up PostgreSQL and Redis containers..."
    
    # Create docker-compose.dev.yml if it doesn't exist
    if [ ! -f "docker-compose.dev.yml" ]; then
        cat > docker-compose.dev.yml << 'EOF'
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: starpower_dev
      POSTGRES_USER: starpower
      POSTGRES_PASSWORD: starpower_dev_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U starpower"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:
EOF
        log_success "Created docker-compose.dev.yml"
    fi
    
    # Start services
    docker-compose -f docker-compose.dev.yml up -d
    log_success "Database services started"
    
    # Wait for services to be ready
    log_info "Waiting for database services to be ready..."
    sleep 10
    
else
    log_warning "Docker not found. Please install PostgreSQL and Redis manually or install Docker."
    log_info "PostgreSQL connection: postgresql://starpower:starpower_dev_pass@localhost:5432/starpower_dev"
    log_info "Redis connection: redis://localhost:6379/0"
fi

# Step 6: Ollama Setup (Optional)
log_info "Setting up Ollama (local LLM)..."

if command -v ollama &> /dev/null; then
    log_info "Ollama already installed"
    
    # Check if dolphin-mixtral model is available
    if ollama list | grep -q "dolphin-mixtral"; then
        log_success "dolphin-mixtral model already available"
    else
        log_info "Pulling dolphin-mixtral model (this may take a while)..."
        ollama pull dolphin-mixtral || log_warning "Failed to pull dolphin-mixtral model. Will use test mode."
    fi
else
    log_warning "Ollama not installed. Installing..."
    curl -fsSL https://ollama.ai/install.sh | sh || log_warning "Ollama installation failed. LLM will use OpenAI fallback only."
    
    if command -v ollama &> /dev/null; then
        log_info "Starting Ollama service..."
        ollama serve &
        sleep 5
        
        log_info "Pulling dolphin-mixtral model..."
        ollama pull dolphin-mixtral || log_warning "Failed to pull model. Will use test mode."
    fi
fi

# Step 7: Environment Configuration
log_info "Setting up environment configuration..."

# Create .env.example if it doesn't exist
if [ ! -f ".env.example" ]; then
    cat > .env.example << 'EOF'
# Environment Configuration
ENVIRONMENT=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=starpower_dev
DB_USER=starpower
DB_PASSWORD=starpower_dev_pass

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# LLM Configuration
OPENAI_API_KEY=your_openai_api_key_here
OLLAMA_HOST=http://localhost:11434

# News API Configuration
GNEWS_API_KEY=your_gnews_api_key_here

# Testing Configuration
TEST_DB_PATH=:memory:
USE_POSTGRES_TESTING=false

# Production Configuration (for reference)
# PROD_DB_HOST=your_prod_db_host
# PROD_DB_NAME=your_prod_db_name
# PROD_DB_USER=your_prod_db_user
# PROD_DB_PASSWORD=your_prod_db_password
# PROD_REDIS_URL=your_prod_redis_url
EOF
    log_success "Created .env.example"
fi

# Create basic .env for development if it doesn't exist
if [ ! -f ".env" ]; then
    cp .env.example .env
    log_success "Created .env from template"
    log_warning "Please update .env with your actual API keys"
fi

# Step 8: Database Initialization
log_info "Initializing database..."

# Set environment for testing
export ENVIRONMENT=development

# Run database migrations (if they exist)
if [ -f "scripts/init_database.py" ]; then
    python scripts/init_database.py || log_warning "Database initialization script failed"
else
    log_info "No database initialization script found. Database will be initialized on first run."
fi

# Step 9: Validation
log_info "Running setup validation..."

# Test Python imports
python -c "
import sys
try:
    from services.astrology_engine import VACircuitDetector
    from services.database import DatabaseConfig
    from services.llm_processor import LLMOrchestrator
    from services.news_pipeline import GNewsClient
    print('✅ All core modules import successfully')
except ImportError as e:
    print(f'❌ Import error: {e}')
    sys.exit(1)
" || {
    log_error "Core module validation failed"
    exit 1
}

# Test database connection (if services are running)
python -c "
import asyncio
import os
os.environ['ENVIRONMENT'] = 'development'

async def test_db():
    try:
        from services.database import ConnectionManager
        manager = ConnectionManager()
        # Just test configuration loading
        config = manager.get_database_config()
        print('✅ Database configuration loaded')
        return True
    except Exception as e:
        print(f'⚠️  Database test: {e}')
        return False

asyncio.run(test_db())
" || log_warning "Database connection test failed (services may not be running)"

log_success "Setup validation completed"

# Step 10: Final Instructions
echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "Next steps:"
echo "1. Activate the virtual environment: source venv/bin/activate"
echo "2. Update .env with your API keys (OpenAI, GNews)"
echo "3. Run tests to validate setup: python -m pytest tests/ -v"
echo "4. Start development server (when implemented): uvicorn api_gateway.main:app --reload"
echo ""
echo "Important files:"
echo "- .env: Environment configuration"
echo "- docker-compose.dev.yml: Development database services"
echo "- requirements.txt: Python dependencies"
echo ""
echo "For Remote Agents:"
echo "- Run: source .augment/setup.sh"
echo "- Then: python -m pytest tests/ -v"
echo "- Check: .augment/instructions.md for detailed guidance"
echo ""

log_success "Star Power setup completed successfully! 🌟"
