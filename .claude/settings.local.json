{"permissions": {"allow": ["WebFetch(domain:github.com)", "mcp__desktop-commander__get_file_info", "Bash(find:*)", "mcp__desktop-commander__create_directory", "mcp__desktop-commander__write_file", "Bash(ls:*)", "Bash(git add:*)", "Bash(git rm:*)", "Bash(git commit:*)", "mcp__desktop-commander__list_directory", "mcp__desktop-commander__read_file", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(node:*)", "<PERSON><PERSON>(mv:*)", "Bash(open http://localhost:3000/api-test)", "Bash(open http://localhost:3000)", "mcp__desktop-commander__search_files", "mcp__desktop-commander__edit_block", "mcp__desktop-commander__start_process"], "deny": []}}