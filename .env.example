# Star Power Astrology Application - Environment Configuration Template
# Copy this file to .env and update with your actual values

# =============================================================================
# CORE ENVIRONMENT SETTINGS
# =============================================================================

# Environment mode: development, testing, production
ENVIRONMENT=development

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Database (Development)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=starpower_dev
DB_USER=starpower
DB_PASSWORD=starpower_dev_pass

# PostgreSQL Database (Production) - Only set in production
# PROD_DB_HOST=your_production_db_host
# PROD_DB_PORT=5432
# PROD_DB_NAME=your_production_db_name
# PROD_DB_USER=your_production_db_user
# PROD_DB_PASSWORD=your_production_db_password
# PROD_DB_SSL_MODE=require

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis Cache (Development)
REDIS_URL=redis://localhost:6379/0

# Redis Cache (Production) - Only set in production
# PROD_REDIS_URL=redis://your_production_redis_host:6379/0
# PROD_REDIS_HOST=your_production_redis_host
# PROD_REDIS_PORT=6379
# PROD_REDIS_DB=0
# PROD_REDIS_PASSWORD=your_redis_password

# =============================================================================
# LLM CONFIGURATION
# =============================================================================

# OpenAI API (Required for LLM fallback)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# Perplexity API (Required for Isolate entity extraction)
# Get your API key from: https://www.perplexity.ai/settings/api
PERPLEXITY_API_KEY=your_perplexity_api_key_here

# Ollama Configuration (Local LLM)
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL=dolphin-mixtral

# =============================================================================
# NEWS API CONFIGURATION
# =============================================================================

# GNews API (Required for news ingestion)
# Get your API key from: https://gnews.io/
GNEWS_API_KEY=your_gnews_api_key_here

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================

# Test database (SQLite for unit tests)
TEST_DB_PATH=:memory:

# Use PostgreSQL for integration testing (optional)
USE_POSTGRES_TESTING=false

# Test environment overrides
TEST_ENVIRONMENT=testing

# =============================================================================
# SWISS EPHEMERIS CONFIGURATION
# =============================================================================

# Ephemeris data path (auto-detected if not set)
# EPHEMERIS_DATA_PATH=/usr/share/swisseph

# Force test mode (useful for CI/CD environments without ephemeris data)
# EPHEMERIS_TEST_MODE=true

# =============================================================================
# API GATEWAY CONFIGURATION (Future)
# =============================================================================

# API server settings
# API_HOST=0.0.0.0
# API_PORT=8000
# API_WORKERS=4

# CORS settings
# CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# Log format: simple, detailed, json
LOG_FORMAT=detailed

# =============================================================================
# PIPELINE CONFIGURATION
# =============================================================================

# Enable/disable pipeline stages
ENABLE_ISOLATE_EXTRACTION=true
ISOLATE_EXTRACTION_TIMEOUT=180
ISOLATE_CONFIDENCE_THRESHOLD=0.7

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Database connection pool settings
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# Redis connection settings
REDIS_MAX_CONNECTIONS=10
REDIS_RETRY_ON_TIMEOUT=true

# LLM timeout settings (seconds)
LLM_TIMEOUT=30
LLM_MAX_RETRIES=3

# Content extraction timeout (seconds)
CONTENT_EXTRACTION_TIMEOUT=30

# =============================================================================
# SECURITY CONFIGURATION (Production)
# =============================================================================

# JWT secret key (generate a secure random key for production)
# JWT_SECRET_KEY=your_jwt_secret_key_here

# API rate limiting
# RATE_LIMIT_PER_MINUTE=60

# HTTPS settings
# USE_HTTPS=true
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem

# =============================================================================
# MONITORING CONFIGURATION (Production)
# =============================================================================

# Application monitoring
# SENTRY_DSN=your_sentry_dsn_here

# Performance monitoring
# ENABLE_METRICS=true
# METRICS_PORT=9090

# Health check settings
# HEALTH_CHECK_INTERVAL=30

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================

# Enable SQL query logging in development
SQL_ECHO=true

# Enable debug mode
DEBUG=true

# Hot reload for development
RELOAD=true

# =============================================================================
# NOTES
# =============================================================================

# Required for basic functionality:
# - OPENAI_API_KEY: For LLM processing fallback
# - GNEWS_API_KEY: For news article ingestion
#
# Auto-configured by setup script:
# - Database settings (PostgreSQL + Redis via Docker)
# - Ollama settings (local LLM installation)
#
# Optional for enhanced functionality:
# - Production database settings (when deploying)
# - Monitoring and security settings (for production)
#
# The setup script (.augment/setup.sh) will create a basic .env file
# from this template with development defaults. Update the API keys
# with your actual values for full functionality.
