# Star Power - Coding Standards

This document outlines the coding standards and best practices for the Star Power project. Adhering to these standards ensures code quality, consistency, and maintainability.

## 1. Frontend Development

### 1.1. API Data Handling

**Rule**: Never trust the shape of API data.

**Description**: When handling responses from the API, always assume that optional fields may be `null` or `undefined`, even if the type definition allows it. Before performing any operations on a potentially nullable field (especially arrays or objects), you must perform a null check.

**Reasoning**: This prevents runtime errors caused by unexpected API responses. A common example is an optional array field being omitted from the API response. Attempting to call array methods like `.join()`, `.map()`, or `.filter()` on an `undefined` value will crash the application.

**Bad Example (Causes Crashes)**:
```typescript
// Assumes article.entities is always an array
const entityText = article.entities.join(' '); 
```

**Good Example (Defensive and Safe)**:
```typescript
// Checks if article.entities exists before using it
const entityText = (article.entities || []).join(' ');

// Or with a more explicit check:
let entityText = '';
if (article.entities && Array.isArray(article.entities)) {
  entityText = article.entities.join(' ');
}
```

---
