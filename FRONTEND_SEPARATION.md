# Frontend/Backend Separation Documentation

## Overview

This document describes the file ownership and synchronization system implemented for the Star Power project to manage separation between layout development (Replit/Lovable) and logic development (AI).

## File Ownership System

### 🎨 LAYOUT Files (Replit/Lovable Owned)
**Purpose**: Visual design, UI components, styling, user experience

**Directories**:
- `client/src/components/ui/` (all shadcn/ui components)
- `client/src/components/` (visual components: ArticleCard, Navigation, PlanetIcon)
- Styling configuration: `tailwind.config.ts`, `postcss.config.js`, `components.json`
- Layout utilities: `client/src/lib/utils.ts`, `client/src/index.css`
- UI hooks: `client/src/hooks/use-mobile.tsx`, `client/src/hooks/use-toast.ts`
- Layout pages: `client/src/pages/profile.tsx`, `client/src/pages/saved.tsx`, `client/src/pages/not-found.tsx`

**Allowed Changes**:
- Component styling, CSS classes, and visual design
- Animation and responsive layout adjustments
- Icon and theme modifications
- User interaction patterns (non-navigation)

**Restrictions**:
- Cannot modify API integration logic
- Cannot change data transformation functions
- Must preserve component prop interfaces
- Cannot alter navigation logic

### 🧠 LOGIC Files (AI Owned)
**Purpose**: API integration, business logic, data processing, backend services

**Directories**:
- `client/src/services/` (API clients and integration)
- `client/src/hooks/useStarPowerApi.ts` (data fetching hooks)
- `server/` (backend services and configuration)
- Logic pages: `client/src/pages/api-test.tsx`

**Allowed Changes**:
- API endpoint modifications and data fetching logic
- Request/response transformation and error handling
- Business logic and data processing functions
- Authentication and request interceptors
- Backend service configuration

**Restrictions**:
- Cannot modify UI components or styling
- Cannot change component prop interfaces used by Layout files
- Must maintain API type definitions used by SHARED files

### 🤝 SHARED Files (Coordination Required)
**Purpose**: Integration points where layout and logic intersect

**Files**:
- `client/src/types/api.ts` (type definitions and interfaces)
- `shared/schema.ts` (core data models)
- Mixed pages: `client/src/pages/home.tsx`, `client/src/pages/article.tsx`, `client/src/pages/actor-profile.tsx`, `client/src/pages/search.tsx`
- Configuration: `package.json`, `tsconfig.json`, `vite.config.ts`, `.env`

**Change Protocol**:
- Requires discussion between both teams before modification
- Interface changes must maintain backward compatibility
- Both teams must review and approve modifications
- New types should be added carefully to avoid breaking changes

## Ownership Headers

Each file has a clear ownership header at the top:

```typescript
/**
 * ==========================================
 * OWNERSHIP: LAYOUT (Replit/Lovable)
 * ==========================================
 * This file contains UI components and visual design.
 * 
 * ALLOWED CHANGES:
 * - Component styling, CSS classes, and visual design
 * - Animation and responsive layout adjustments
 * 
 * RESTRICTIONS:
 * - Do NOT modify component prop interfaces
 * - Must preserve data property usage patterns
 * 
 * CONTACT: Layout team for styling/UI changes
 * ==========================================
 */
```

## Synchronization Scripts

### sync-frontend.sh
**Purpose**: Pull latest layout changes from star-power-fe repository

**Usage**:
```bash
./sync-frontend.sh
```

**What it does**:
1. Creates backup of current state
2. Clones star-power-fe repository
3. Selectively copies LAYOUT files based on ownership headers
4. Preserves LOGIC files in the main repository
5. Reports conflicts in SHARED files
6. Verifies sync integrity

### push-logic-changes.sh
**Purpose**: Push API integration and business logic changes to both repositories

**Usage**:
```bash
./push-logic-changes.sh "commit message"
```

**What it does**:
1. Commits changes to main star-power repository
2. Clones star-power-fe repository
3. Copies LOGIC and SHARED files to frontend repository
4. Commits and pushes changes to star-power-fe
5. Verifies push integrity

## Integration Points

### Critical Contracts
These interfaces must be maintained for proper integration:

**Component Props**:
```typescript
interface ArticleCardProps {
  article: ArticleWithDetails;  // SHARED type
}
```

**Hook Return Types**:
```typescript
interface UseArticlesReturn {
  articles: ExtendedArticle[];
  loading: boolean;
  error: string | null;
  // ... other properties
}
```

**API Type Definitions**:
```typescript
interface ExtendedArticle extends Article {
  // Layout needs: title, content, publishedAt
  // Logic adds: url, source, celebrity_mentions
}
```

### Data Flow Boundaries

**Layout → Logic**:
- User interactions (clicks, form submissions)
- Navigation requests
- Filter/search parameters

**Logic → Layout**:
- Fetched data (articles, celebrities, analyses)
- Loading states and error messages
- Processing results

## Conflict Resolution

### Ownership-Based Resolution
1. **Clear Ownership**: Automatic sync based on file ownership headers
2. **SHARED Files**: Manual review required, both teams notified
3. **Missing Headers**: Default rules based on file path patterns

### Manual Review Process
For SHARED files requiring coordination:
1. Create GitHub issue with both teams tagged
2. Discuss interface changes in advance
3. Implement with backward compatibility
4. Coordinate deployment timing

## Testing Strategy

### Layout Testing
- Visual regression tests
- Component rendering validation
- User interaction flow testing
- Responsive design verification

### Logic Testing
- API integration tests
- Data transformation validation
- Error handling verification
- Performance and reliability tests

### Integration Testing
- End-to-end data flow from API to UI
- Component prop interface validation
- Hook return type verification
- Navigation and state management

## Webhook Configuration

The `webhook-config.json` file contains configuration for automated synchronization:

### Triggers
- **star-power-fe → main**: Layout file changes trigger sync to main repo
- **main → star-power-fe**: Logic file changes trigger sync to frontend repo

### Safety Features
- Ownership header verification
- File size and type restrictions
- Rate limiting and backup creation
- Conflict detection and notification

## Development Workflow

### For Layout Changes (Replit/Lovable)
1. Work directly in star-power-fe repository
2. Focus on UI components, styling, and visual design
3. Changes automatically sync to main repo via webhook
4. Test integration with existing data flows

### For Logic Changes (AI)
1. Work in main star-power repository
2. Modify API integration and business logic
3. Run `./push-logic-changes.sh` to sync changes
4. Test integration without modifying component structure

### For SHARED Changes
1. Create GitHub issue for coordination
2. Discuss changes with both teams
3. Implement with backward compatibility
4. Use `./sync-frontend.sh` after layout updates
5. Use `./push-logic-changes.sh` after logic updates

## Troubleshooting

### Common Issues

**Sync Conflicts**:
- Check ownership headers in conflicting files
- Use backup files in `backups/` directory if needed
- Review SHARED files manually for conflicts

**Missing Files**:
- Run `./sync-frontend.sh` to ensure latest layout files
- Check that ownership headers are correct
- Verify file paths match ownership rules

**Type Errors**:
- Ensure SHARED type definitions are synchronized
- Check that component props match expected interfaces
- Verify hook return types haven't changed

### Recovery Procedures

**Restore from Backup**:
```bash
# Backups are created automatically in backups/ directory
cp -r backups/frontend-YYYYMMDD-HHMMSS/frontend-integration ./
```

**Manual Sync**:
```bash
# If scripts fail, manually clone and copy files
git clone https://github.com/VerbalClaymore/star-power-fe.git temp-manual-sync
# Copy specific files based on ownership rules
```

**Reset to Known Good State**:
```bash
# Reset to last known working commit
git checkout HEAD~1 frontend-integration/
./sync-frontend.sh
```

## Future Enhancements

### Planned Improvements
1. **Real-time Sync**: WebSocket-based real-time synchronization
2. **Visual Diff Tool**: Compare layout changes before sync
3. **Automated Testing**: Run tests before allowing sync
4. **Conflict Resolution UI**: Web interface for resolving SHARED file conflicts
5. **Performance Monitoring**: Track sync performance and reliability

### Integration Opportunities
1. **IDE Extensions**: VSCode extension for ownership management
2. **CI/CD Integration**: Automated testing in GitHub Actions
3. **Deployment Coordination**: Coordinated deployment of both repositories
4. **Monitoring Dashboard**: Real-time view of sync status and health

---

This separation system enables efficient parallel development while maintaining code quality and preventing conflicts through clear ownership boundaries and automated synchronization.