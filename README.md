# Star Power - Clean Architecture Implementation

## Overview

Star Power is an advanced astrology application featuring the crown jewel Vibrational Astrology (VA) circuit detection algorithm. This clean architecture implementation preserves the 694-line VA algorithm exactly while building a scalable service-oriented architecture around it.

## Crown Jewel Technology

**CRITICAL**: The VA algorithm in `services/astrology_engine/va_circuit_detector.py` is the crown jewel technology and must be preserved exactly. Any modifications require explicit authorization.

- **Algorithm Hash**: `81a33f786bd7c84e055570fc601b8a2a08fdccb9824dc23a92785d62471c3a59`
- **Line Count**: 694 lines (including comprehensive error handling)
- **Performance Target**: <2 seconds per analysis
- **Test Coverage**: 45+ unit tests

## Architecture

### Service Structure

```
services/
├── astrology_engine/     # Crown jewel VA algorithm + astrological calculations
├── news_pipeline/        # News article processing and celebrity identification
├── llm_processor/        # LLM integration for content enhancement
├── database/            # Data persistence and caching services
└── api_gateway/         # API routing and service coordination
```

### Key Features

- **Multi-harmonic VA Analysis**: 7 base harmonics (1, 5, 7, 8, 9, 11, 13)
- **Complete Graph Theory**: Sophisticated circuit detection algorithm
- **Performance Optimized**: <2s VA circuit analysis, 90s end-to-end processing
- **Service Architecture**: Modular design with clear boundaries
- **Comprehensive Testing**: Unit, integration, and performance tests

## Development Guidelines

### Crown Jewel Protection

1. **Never modify** `services/astrology_engine/va_circuit_detector.py` without authorization
2. **Validate integrity** using the stored algorithm hash
3. **Preserve test coverage** - all 45+ VA tests must pass
4. **Monitor performance** - maintain <2s analysis target

### Development Environment

**Docker is the standard for all development services**. Use Docker containers for:
- PostgreSQL database (port 5432)
- Redis cache (port 6379)
- Optional admin tools (pgAdmin, Redis Commander)

```bash
# Start development services
docker-compose -f docker-compose.dev.yml up -d postgres redis

# Start with admin tools
docker-compose -f docker-compose.dev.yml --profile admin up -d

# Check service health
docker-compose -f docker-compose.dev.yml ps
```

### Module Constraints

- All modules must be <300 lines (except crown jewel VA algorithm)
- Clear service boundaries and interfaces
- Comprehensive error handling and logging
- Performance monitoring and optimization

## 📚 Documentation

For detailed troubleshooting, debugging procedures, and operational guides, see the [Documentation Index](docs/README.md).

**Quick Links**:
- **[Frontend Debugging Guide](docs/frontend_debugging_log.md)** - Connectivity issues, blank page fixes, race condition solutions
- **[Crown Jewel Protection](docs/crown_jewel_protection.md)** - VA algorithm integrity and security procedures  
- **[Integration Verification](docs/frontend_integration_verification.md)** - API testing and validation procedures

## Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Run tests
pytest tests/ -v

# Validate crown jewel integrity
python -c "from services.astrology_engine import VACircuitDetector; VACircuitDetector()"
```

## Frontend Development & Debugging

### Quick Verification Scripts

For fast iteration and automated testing without manual browser interaction:

```bash
# Comprehensive frontend verification (recommended)
./scripts/verify_frontend.sh

# Individual component testing
curl -s http://localhost:8000/health/               # API health check
curl -s http://localhost:8000/api/articles/         # Articles endpoint
curl -s http://localhost:3000 | grep -i error       # Frontend error detection (dev server)
```

### Development Servers

```bash
# Start API Gateway (backend)
python -m services.api_gateway.main

# Start Frontend (in frontend-integration/ directory)
cd frontend-integration && npm run dev
```

### Debugging Checklist

When encountering frontend issues, run through this systematic checklist:

1. **Verify servers are running**:
   - API Gateway: `curl http://localhost:8000/health/`
   - Frontend: `curl http://localhost:5173`

2. **Check API endpoints**:
   - Articles: `curl http://localhost:8000/api/articles/`
   - Verify response structure matches frontend expectations

3. **Run automated verification**:
   - `./scripts/verify_frontend.sh` - comprehensive testing
   - Check exit code: 0 = success, 1 = failure

4. **Common issues**:
   - URL construction mismatches (base URL vs endpoint paths)
   - Data structure mismatches (API response vs frontend expectations)
   - Environment variable loading (`.env` file presence)

See `docs/frontend_debugging_log.md` for detailed troubleshooting history and `docs/frontend_url_fix_guide.md` for URL construction standardization.

## Testing

### Test Categories

- **Unit Tests**: `tests/unit/` - Individual component testing
- **Integration Tests**: `tests/integration/` - Service interaction testing
- **Performance Tests**: `tests/performance/` - Load and timing validation

### Crown Jewel Validation

```bash
# VA algorithm integrity check
python -m pytest tests/unit/test_va_circuit_detector.py -v

# Performance validation
python -m pytest tests/performance/test_va_performance.py -v
```

## Quality Gates

- [ ] Crown jewel algorithm preserved with 100% test coverage
- [ ] All modules <300 lines with clear service boundaries
- [ ] End-to-end pipeline processing within 90-second requirement
- [ ] Celebrity identification >90% accuracy
- [ ] Test coverage >85% with integration testing

## License

Proprietary - Crown Jewel VA Algorithm Protected

## Contact

For questions about the crown jewel VA algorithm or architecture decisions, please contact the development team.