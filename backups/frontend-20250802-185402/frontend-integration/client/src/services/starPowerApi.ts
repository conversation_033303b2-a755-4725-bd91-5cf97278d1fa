/**
 * ==========================================
 * OWNERSHIP: LOGIC (AI)
 * ==========================================
 * This file contains API integration and business logic.
 * 
 * ALLOWED CHANGES:
 * - API endpoint modifications and data fetching logic
 * - Request/response transformation and error handling
 * - Business logic and data processing functions
 * - Authentication and request interceptors
 * 
 * RESTRICTIONS:
 * - Do NOT modify UI components or styling
 * - Do NOT change component prop interfaces used by Layout files
 * - Must maintain API type definitions used by SHARED files
 * 
 * INTEGRATION POINTS:
 * - API types exported to frontend components (SHARED contract)
 * - transformers.* functions used by UI (maintain interface)
 * 
 * CONTACT: AI team for API/business logic changes
 * ==========================================
 * 
 * Star Power API Client
 * 
 * Integration layer for the new Star Power API Gateway.
 * Provides typed API methods for all Star Power services.
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

// Create axios instance with base configuration
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds for astrology analysis
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// API Response Types (matching API Gateway Pydantic models)
export interface ApiArticle {
  id: string;
  title: string;
  content: string;
  url: string;
  published_at: string;
  source: string;
  entities: string[];
  celebrity_mentions: string[];
  extraction_confidence: number;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  language?: string;
  word_count?: number;
  created_at: string;
  updated_at: string;
}

export interface ApiCelebrity {
  id: string;
  name: string;
  birth_date?: string;
  birth_time?: string;
  birth_location?: string;
  confidence_score: number;
  data_sources: string[];
  verified: boolean;
  professional_name?: string;
  aliases: string[];
  enhanced: boolean;
  created_at: string;
  updated_at: string;
}

export interface ApiAstrologyAnalysis {
  celebrity_id: string;
  analysis_date: string;
  analysis_results: {
    planetary_positions: Record<string, any>;
    aspects: any[];
    houses: Record<string, any>;
  };
  va_circuits?: any[];
  confidence_score: number;
  processing_time_ms: number;
}

export interface ApiPipelineJob {
  job_id: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  result?: {
    articles_processed: number;
    celebrities_identified: number;
    analyses_completed: number;
  };
  error_message?: string;
  created_at: string;
  updated_at: string;
}

export interface ApiListResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface ApiHealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  services: Record<string, {
    status: string;
    response_time_ms?: number;
    error?: string;
  }>;
}

// API Methods
export const starPowerApi = {
  // Health Check
  health: {
    check: (): Promise<AxiosResponse<ApiHealthStatus>> =>
      apiClient.get('/health'),
    
    detailed: (): Promise<AxiosResponse<ApiHealthStatus>> =>
      apiClient.get('/health/detailed'),
  },

  // Articles
  articles: {
    list: (params?: {
      page?: number;
      limit?: number;
      status?: string;
      source?: string;
      search?: string;
    }): Promise<AxiosResponse<ApiListResponse<ApiArticle>>> =>
      apiClient.get('/articles', { params }),

    getById: (id: string): Promise<AxiosResponse<ApiArticle>> =>
      apiClient.get(`/articles/${id}`),

    process: (data: {
      url?: string;
      content?: string;
      title?: string;
      source?: string;
    }): Promise<AxiosResponse<ApiArticle>> =>
      apiClient.post('/articles/process', data),
  },

  // Celebrities
  celebrities: {
    list: (params?: {
      page?: number;
      limit?: number;
      search?: string;
      verified?: boolean;
    }): Promise<AxiosResponse<ApiListResponse<ApiCelebrity>>> =>
      apiClient.get('/celebrities', { params }),

    getById: (id: string): Promise<AxiosResponse<ApiCelebrity>> =>
      apiClient.get(`/celebrities/${id}`),

    identify: (data: {
      text: string;
      context?: string;
      confidence_threshold?: number;
    }): Promise<AxiosResponse<{
      celebrities: Array<{
        name: string;
        confidence: number;
        context: string;
        birth_date?: string;
        birth_location?: string;
      }>;
      total_matches: number;
      processing_time_ms: number;
    }>> =>
      apiClient.post('/celebrities/identify', data),

    search: (query: string): Promise<AxiosResponse<ApiListResponse<ApiCelebrity>>> =>
      apiClient.get('/celebrities', { params: { search: query } }),
  },

  // Astrology
  astrology: {
    analyze: (data: {
      celebrity_id: string;
      analysis_date?: string;
      include_va_circuits?: boolean;
      include_transits?: boolean;
    }): Promise<AxiosResponse<ApiAstrologyAnalysis>> =>
      apiClient.post('/astrology/analyze', data),

    getChart: (celebrityId: string): Promise<AxiosResponse<{
      celebrity_id: string;
      chart_data: any;
      planetary_positions: Record<string, any>;
      houses: Record<string, any>;
      aspects: any[];
    }>> =>
      apiClient.get(`/astrology/charts/${celebrityId}`),
  },

  // Pipeline
  pipeline: {
    process: (data: {
      query?: string;
      max_articles?: number;
      article_url?: string;
    }): Promise<AxiosResponse<{
      message: string;
      job_ids: string[];
      total_jobs: number;
      query?: string;
      max_articles?: number;
    }>> =>
      apiClient.post('/pipeline/process', data),

    getStatus: (jobId: string): Promise<AxiosResponse<ApiPipelineJob>> =>
      apiClient.get(`/pipeline/status/${jobId}`),

    cancel: (jobId: string): Promise<AxiosResponse<{ message: string }>> =>
      apiClient.post(`/pipeline/cancel/${jobId}`),
  },
};

// Utility functions for data transformation
export const transformers = {
  // Convert API article to frontend article format
  articleToFrontend: (apiArticle: ApiArticle) => ({
    id: parseInt(apiArticle.id),
    title: apiArticle.title,
    summary: apiArticle.content.substring(0, 200) + '...',
    content: apiArticle.content,
    categoryId: 1, // Default category, could be enhanced
    publishedAt: new Date(apiArticle.published_at),
    astroAnalysis: 'Analysis pending...', // Placeholder
    astroGlyphs: [], // Placeholder
    hashtags: apiArticle.entities,
    actorIds: [], // Will be populated from celebrity mentions
    likeCount: 0,
    shareCount: 0,
    bookmarkCount: 0,
    isCelebrity: apiArticle.celebrity_mentions.length > 0,
  }),

  // Convert API celebrity to frontend actor format
  celebrityToActor: (apiCelebrity: ApiCelebrity) => ({
    id: parseInt(apiCelebrity.id),
    name: apiCelebrity.name,
    slug: apiCelebrity.name.toLowerCase().replace(/\s+/g, '-'),
    category: 'celebrity',
    sunSign: null, // Will be populated from astrology analysis
    moonSign: null,
    risingSign: null,
    profileImage: null,
  }),
};

export default starPowerApi;
