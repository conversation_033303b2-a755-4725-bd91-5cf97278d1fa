/**
 * ==========================================
 * OWNERSHIP: LOGIC (AI)
 * ==========================================
 * This file contains API integration hooks and business logic.
 * 
 * ALLOWED CHANGES:
 * - API hook implementation and data fetching logic
 * - React Query configuration and caching strategies
 * - Error handling and loading state management
 * - Business logic for data transformation
 * 
 * RESTRICTIONS:
 * - Do NOT modify hook return type interfaces (used by Layout)
 * - Do NOT change component usage patterns
 * - Must maintain backward compatibility for hook signatures
 * 
 * INTEGRATION POINTS:
 * - Hook return types defined in types/api.ts (SHARED)
 * - Used by page components and UI components (LAYOUT)
 * 
 * CONTACT: AI team for API integration changes
 * ==========================================
 * 
 * Star Power API Hooks
 * 
 * Custom React hooks for integrating with the Star Power API Gateway.
 * Provides easy-to-use hooks for all API functionality.
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState, useCallback } from 'react';
import { starPowerApi, transformers } from '@/services/starPowerApi';
import type {
  ExtendedArticle,
  ExtendedActor,
  AstrologyAnalysis,
  PipelineJob,
  ArticleFilters,
  CelebrityFilters,
  UseArticlesReturn,
  UseCelebritiesReturn,
  UseAstrologyReturn,
  UsePipelineReturn,
} from '@/types/api';

// Articles Hook
export function useArticles(initialFilters: ArticleFilters = {}): UseArticlesReturn {
  const [filters, setFilters] = useState<ArticleFilters>(initialFilters);
  const queryClient = useQueryClient();

  const {
    data,
    isLoading: loading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['articles', filters],
    queryFn: async () => {
      const response = await starPowerApi.articles.list(filters);
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const articles = data?.items.map(transformers.articleToFrontend) || [];
  const total = data?.total || 0;
  const hasNext = data?.has_next || false;
  const hasPrevious = data?.has_previous || false;

  const loadMore = useCallback(() => {
    if (hasNext) {
      setFilters(prev => ({
        ...prev,
        page: (prev.page || 1) + 1,
      }));
    }
  }, [hasNext]);

  const refresh = useCallback(() => {
    refetch();
  }, [refetch]);

  const filter = useCallback((newFilters: Partial<ArticleFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1, // Reset to first page when filtering
    }));
  }, []);

  return {
    articles,
    loading,
    error: error?.message || null,
    total,
    hasNext,
    hasPrevious,
    loadMore,
    refresh,
    filter,
  };
}

// Single Article Hook
export function useArticle(id: string) {
  return useQuery({
    queryKey: ['article', id],
    queryFn: async () => {
      const response = await starPowerApi.articles.getById(id);
      return transformers.articleToFrontend(response.data);
    },
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Celebrities Hook
export function useCelebrities(initialFilters: CelebrityFilters = {}): UseCelebritiesReturn {
  const [filters, setFilters] = useState<CelebrityFilters>(initialFilters);
  const queryClient = useQueryClient();

  const {
    data,
    isLoading: loading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['celebrities', filters],
    queryFn: async () => {
      const response = await starPowerApi.celebrities.list(filters);
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const celebrities = data?.items.map(transformers.celebrityToActor) || [];
  const total = data?.total || 0;
  const hasNext = data?.has_next || false;
  const hasPrevious = data?.has_previous || false;

  const identifyMutation = useMutation({
    mutationFn: async (text: string) => {
      const response = await starPowerApi.celebrities.identify({ text });
      return response.data.celebrities;
    },
  });

  const loadMore = useCallback(() => {
    if (hasNext) {
      setFilters(prev => ({
        ...prev,
        page: (prev.page || 1) + 1,
      }));
    }
  }, [hasNext]);

  const refresh = useCallback(() => {
    refetch();
  }, [refetch]);

  const search = useCallback((query: string) => {
    setFilters(prev => ({
      ...prev,
      search: query,
      page: 1,
    }));
  }, []);

  const identify = useCallback(async (text: string) => {
    return identifyMutation.mutateAsync(text);
  }, [identifyMutation]);

  return {
    celebrities,
    loading,
    error: error?.message || null,
    total,
    hasNext,
    hasPrevious,
    loadMore,
    refresh,
    search,
    identify,
  };
}

// Single Celebrity Hook
export function useCelebrity(id: string) {
  return useQuery({
    queryKey: ['celebrity', id],
    queryFn: async () => {
      const response = await starPowerApi.celebrities.getById(id);
      return transformers.celebrityToActor(response.data);
    },
    enabled: !!id,
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
}

// Astrology Hook
export function useAstrology(): UseAstrologyReturn {
  const [currentAnalysis, setCurrentAnalysis] = useState<AstrologyAnalysis>();
  const queryClient = useQueryClient();

  const analyzeMutation = useMutation({
    mutationFn: async (celebrityId: string) => {
      const response = await starPowerApi.astrology.analyze({ celebrity_id: celebrityId });
      return response.data;
    },
    onSuccess: (data) => {
      setCurrentAnalysis(data);
      // Cache the analysis
      queryClient.setQueryData(['astrology', data.celebrity_id], data);
    },
  });

  const chartMutation = useMutation({
    mutationFn: async (celebrityId: string) => {
      const response = await starPowerApi.astrology.getChart(celebrityId);
      return response.data;
    },
  });

  const analyze = useCallback(async (celebrityId: string) => {
    return analyzeMutation.mutateAsync(celebrityId);
  }, [analyzeMutation]);

  const getChart = useCallback(async (celebrityId: string) => {
    return chartMutation.mutateAsync(celebrityId);
  }, [chartMutation]);

  return {
    analyze,
    getChart,
    loading: analyzeMutation.isPending || chartMutation.isPending,
    error: analyzeMutation.error?.message || chartMutation.error?.message || null,
    currentAnalysis,
  };
}

// Pipeline Hook
export function usePipeline(): UsePipelineReturn {
  const [jobs, setJobs] = useState<Record<string, PipelineJob>>({});
  const [activeJobs, setActiveJobs] = useState<string[]>([]);

  const processArticleMutation = useMutation({
    mutationFn: async (url: string) => {
      const response = await starPowerApi.pipeline.process({ article_url: url });
      return response.data.job_ids;
    },
    onSuccess: (jobIds) => {
      setActiveJobs(prev => [...prev, ...jobIds]);
    },
  });

  const processNewsMutation = useMutation({
    mutationFn: async ({ query, maxArticles }: { query: string; maxArticles?: number }) => {
      const response = await starPowerApi.pipeline.process({ query, max_articles: maxArticles });
      return response.data.job_ids;
    },
    onSuccess: (jobIds) => {
      setActiveJobs(prev => [...prev, ...jobIds]);
    },
  });

  const statusMutation = useMutation({
    mutationFn: async (jobId: string) => {
      const response = await starPowerApi.pipeline.getStatus(jobId);
      return response.data;
    },
    onSuccess: (job) => {
      setJobs(prev => ({ ...prev, [job.job_id]: job }));
      if (job.status === 'completed' || job.status === 'failed') {
        setActiveJobs(prev => prev.filter(id => id !== job.job_id));
      }
    },
  });

  const cancelMutation = useMutation({
    mutationFn: async (jobId: string) => {
      await starPowerApi.pipeline.cancel(jobId);
    },
    onSuccess: (_, jobId) => {
      setActiveJobs(prev => prev.filter(id => id !== jobId));
    },
  });

  const processArticle = useCallback(async (url: string) => {
    return processArticleMutation.mutateAsync(url);
  }, [processArticleMutation]);

  const processNews = useCallback(async (query: string, maxArticles?: number) => {
    return processNewsMutation.mutateAsync({ query, maxArticles });
  }, [processNewsMutation]);

  const getJobStatus = useCallback(async (jobId: string) => {
    return statusMutation.mutateAsync(jobId);
  }, [statusMutation]);

  const cancelJob = useCallback(async (jobId: string) => {
    return cancelMutation.mutateAsync(jobId);
  }, [cancelMutation]);

  return {
    processArticle,
    processNews,
    getJobStatus,
    cancelJob,
    jobs,
    activeJobs,
    loading: processArticleMutation.isPending || processNewsMutation.isPending,
    error: processArticleMutation.error?.message || processNewsMutation.error?.message || null,
  };
}

// Health Check Hook
export function useHealth() {
  return useQuery({
    queryKey: ['health'],
    queryFn: async () => {
      const response = await starPowerApi.health.check();
      return response.data;
    },
    refetchInterval: 30000, // Check every 30 seconds
    staleTime: 10000, // 10 seconds
  });
}

// Article Processing Hook
export function useArticleProcessing() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { url?: string; content?: string; title?: string; source?: string }) => {
      const response = await starPowerApi.articles.process(data);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate articles list to show new article
      queryClient.invalidateQueries({ queryKey: ['articles'] });
    },
  });
}
