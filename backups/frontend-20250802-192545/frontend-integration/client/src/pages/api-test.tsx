/**
 * API Test Page
 * 
 * Test page for demonstrating Star Power API Gateway integration.
 * Shows health status, article processing, celebrity identification, and astrology analysis.
 */

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  useHealth, 
  useArticleProcessing, 
  useCelebrities, 
  useAstrology,
  usePipeline 
} from '@/hooks/useStarPowerApi';

export default function ApiTest() {
  const [articleUrl, setArticleUrl] = useState('');
  const [celebrityText, setCelebrityText] = useState('');
  const [selectedCelebrityId, setSelectedCelebrityId] = useState('');

  // API Hooks
  const { data: health, isLoading: healthLoading } = useHealth();
  const articleProcessing = useArticleProcessing();
  const { identify } = useCelebrities();
  const { analyze, currentAnalysis } = useAstrology();
  const { processArticle, processNews, jobs, activeJobs } = usePipeline();

  const [identificationResults, setIdentificationResults] = useState<any[]>([]);
  const [processingResults, setProcessingResults] = useState<any>(null);

  const handleProcessArticle = async () => {
    if (!articleUrl.trim()) return;
    
    try {
      const result = await articleProcessing.mutateAsync({ url: articleUrl });
      setProcessingResults(result);
    } catch (error) {
      console.error('Article processing failed:', error);
    }
  };

  const handleIdentifyCelebrity = async () => {
    if (!celebrityText.trim()) return;
    
    try {
      const results = await identify(celebrityText);
      setIdentificationResults(results);
    } catch (error) {
      console.error('Celebrity identification failed:', error);
    }
  };

  const handleAnalyzeAstrology = async () => {
    if (!selectedCelebrityId) return;
    
    try {
      await analyze(selectedCelebrityId);
    } catch (error) {
      console.error('Astrology analysis failed:', error);
    }
  };

  const handleProcessNews = async () => {
    try {
      const jobIds = await processNews('celebrity news', 5);
      console.log('Started pipeline jobs:', jobIds);
    } catch (error) {
      console.error('News processing failed:', error);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold">Star Power API Test</h1>
      
      {/* Health Status */}
      <Card>
        <CardHeader>
          <CardTitle>API Health Status</CardTitle>
        </CardHeader>
        <CardContent>
          {healthLoading ? (
            <Skeleton className="h-4 w-32" />
          ) : health ? (
            <div className="space-y-2">
              <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                health.status === 'healthy' ? 'bg-green-100 text-green-800' :
                health.status === 'degraded' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {health.status.toUpperCase()}
              </div>
              <div className="text-sm text-gray-600">
                Last checked: {new Date(health.timestamp).toLocaleTimeString()}
              </div>
              <div className="grid grid-cols-2 gap-2 mt-4">
                {Object.entries(health.services).map(([service, status]) => (
                  <div key={service} className="text-xs">
                    <span className="font-medium">{service}:</span>{' '}
                    <span className={status.status === 'healthy' ? 'text-green-600' : 'text-red-600'}>
                      {status.status}
                    </span>
                    {status.response_time_ms && (
                      <span className="text-gray-500"> ({status.response_time_ms}ms)</span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-red-600">API not available</div>
          )}
        </CardContent>
      </Card>

      {/* Article Processing */}
      <Card>
        <CardHeader>
          <CardTitle>Article Processing</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-2">
            <Input
              placeholder="Enter article URL..."
              value={articleUrl}
              onChange={(e) => setArticleUrl(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={handleProcessArticle}
              disabled={articleProcessing.isPending || !articleUrl.trim()}
            >
              {articleProcessing.isPending ? 'Processing...' : 'Process Article'}
            </Button>
          </div>
          
          {processingResults && (
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-medium text-green-800">Article Processed Successfully</h4>
              <p className="text-sm text-green-600 mt-1">
                Title: {processingResults.title}
              </p>
              <p className="text-sm text-green-600">
                Status: {processingResults.processing_status}
              </p>
            </div>
          )}
          
          {articleProcessing.error && (
            <div className="bg-red-50 p-4 rounded-lg">
              <p className="text-red-600 text-sm">{articleProcessing.error.message}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Celebrity Identification */}
      <Card>
        <CardHeader>
          <CardTitle>Celebrity Identification</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-2">
            <Input
              placeholder="Enter text mentioning celebrities..."
              value={celebrityText}
              onChange={(e) => setCelebrityText(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={handleIdentifyCelebrity}
              disabled={!celebrityText.trim()}
            >
              Identify
            </Button>
          </div>
          
          {identificationResults.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium">Identified Celebrities:</h4>
              {identificationResults.map((result, index) => (
                <div key={index} className="bg-blue-50 p-3 rounded-lg">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-medium">{result.name}</p>
                      <p className="text-sm text-gray-600">
                        Confidence: {(result.confidence * 100).toFixed(1)}%
                      </p>
                      {result.birth_date && (
                        <p className="text-sm text-gray-600">
                          Born: {result.birth_date}
                        </p>
                      )}
                    </div>
                    <Button
                      size="sm"
                      onClick={() => setSelectedCelebrityId(result.id || '1')}
                    >
                      Select for Analysis
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Astrology Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Astrology Analysis</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-2">
            <Input
              placeholder="Celebrity ID for analysis..."
              value={selectedCelebrityId}
              onChange={(e) => setSelectedCelebrityId(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={handleAnalyzeAstrology}
              disabled={!selectedCelebrityId.trim()}
            >
              Analyze
            </Button>
          </div>
          
          {currentAnalysis && (
            <div className="bg-purple-50 p-4 rounded-lg">
              <h4 className="font-medium text-purple-800">Analysis Complete</h4>
              <p className="text-sm text-purple-600 mt-1">
                Celebrity ID: {currentAnalysis.celebrity_id}
              </p>
              <p className="text-sm text-purple-600">
                Confidence: {(currentAnalysis.confidence_score * 100).toFixed(1)}%
              </p>
              <p className="text-sm text-purple-600">
                Processing time: {currentAnalysis.processing_time_ms}ms
              </p>
              {currentAnalysis.va_circuits && currentAnalysis.va_circuits.length > 0 && (
                <p className="text-sm text-purple-600">
                  VA Circuits detected: {currentAnalysis.va_circuits.length}
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pipeline Processing */}
      <Card>
        <CardHeader>
          <CardTitle>Pipeline Processing</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={handleProcessNews}>
            Process Celebrity News (5 articles)
          </Button>
          
          {activeJobs.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium">Active Jobs:</h4>
              {activeJobs.map(jobId => (
                <div key={jobId} className="bg-yellow-50 p-2 rounded text-sm">
                  Job {jobId}: Processing...
                </div>
              ))}
            </div>
          )}
          
          {Object.keys(jobs).length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium">Completed Jobs:</h4>
              {Object.values(jobs).map(job => (
                <div key={job.job_id} className="bg-gray-50 p-2 rounded text-sm">
                  <div>Job {job.job_id}: {job.status}</div>
                  {job.result && (
                    <div className="text-xs text-gray-600 mt-1">
                      Articles: {job.result.articles_processed}, 
                      Celebrities: {job.result.celebrities_identified}, 
                      Analyses: {job.result.analyses_completed}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
