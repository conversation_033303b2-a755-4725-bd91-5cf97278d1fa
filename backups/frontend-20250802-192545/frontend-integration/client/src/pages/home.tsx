/**
 * ==========================================
 * OWNERSHIP: SHARED (Coordination Required)
 * ==========================================
 * This file integrates Layout components with Logic data flows.
 * 
 * CHANGE PROTOCOL:
 * - Layout team: Can modify UI structure, styling, component composition
 * - Logic team: Can modify data fetching, business logic, API integration
 * - Both teams: Must coordinate on data flow and component interfaces
 * 
 * LAYOUT RESPONSIBILITIES:
 * - Component rendering and visual design
 * - User interaction patterns and navigation
 * - Styling and responsive layout
 * 
 * LOGIC RESPONSIBILITIES:
 * - Data fetching with useArticles, useHealth hooks
 * - State management and error handling
 * - Business logic for filtering and data transformation
 * 
 * CRITICAL INTEGRATION POINTS:
 * - Hook interfaces (useArticles, useHealth) - maintain return types
 * - Component props (ArticleCard) - preserve prop structure
 * - Type imports from shared schema - coordinate changes
 * 
 * CONTACT: Both teams required for modifications
 * ==========================================
 */

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import TopNavigation from "@/components/TopNavigation";
import ArticleCard from "@/components/ArticleCard";
import { Skeleton } from "@/components/ui/skeleton";
import { useArticles, useHealth } from "@/hooks/useStarPowerApi";
import type { Category, ArticleWithDetails } from "@shared/schema";

export default function Home() {
  const [activeCategory, setActiveCategory] = useState("top");

  // Keep existing categories query for now (from mock server)
  const { data: categories, isLoading: categoriesLoading } = useQuery<Category[]>({
    queryKey: ["/api/categories"],
  });

  // Use new Star Power API for articles
  const {
    articles: starPowerArticles,
    loading: articlesLoading,
    error: articlesError,
    filter: filterArticles
  } = useArticles({
    page: 1,
    limit: 20,
    ...(activeCategory !== "top" && { category: activeCategory })
  });

  // Health check for API status
  const { data: healthStatus } = useHealth();

  // Transform Star Power articles to match existing interface
  const articles: ArticleWithDetails[] = starPowerArticles.map(article => ({
    ...article,
    category: categories?.find(c => c.slug === activeCategory) || {
      id: 1,
      name: "General",
      slug: "general",
      color: "#6366f1",
      icon: "⭐"
    },
    actors: [], // Will be populated from celebrity mentions
  }));

  if (categoriesLoading) {
    return (
      <div className="mobile-container">
        <div className="bg-primary text-white">
          <div className="status-bar">
            <span className="font-medium">9:41</span>
            <div className="flex items-center space-x-1">
              <span>📶</span>
              <span>📶</span>
              <span>🔋</span>
            </div>
          </div>
          <div className="app-header">
            <h1 className="text-xl font-bold">Star Power</h1>
            <p className="text-xs text-purple-200">Astrological News Reader</p>
            {healthStatus && (
              <div className="text-xs mt-1">
                API: <span className={`${healthStatus.status === 'healthy' ? 'text-green-300' : 'text-yellow-300'}`}>
                  {healthStatus.status}
                </span>
              </div>
            )}
          </div>
          <div className="px-4 pb-4">
            <Skeleton className="h-8 w-full bg-white/20" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <TopNavigation
        categories={categories || []}
        activeCategory={activeCategory}
        onCategoryChange={setActiveCategory}
      />
      
      <div className="flex-1 pb-20 bg-[hsl(var(--surface-variant))] min-h-screen">
        {articlesError && (
          <div className="p-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h3 className="text-red-800 font-medium">API Connection Error</h3>
              <p className="text-red-600 text-sm mt-1">
                {articlesError}
              </p>
              <p className="text-red-600 text-xs mt-2">
                Falling back to local data. Check if the Star Power API Gateway is running on port 8000.
              </p>
            </div>
          </div>
        )}

        {articlesLoading ? (
          <div className="space-y-4 p-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg p-4 border-l-4 border-gray-200">
                <Skeleton className="h-4 w-20 mb-2" />
                <Skeleton className="h-6 w-full mb-2" />
                <Skeleton className="h-4 w-3/4 mb-3" />
                <div className="flex space-x-2">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-16" />
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-4 p-4">
            {articles?.map((article) => (
              <ArticleCard key={article.id} article={article} />
            ))}
            {articles?.length === 0 && !articlesError && (
              <div className="text-center py-8 text-gray-500">
                No articles found for this category.
                <br />
                <span className="text-xs">
                  Try processing some news articles using the Star Power API.
                </span>
              </div>
            )}
          </div>
        )}
      </div>
    </>
  );
}
