/**
 * ==========================================
 * OWNERSHIP: SHARED (Coordination Required)
 * ==========================================
 * This file contains type definitions that both Layout and Logic depend on.
 * 
 * CHANGE PROTOCOL:
 * - ANY modifications require discussion between Layout and Logic teams
 * - Interface changes must maintain backward compatibility
 * - New types should be added carefully to avoid breaking changes
 * - Both teams must review and approve modifications
 * 
 * LAYOUT DEPENDENCIES:
 * - Component prop types and interfaces
 * - UI state management types
 * - Display data structures
 * 
 * LOGIC DEPENDENCIES:
 * - API request/response types
 * - Data transformation interfaces
 * - Business logic contracts
 * 
 * CRITICAL INTEGRATION POINTS:
 * - ArticleWithDetails, ExtendedArticle, ExtendedActor
 * - All *Props interfaces used by components
 * - Hook return types (Use*Return interfaces)
 * 
 * CONTACT: Both Layout and Logic teams for any changes
 * ==========================================
 * 
 * API Type Definitions
 * 
 * Type definitions that bridge the existing frontend data models
 * with the new Star Power API Gateway responses.
 */

// Re-export existing types from shared schema
export type {
  User,
  Category,
  Actor,
  Article,
  ArticleWithDetails,
  UserBookmark,
  UserFollow,
} from '@shared/schema';

// Extended types for API integration
export interface ExtendedArticle extends Article {
  // Additional fields from API Gateway
  url?: string;
  source?: string;
  entities?: string[];
  celebrity_mentions?: string[];
  extraction_confidence?: number;
  processing_status?: 'pending' | 'processing' | 'completed' | 'failed';
  language?: string;
  word_count?: number;
}

export interface ExtendedActor extends Actor {
  // Additional fields from API Gateway
  birth_date?: string;
  birth_time?: string;
  birth_location?: string;
  confidence_score?: number;
  data_sources?: string[];
  verified?: boolean;
  professional_name?: string;
  aliases?: string[];
  enhanced?: boolean;
}

// New types for Star Power specific features
export interface CelebrityIdentification {
  name: string;
  confidence: number;
  context: string;
  birth_date?: string;
  birth_location?: string;
  aliases?: string[];
  profession?: string;
}

export interface AstrologyAnalysis {
  celebrity_id: string;
  analysis_date: string;
  planetary_positions: Record<string, {
    longitude: number;
    latitude: number;
    sign: string;
    house: number;
    retrograde?: boolean;
  }>;
  aspects: Array<{
    planet1: string;
    planet2: string;
    aspect_type: string;
    orb: number;
    applying: boolean;
  }>;
  houses: Record<string, {
    sign: string;
    degree: number;
    planets: string[];
  }>;
  va_circuits?: Array<{
    planets: string[];
    circuit_type: string;
    strength: number;
    harmonic: number;
  }>;
  confidence_score: number;
  processing_time_ms: number;
}

export interface PipelineJob {
  job_id: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  created_at: string;
  updated_at: string;
  result?: {
    articles_processed: number;
    celebrities_identified: number;
    analyses_completed: number;
    articles: ExtendedArticle[];
    celebrities: ExtendedActor[];
  };
  error_message?: string;
}

export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  services: Record<string, {
    status: string;
    response_time_ms?: number;
    error?: string;
    details?: Record<string, any>;
  }>;
  overall_response_time_ms?: number;
}

// API Request/Response wrapper types
export interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
  timestamp: string;
}

export interface ApiListResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface ApiError {
  error: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
}

// Search and filter types
export interface ArticleFilters {
  page?: number;
  limit?: number;
  status?: 'pending' | 'processing' | 'completed' | 'failed';
  source?: string;
  search?: string;
  category?: string;
  celebrity_mentions?: string[];
  date_from?: string;
  date_to?: string;
}

export interface CelebrityFilters {
  page?: number;
  limit?: number;
  search?: string;
  verified?: boolean;
  category?: string;
  birth_year_from?: number;
  birth_year_to?: number;
  confidence_min?: number;
}

// Processing request types
export interface ArticleProcessRequest {
  url?: string;
  content?: string;
  title?: string;
  source?: string;
}

export interface CelebrityIdentifyRequest {
  text: string;
  context?: string;
  confidence_threshold?: number;
}

export interface AstrologyAnalyzeRequest {
  celebrity_id: string;
  analysis_date?: string;
  include_va_circuits?: boolean;
  include_transits?: boolean;
}

export interface PipelineProcessRequest {
  query?: string;
  max_articles?: number;
  article_url?: string;
}

// UI State types
export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
  progress?: number;
}

export interface ArticleState extends LoadingState {
  articles: ExtendedArticle[];
  total: number;
  currentPage: number;
  filters: ArticleFilters;
}

export interface CelebrityState extends LoadingState {
  celebrities: ExtendedActor[];
  total: number;
  currentPage: number;
  filters: CelebrityFilters;
}

export interface AstrologyState extends LoadingState {
  analyses: Record<string, AstrologyAnalysis>;
  currentAnalysis?: AstrologyAnalysis;
}

export interface PipelineState extends LoadingState {
  jobs: Record<string, PipelineJob>;
  activeJobs: string[];
}

// Component prop types
export interface ArticleCardProps {
  article: ExtendedArticle;
  showCelebrities?: boolean;
  showAstrology?: boolean;
  onCelebrityClick?: (celebrity: ExtendedActor) => void;
  onAnalyzeClick?: (article: ExtendedArticle) => void;
}

export interface CelebrityCardProps {
  celebrity: ExtendedActor;
  showAstrology?: boolean;
  onAnalyzeClick?: (celebrity: ExtendActor) => void;
  onViewArticles?: (celebrity: ExtendedActor) => void;
}

export interface AstrologyDisplayProps {
  analysis: AstrologyAnalysis;
  celebrity: ExtendedActor;
  compact?: boolean;
}

// Hook return types
export interface UseArticlesReturn {
  articles: ExtendedArticle[];
  loading: boolean;
  error: string | null;
  total: number;
  hasNext: boolean;
  hasPrevious: boolean;
  loadMore: () => void;
  refresh: () => void;
  filter: (filters: Partial<ArticleFilters>) => void;
}

export interface UseCelebritiesReturn {
  celebrities: ExtendedActor[];
  loading: boolean;
  error: string | null;
  total: number;
  hasNext: boolean;
  hasPrevious: boolean;
  loadMore: () => void;
  refresh: () => void;
  search: (query: string) => void;
  identify: (text: string) => Promise<CelebrityIdentification[]>;
}

export interface UseAstrologyReturn {
  analyze: (celebrityId: string) => Promise<AstrologyAnalysis>;
  getChart: (celebrityId: string) => Promise<any>;
  loading: boolean;
  error: string | null;
  currentAnalysis?: AstrologyAnalysis;
}

export interface UsePipelineReturn {
  processArticle: (url: string) => Promise<string[]>;
  processNews: (query: string, maxArticles?: number) => Promise<string[]>;
  getJobStatus: (jobId: string) => Promise<PipelineJob>;
  cancelJob: (jobId: string) => Promise<void>;
  jobs: Record<string, PipelineJob>;
  activeJobs: string[];
  loading: boolean;
  error: string | null;
}
