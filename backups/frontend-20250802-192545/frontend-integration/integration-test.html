<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Star Power API Integration Test</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .healthy { background-color: #d4edda; color: #155724; }
        .unhealthy { background-color: #f8d7da; color: #721c24; }
        .degraded { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <h1>🌟 Star Power API Integration Test</h1>
    
    <div class="container">
        <h2>🔍 API Health Status</h2>
        <button onclick="checkHealth()">Check Health</button>
        <div id="health-status"></div>
    </div>

    <div class="grid">
        <div class="container">
            <h2>📰 Article Processing</h2>
            <input type="url" id="article-url" placeholder="Enter article URL" 
                   value="https://example.com/celebrity-news">
            <button onclick="processArticle()">Process Article</button>
            <div id="article-result" class="result"></div>
        </div>

        <div class="container">
            <h2>🎭 Celebrity Identification</h2>
            <textarea id="celebrity-text" placeholder="Enter text mentioning celebrities" rows="3">Brad Pitt and Angelina Jolie were spotted at the premiere.</textarea>
            <button onclick="identifyCelebrities()">Identify Celebrities</button>
            <div id="celebrity-result" class="result"></div>
        </div>
    </div>

    <div class="grid">
        <div class="container">
            <h2>🔮 Astrology Analysis</h2>
            <input type="text" id="celebrity-id" placeholder="Enter celebrity ID">
            <button onclick="analyzeAstrology()">Analyze Astrology</button>
            <div id="astrology-result" class="result"></div>
        </div>

        <div class="container">
            <h2>⚡ Pipeline Processing</h2>
            <input type="text" id="search-query" placeholder="Search query" value="celebrity news">
            <input type="number" id="max-articles" placeholder="Max articles" value="5" min="1" max="20">
            <button onclick="processPipeline()">Start Pipeline</button>
            <div id="pipeline-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';
        
        // Configure axios defaults
        axios.defaults.timeout = 30000;
        
        function log(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = isError ? 'result error' : 'result';
        }

        async function checkHealth() {
            try {
                log('health-status', 'Checking health...');
                const response = await axios.get(`${API_BASE.replace('/api', '')}/health/`);
                const health = response.data;
                
                let statusHtml = `<div class="status ${health.status}">
                    <strong>Overall Status:</strong> ${health.status.toUpperCase()}
                    <br><strong>Timestamp:</strong> ${health.timestamp}
                    <br><strong>Version:</strong> ${health.version}
                </div>`;
                
                for (const [service, status] of Object.entries(health.services)) {
                    const serviceStatus = typeof status === 'object' ? status.status : status;
                    statusHtml += `<div class="status ${serviceStatus}">
                        <strong>${service}:</strong> ${serviceStatus.toUpperCase()}
                        ${status.error ? `<br><em>Error: ${status.error}</em>` : ''}
                    </div>`;
                }
                
                document.getElementById('health-status').innerHTML = statusHtml;
            } catch (error) {
                log('health-status', `Health check failed: ${error.message}`, true);
            }
        }

        async function processArticle() {
            try {
                const url = document.getElementById('article-url').value;
                if (!url) {
                    log('article-result', 'Please enter an article URL', true);
                    return;
                }
                
                log('article-result', 'Processing article...');
                const response = await axios.post(`${API_BASE}/articles/process`, { url });
                log('article-result', JSON.stringify(response.data, null, 2));
            } catch (error) {
                log('article-result', `Article processing failed: ${error.response?.data?.detail || error.message}`, true);
            }
        }

        async function identifyCelebrities() {
            try {
                const text = document.getElementById('celebrity-text').value;
                if (!text) {
                    log('celebrity-result', 'Please enter text to analyze', true);
                    return;
                }
                
                log('celebrity-result', 'Identifying celebrities...');
                const response = await axios.post(`${API_BASE}/celebrities/identify`, { text });
                log('celebrity-result', JSON.stringify(response.data, null, 2));
            } catch (error) {
                log('celebrity-result', `Celebrity identification failed: ${error.response?.data?.detail || error.message}`, true);
            }
        }

        async function analyzeAstrology() {
            try {
                const celebrityId = document.getElementById('celebrity-id').value;
                if (!celebrityId) {
                    log('astrology-result', 'Please enter a celebrity ID', true);
                    return;
                }
                
                log('astrology-result', 'Analyzing astrology...');
                const response = await axios.post(`${API_BASE}/astrology/analyze`, { 
                    celebrity_id: celebrityId 
                });
                log('astrology-result', JSON.stringify(response.data, null, 2));
            } catch (error) {
                log('astrology-result', `Astrology analysis failed: ${error.response?.data?.detail || error.message}`, true);
            }
        }

        async function processPipeline() {
            try {
                const query = document.getElementById('search-query').value;
                const maxArticles = parseInt(document.getElementById('max-articles').value);
                
                if (!query) {
                    log('pipeline-result', 'Please enter a search query', true);
                    return;
                }
                
                log('pipeline-result', 'Starting pipeline processing...');
                const response = await axios.post(`${API_BASE}/pipeline/process-news`, {
                    search_query: query,
                    max_articles: maxArticles
                });
                log('pipeline-result', JSON.stringify(response.data, null, 2));
            } catch (error) {
                log('pipeline-result', `Pipeline processing failed: ${error.response?.data?.detail || error.message}`, true);
            }
        }

        // Auto-check health on page load
        window.onload = () => {
            checkHealth();
        };
    </script>
</body>
</html>
