# Revised Integration Plan for the "Isolate" Feature

**Version**: 2.1 - Final Pre-Implementation Review
**Last Updated**: August 4, 2025
**Status**: Approved for Implementation

This document outlines the revised plan for integrating the "Isolate" feature, powered by the Perplexity API, into the existing Star Power application. The plan leverages existing infrastructure while adding sophisticated entity extraction capabilities.

## Executive Summary

The Isolate integration represents the **critical missing component** for transforming Star Power from a news aggregator into an intelligent astrological analysis platform. This revised plan prioritizes **infrastructure reuse** and **database retrofitting** over complete system rebuilds, reducing implementation time from 8-10 hours to 6-8 hours.

**Key Strategic Changes**:
- Use `sonar-pro` model for quality over cost optimization
- Retrofit existing database schema instead of complete rebuild
- Leverage existing LLM orchestrator infrastructure
- Position Isolate as data provider for existing VA circuit algorithm

## 1. Core Implementation Assets

### A. **Prompt Template (Unchanged)**

The core instructions for the Perplexity API remain as defined in `isolate_prompt_template.txt`:

```
ROLE: You are the Star Power Isolate component, specialized in extracting structured astrological data from celebrity news articles.

TASK: Extract entities, relationships, actions, and endeavors from the provided text and return them in the specified JSON format.

GUIDELINES:
1. Extract all primary entities (people, organizations, objects, events, endeavors, ships, locations)
2. Include non-primary entities if they meet the Hellenistic house relationship criteria
3. Capture relationships (ongoing states between entities)
4. Record actions (singular events with specific timing)
5. Document endeavors (projects, releases, activities)
6. Use precise inception dates/times/locations when available
7. Mark estimated data with estimated_inception: true

ARTICLE TEXT:
{article_text}

OUTPUT FORMAT: Return ONLY the JSON response following the exact schema structure provided below.

SCHEMA:
{schema}

RESPONSE:
```

### B. **Output Schema (Enhanced Validation)**

The Isolate JSON schema remains comprehensive but will be enhanced with strict Pydantic validation:

```json
{
  "source": "string",
  "entities": [
    {
      "name": "string",
      "type": "person|organization|object|event|endeavor|ship|location",
      "subtype": "string",
      "roles": ["role1", "role2"],
      "primary": true|false,
      "inception_date": "YYYY-MM-DD | null",
      "inception_time": "HH:MM | null",
      "inception_location": "City, State/Country | null",
      "estimated_inception": true|false,
      "relevance_reason": "string"
    }
  ],
  "relationships": [...],
  "actions": [...],
  "endeavors": [...],
  "notes": ["string"]
}
```

### C. **API Configuration (Revised)**

**Model Selection**: `sonar-pro` (upgraded from `sonar-reasoning`)

**Rationale**: For ~10 articles/day processing, quality trumps cost:
- Cost difference: ~$1.20/month ($0.60 vs $1.80)
- Quality impact: Significant for complex entity extraction
- Business value: Better accuracy worth minimal cost increase

**Configuration**:
```json
{
  "model": "sonar-pro",
  "temperature": 0.1,
  "max_tokens": 4000,
  "timeout": 60
}
```

### D. **Golden Set Testing (Enhanced)**

The `starpower_isolate_golden_set.md` will be used for:
- Initial implementation validation
- Regression testing for all changes
- Performance benchmarking
- Quality threshold establishment (90% accuracy target)

## 2. Infrastructure Integration Strategy

### **Key Architectural Decision: Leverage Existing Systems**

Instead of building parallel infrastructure, Isolate will integrate with existing Star Power architecture:

### A. **LLM Orchestrator Integration**

**Current System**: Sophisticated multi-provider LLM orchestration with Ollama + OpenAI
**Integration**: Add Perplexity as specialized provider for article analysis

**Changes to `services/llm_processor/models.py`**:
```python
class LLMProvider(str, Enum):
    OLLAMA = "ollama"
    OPENAI = "openai"
    PERPLEXITY = "perplexity"  # NEW
    LOCAL = "local"

class LLMTaskType(str, Enum):
    CELEBRITY_IDENTIFICATION = "celebrity_identification"
    CONTENT_ANALYSIS = "content_analysis"
    ISOLATE_EXTRACTION = "isolate_extraction"  # NEW
    DATA_ENHANCEMENT = "data_enhancement"
    VALIDATION = "validation"
```

**Task Routing Strategy**:
```python
TASK_PROVIDER_MAPPING = {
    LLMTaskType.ISOLATE_EXTRACTION: LLMProvider.PERPLEXITY,
    LLMTaskType.CELEBRITY_IDENTIFICATION: LLMProvider.PERPLEXITY,
    LLMTaskType.CONTENT_ANALYSIS: LLMProvider.OLLAMA,  # Keep existing
    LLMTaskType.VALIDATION: LLMProvider.OPENAI         # Keep existing
}
```

### B. **Database Retrofit Strategy**

**Recommendation**: **RETROFIT** existing schema, don't rebuild. This approach maintains data integrity while allowing for future scalability.

**New & Enhanced Models**:
```python
# In services/database/models.py

# NEW: A generic model for non-person entities to keep the DB schema clean.
class GenericEntity(BaseModel):
    """Model for non-person entities like organizations, objects, events, etc."""
    id: str = Field(default_factory=lambda: str(uuid4()))
    name: str
    entity_type: str # From IsolateEntity type literal
    subtype: Optional[str] = None
    roles: List[str] = Field(default_factory=list)
    inception_date: Optional[datetime] = None
    inception_time: Optional[str] = None
    inception_location: Optional[str] = None
    estimated_inception: bool = False
    created_at: datetime = Field(default_factory=datetime.utcnow)

# NEW: Tables for relationships, actions, and endeavors
class EntityRelationship(BaseModel):
    """Ongoing relationships between entities"""
    id: str = Field(default_factory=lambda: str(uuid4()))
    entity1_id: str
    entity2_id: str
    relationship_type: str
    # ... other fields from Isolate schema
    created_at: datetime = Field(default_factory=datetime.utcnow)

class EntityAction(BaseModel):
    """Singular events with specific timing"""
    id: str = Field(default_factory=lambda: str(uuid4()))
    actor_id: str
    target_id: str
    action_type: str
    # ... other fields from Isolate schema
    created_at: datetime = Field(default_factory=datetime.utcnow)

class EntityEndeavor(BaseModel):
    """Projects, releases, and significant activities"""
    id: str = Field(default_factory=lambda: str(uuid4()))
    entity_id: str
    endeavor_type: str
    # ... other fields from Isolate schema
    created_at: datetime = Field(default_factory=datetime.utcnow)

# ENHANCED: Existing models
class CelebrityData(BaseModel): # EXISTING - enhance
    # ... existing fields ...
    entity_type: str = Field(default="person", const=True) # Enforce type for compatibility
    roles: List[str] = Field(default_factory=list)

class ArticleData(BaseModel):  # EXISTING - enhance
    # ... existing fields ...
    isolate_processed_at: Optional[datetime] = None # Timestamp for processing
    isolate_entity_ids: List[str] = Field(default_factory=list) # Store resolved DB IDs
    isolate_confidence: Optional[float] = None
```

## 3. Implementation Plan

### **Phase 1: Core LLM Integration (2-3 hours)**

#### 1.1 Perplexity Client Creation
**File**: `services/llm_processor/perplexity_client.py`
- Mirror structure of existing `openai_client.py`.

#### 1.2 Comprehensive Pydantic Models
**File**: `services/llm_processor/isolate_models.py` (NEW)
- Create Pydantic models (`IsolateEntity`, `IsolateResponse`, etc.) that match the Isolate JSON schema for strict validation.

#### 1.3 LLM Orchestrator Updates
**File**: `services/llm_processor/llm_orchestrator.py`
- Integrate the new `PerplexityClient` and update task routing.

### **Phase 2: Database Integration (2-3 hours)**

#### 2.1 Database Schema Enhancement
**File**: `services/database/models.py`
- Add the new `GenericEntity`, `EntityRelationship`, `EntityAction`, and `EntityEndeavor` models.
- Enhance `CelebrityData` and `ArticleData` as specified above.

#### 2.2 Entity Resolution Service
**File**: `services/llm_processor/entity_resolver.py` (NEW)
- This service will be responsible for the crucial logic of mapping entities from the Perplexity response to the database.
```python
class EntityResolver:
    """Maps Isolate entities to DB entities and stores the resulting graph."""

    def __init__(self, db_session):
        self.db = db_session

    async def resolve_and_store_entities(self, isolate_response: IsolateResponse) -> Dict[str, str]:
        """
        Resolves all entities from an Isolate response, stores them in the DB,
        and returns a map of {isolate_entity_name: db_entity_id}.
        """
        # Logic to loop through isolate_response.entities
        # For each entity, decide if it's a 'person' (CelebrityData) or other (GenericEntity)
        # Find or create the record in the corresponding DB table
        # Return a dictionary mapping the name from the JSON to the new DB ID
        pass

    async def store_isolate_graph(self, article_id: str, isolate_response: IsolateResponse):
        """
        Resolves all entities, then stores the relationships, actions,
        and endeavors connecting them.
        """
        # 1. Get all entities into the DB and get their IDs
        entity_map = await self.resolve_and_store_entities(isolate_response)

        # 2. Store relationships, actions, and endeavors using the mapped DB IDs

        # 3. Update the ArticleData record with metadata
        await self.db.articles.update(article_id, {
            "isolate_processed_at": datetime.utcnow(),
            "isolate_entity_ids": list(entity_map.values()),
        })
```

#### 2.3 Database Service Updates
**File**: `services/database/celebrity_db_service.py`
- Add methods to support the `EntityResolver`, such as `find_or_create` for celebrities and the new generic entities.

### **Phase 3: Pipeline Integration (1-2 hours)**

#### 3.1 News Processing Pipeline Update
**File**: `services/news_pipeline/news_ingestion_service.py`
- Modify the `process_article` method to include the new `ISOLATE_EXTRACTION` step and call the `EntityResolver` to store the results.

#### 3.2 API Gateway Integration
**File**: `services/api_gateway/routes/articles.py`
- Enhance API responses to include the newly extracted entity and relationship data.

#### 3.3 VA Circuit Algorithm Integration
**Data Flow**: The critical data flow remains the same, but Isolate now provides a much richer source of entities for the VA algorithm.
`GNews -> Isolate -> DB -> VA Circuit -> Frontend`

### **Phase 4: Testing & Validation (1 hour)**

#### 4.1 Golden Set Implementation
**File**: `tests/unit/test_isolate_integration.py`
- Create tests that use the Golden Set articles as input and assert that the database contains the correct entities and relationships after processing.

---
(The rest of the sections: 4. Environment Configuration, 5. Success Criteria, 6. Risk Mitigation, 7. Future Enhancements, 8. Implementation Timeline, and 9. Conclusion remain the same as in the previous version.)
