class StarPowerIsolateLab {
    constructor() {
        this.config = {
            apiKey: '',
            model: 'sonar-pro',
            temperature: 0.1,
            maxTokens: 4000,
            threshold: 90,
            autoValidate: true
        };
        
        this.currentResponse = null;
        this.goldenSetData = null;
        this.sampleArticles = [];
        this.promptTemplate = '';
        this.schema = {};
        
        this.init();
    }

    async init() {
        await this.loadDefaultData();
        this.setupEventListeners();
        this.setupKeyboardShortcuts();
        this.updateUI();
        this.loadSampleArticles();
        this.loadPromptTemplate();
    }

    async loadDefaultData() {
        // Load default data if assets fail to load
        this.goldenSetData = {
            "source": "Billboard, February 4, 1959",
            "entities": [
                {
                    "name": "<PERSON> Holly",
                    "type": "person",
                    "roles": ["performer"],
                    "primary": true,
                    "inception_date": "1936-09-07",
                    "inception_time": null,
                    "inception_location": "Lubbock, Texas, USA",
                    "estimated_inception": true
                }
            ],
            "relationships": [],
            "actions": [
                {
                    "entity1": "Buddy Holly",
                    "entity2": "<PERSON>",
                    "action_type": "died_with",
                    "both_primary": true,
                    "inception_date": "1959-02-03",
                    "inception_time": "01:00", 
                    "inception_location": "Clear Lake, Iowa, USA",
                    "estimated_inception": false
                }
            ],
            "endeavors": [],
            "notes": ["Used 'died_with' action to avoid assigning false agency in mutual casualty event"]
        };

        this.sampleArticles = [
            {
                "title": "Buddy Holly Plane Crash Example",
                "text": "On February 3, 1959, rock and roll musicians Buddy Holly, Ritchie Valens, and J.P. \"The Big Bopper\" Richardson died in a plane crash near Clear Lake, Iowa, shortly after takeoff from Mason City airport. The three performers had been touring as part of the Winter Dance Party tour and chartered the small aircraft to reach their next venue in Moorhead, Minnesota."
            },
            {
                "title": "Modern Celebrity Romance", 
                "text": "Tom Holland and Zendaya confirmed their relationship on July 2, 2021, when paparazzi photos showed them kissing in a car in Los Angeles. The Spider-Man co-stars had been the subject of dating rumors since 2017, but had consistently denied being anything more than friends."
            },
            {
                "title": "Award Show Interruption",
                "text": "During the 2009 MTV Video Music Awards at Radio City Music Hall, Kanye West interrupted Taylor Swift's acceptance speech for Best Female Video. Swift, who won for 'You Belong With Me,' was giving her speech when West took the microphone and declared that Beyoncé had 'one of the best videos of all time.'"
            }
        ];

        this.promptTemplate = `ROLE: You are the Star Power Isolate component, specialized in extracting structured astrological data from celebrity news articles.

TASK: Extract entities, relationships, actions, and endeavors from the provided text and return them in the specified JSON format.

GUIDELINES:
1. Extract all primary entities (people, organizations, objects, events, endeavors, ships, locations)
2. Include non-primary entities if they meet the Hellenistic house relationship criteria
3. Capture relationships (ongoing states between entities)
4. Record actions (singular events with specific timing)
5. Document endeavors (projects, releases, activities)
6. Use precise inception dates/times/locations when available
7. Mark estimated data with estimated_inception: true

ARTICLE TEXT:
{article_text}

OUTPUT FORMAT: Return ONLY the JSON response following the exact schema structure provided below.

SCHEMA:
{schema}

RESPONSE:`;

        this.schema = {
            "source": "string",
            "entities": [
                {
                    "name": "string",
                    "type": "person|organization|object|event|endeavor|ship|location",
                    "subtype": "string",
                    "roles": ["role1", "role2"],
                    "primary": "true|false",
                    "inception_date": "YYYY-MM-DD | null",
                    "inception_time": "HH:MM | null",
                    "inception_location": "City, State/Country | null",
                    "estimated_inception": "true|false",
                    "relevance_reason": "string"
                }
            ],
            "relationships": [
                {
                    "entity1": "string",
                    "entity2": "string", 
                    "relation_type": "string",
                    "both_primary": "true|false",
                    "inception_date": "YYYY-MM-DD | null",
                    "inception_time": "HH:MM | null",
                    "inception_location": "string | null",
                    "estimated_inception": "true|false"
                }
            ],
            "actions": [
                {
                    "entity1": "string",
                    "entity2": "string",
                    "action_type": "string", 
                    "both_primary": "true|false",
                    "inception_date": "YYYY-MM-DD",
                    "inception_time": "HH:MM | null",
                    "inception_location": "string | null",
                    "estimated_inception": "true|false"
                }
            ],
            "endeavors": [
                {
                    "entity": "string",
                    "endeavor_type": "string",
                    "endeavor_label": "string",
                    "primary": "true|false",
                    "inception_date": "YYYY-MM-DD | null",
                    "inception_time": "HH:MM | null", 
                    "inception_location": "string | null",
                    "estimated_inception": "true|false"
                }
            ],
            "notes": ["string"]
        };

        // Try to load from external assets, but fallback to defaults
        try {
            const assets = await Promise.allSettled([
                fetch('https://ppl-ai-code-interpreter-files.s3.amazonaws.com/web/direct-files/dcbe025fd6b8d8eb5d2ae4efdf1bf5d0/54f9717d-fa0c-4797-abc0-0f213c01c020/898399b7.json').then(r => r.json()),
                fetch('https://ppl-ai-code-interpreter-files.s3.amazonaws.com/web/direct-files/dcbe025fd6b8d8eb5d2ae4efdf1bf5d0/54f9717d-fa0c-4797-abc0-0f213c01c020/13d47ddf.json').then(r => r.json()),
                fetch('https://ppl-ai-code-interpreter-files.s3.amazonaws.com/web/direct-files/dcbe025fd6b8d8eb5d2ae4efdf1bf5d0/257c333c-af43-4c08-856f-42dc766e4998/52255094.txt').then(r => r.text()),
                fetch('https://ppl-ai-code-interpreter-files.s3.amazonaws.com/web/direct-files/dcbe025fd6b8d8eb5d2ae4efdf1bf5d0/257c333c-af43-4c08-856f-42dc766e4998/17591cd7.json').then(r => r.json())
            ]);

            if (assets[0].status === 'fulfilled') this.goldenSetData = assets[0].value;
            if (assets[1].status === 'fulfilled') this.sampleArticles = assets[1].value;
            if (assets[2].status === 'fulfilled') this.promptTemplate = assets[2].value;
            if (assets[3].status === 'fulfilled') this.schema = assets[3].value;
            
        } catch (error) {
            console.warn('Using default data due to asset loading error:', error);
        }
    }

    setupEventListeners() {
        // Configuration controls
        document.getElementById('apiKey').addEventListener('input', (e) => {
            this.config.apiKey = e.target.value;
            this.updateApiStatus();
        });
        
        document.getElementById('modelSelect').addEventListener('change', (e) => {
            this.config.model = e.target.value;
        });
        
        document.getElementById('temperature').addEventListener('input', (e) => {
            this.config.temperature = parseFloat(e.target.value);
            document.getElementById('tempValue').textContent = e.target.value;
        });
        
        document.getElementById('maxTokens').addEventListener('input', (e) => {
            this.config.maxTokens = parseInt(e.target.value);
        });
        
        document.getElementById('threshold').addEventListener('input', (e) => {
            this.config.threshold = parseInt(e.target.value);
            document.getElementById('thresholdValue').textContent = `${e.target.value}%`;
        });
        
        document.getElementById('autoValidate').addEventListener('change', (e) => {
            this.config.autoValidate = e.target.checked;
        });

        // Tab switching
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Article input
        document.getElementById('customArticle').addEventListener('input', this.updateWordCount.bind(this));
        document.getElementById('sampleSelect').addEventListener('change', this.loadSampleArticle.bind(this));

        // Button controls
        document.getElementById('runTest').addEventListener('click', this.runIsolateTest.bind(this));
        document.getElementById('compareGolden').addEventListener('click', this.compareWithGoldenSet.bind(this));
        document.getElementById('resetConfig').addEventListener('click', this.resetConfiguration.bind(this));
        document.getElementById('resetPrompt').addEventListener('click', this.resetPrompt.bind(this));
        document.getElementById('exportPrompt').addEventListener('click', this.exportPrompt.bind(this));
        document.getElementById('clearArticle').addEventListener('click', this.clearArticle.bind(this));
        document.getElementById('exportResults').addEventListener('click', this.exportResults.bind(this));
        document.getElementById('themeToggle').addEventListener('click', this.toggleTheme.bind(this));
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'Enter':
                        e.preventDefault();
                        this.runIsolateTest();
                        break;
                    case 's':
                        e.preventDefault();
                        this.exportPrompt();
                        break;
                }
            }
        });
    }

    updateUI() {
        document.getElementById('tempValue').textContent = this.config.temperature;
        document.getElementById('thresholdValue').textContent = `${this.config.threshold}%`;
        document.getElementById('temperature').value = this.config.temperature;
        document.getElementById('threshold').value = this.config.threshold;
        document.getElementById('maxTokens').value = this.config.maxTokens;
        document.getElementById('modelSelect').value = this.config.model;
        document.getElementById('autoValidate').checked = this.config.autoValidate;
    }

    updateApiStatus() {
        const statusEl = document.getElementById('apiStatus');
        if (this.config.apiKey && this.config.apiKey.length > 10) {
            statusEl.textContent = 'Connected';
            statusEl.classList.add('connected');
        } else {
            statusEl.textContent = 'Disconnected';
            statusEl.classList.remove('connected');
        }
    }

    loadSampleArticles() {
        const select = document.getElementById('sampleSelect');
        select.innerHTML = '<option value="">Select a sample article...</option>';
        
        this.sampleArticles.forEach((article, index) => {
            const option = document.createElement('option');
            option.value = index;
            option.textContent = article.title;
            select.appendChild(option);
        });
    }

    loadPromptTemplate() {
        document.getElementById('promptEditor').value = this.promptTemplate;
        document.getElementById('expectedOutput').textContent = JSON.stringify(this.goldenSetData, null, 2);
        this.highlightJSON('expectedOutput');
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');
    }

    updateWordCount() {
        const text = document.getElementById('customArticle').value;
        const words = text.trim() ? text.trim().split(/\s+/).length : 0;
        document.getElementById('wordCount').textContent = `${words} words`;
    }

    loadSampleArticle() {
        const select = document.getElementById('sampleSelect');
        const index = select.value;
        
        if (index !== '') {
            const article = this.sampleArticles[index];
            document.getElementById('samplePreview').textContent = article.text;
        } else {
            document.getElementById('samplePreview').textContent = '';
        }
    }

    async runIsolateTest() {
        // Check API key first
        if (!this.config.apiKey || this.config.apiKey.length < 10) {
            this.showToast('Please enter your Perplexity API key in the Configuration panel', 'error');
            return;
        }

        const articleText = this.getArticleText();
        if (!articleText) {
            this.showToast('Please enter an article in the Custom Article tab or select a sample article', 'error');
            return;
        }

        const button = document.getElementById('runTest');
        const buttonText = button.querySelector('.button-text');
        const spinner = button.querySelector('.loading-spinner');
        
        // Show loading state
        button.disabled = true;
        buttonText.textContent = 'Running Test...';
        spinner.classList.remove('hidden');

        try {
            const prompt = this.buildPrompt(articleText);
            const startTime = Date.now();
            
            const response = await this.callPerplexityAPI(prompt);
            const endTime = Date.now();
            
            this.currentResponse = response;
            
            // Display results
            this.displayAPIResponse(response, endTime - startTime);
            
            // Enable comparison
            document.getElementById('compareGolden').disabled = false;
            document.getElementById('exportResults').disabled = false;
            
            this.showToast('Test completed successfully', 'success');
            
        } catch (error) {
            console.error('API Error:', error);
            this.showToast(`API Error: ${error.message}`, 'error');
            document.getElementById('apiResponse').textContent = `Error: ${error.message}`;
        } finally {
            // Reset button state
            button.disabled = false;
            buttonText.textContent = 'Run Isolate Test';
            spinner.classList.add('hidden');
        }
    }

    getArticleText() {
        const activeTab = document.querySelector('.tab.active').dataset.tab;
        
        if (activeTab === 'custom') {
            return document.getElementById('customArticle').value.trim();
        } else {
            const select = document.getElementById('sampleSelect');
            const index = select.value;
            return index !== '' ? this.sampleArticles[index].text : '';
        }
    }

    buildPrompt(articleText) {
        const prompt = document.getElementById('promptEditor').value;
        const schemaText = JSON.stringify(this.schema, null, 2);
        
        return prompt
            .replace('{article_text}', articleText)
            .replace('{schema}', schemaText);
    }

    async callPerplexityAPI(prompt) {
        const response = await fetch('https://api.perplexity.ai/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.config.apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: this.config.model,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: this.config.temperature,
                max_tokens: this.config.maxTokens
            })
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data;
    }

    displayAPIResponse(response, duration) {
        const responseEl = document.getElementById('apiResponse');
        const metaEl = document.getElementById('responseMeta');
        
        // Display metadata
        const usage = response.usage || {};
        metaEl.innerHTML = `
            <span>Tokens: ${usage.total_tokens || 'N/A'}</span>
            <span>Duration: ${duration}ms</span>
            <span>Model: ${this.config.model}</span>
        `;

        // Extract and display JSON content
        const content = response.choices?.[0]?.message?.content || '';
        let jsonContent = content;
        
        // Try to extract JSON from response
        const jsonMatch = content.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
            jsonContent = jsonMatch[0];
        }

        try {
            const parsed = JSON.parse(jsonContent);
            responseEl.textContent = JSON.stringify(parsed, null, 2);
            this.highlightJSON('apiResponse');
            
            // Auto-validate if enabled
            if (this.config.autoValidate) {
                this.validateSchema(parsed);
            }
        } catch (e) {
            responseEl.textContent = content;
            this.showToast('Response is not valid JSON', 'warning');
        }
    }

    validateSchema(data) {
        const required = ['source', 'entities', 'relationships', 'actions', 'endeavors', 'notes'];
        const missing = required.filter(field => !(field in data));
        
        if (missing.length > 0) {
            this.showToast(`Schema validation failed: missing ${missing.join(', ')}`, 'warning');
        } else {
            this.showToast('Schema validation passed', 'success');
        }
    }

    compareWithGoldenSet() {
        if (!this.currentResponse) {
            this.showToast('No test results to compare', 'error');
            return;
        }

        const content = this.currentResponse.choices?.[0]?.message?.content || '';
        let testData;
        
        try {
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            const jsonContent = jsonMatch ? jsonMatch[0] : content;
            testData = JSON.parse(jsonContent);
        } catch (e) {
            this.showToast('Cannot compare: invalid JSON response', 'error');
            return;
        }

        // Simple comparison scoring
        const score = this.calculateSimilarityScore(testData, this.goldenSetData);
        
        // Display comparison
        document.getElementById('testOutput').textContent = JSON.stringify(testData, null, 2);
        this.highlightJSON('testOutput');
        
        const scoreEl = document.getElementById('comparisonScore');
        scoreEl.textContent = `Similarity Score: ${score.toFixed(1)}%`;
        
        // Apply score styling
        scoreEl.className = 'comparison-score';
        if (score >= this.config.threshold) {
            scoreEl.classList.add('pass');
        } else if (score >= 70) {
            scoreEl.classList.add('warning');
        } else {
            scoreEl.classList.add('fail');
        }
        
        this.showToast(`Comparison complete: ${score.toFixed(1)}% similarity`, 'info');
    }

    calculateSimilarityScore(test, golden) {
        // Simple scoring based on matching keys and similar structures
        let score = 0;
        let totalChecks = 0;
        
        // Check source
        totalChecks++;
        if (test.source && golden.source) score++;
        
        // Check entities count similarity
        totalChecks++;
        const testEntities = test.entities?.length || 0;
        const goldenEntities = golden.entities?.length || 0;
        if (testEntities > 0 && goldenEntities > 0) {
            const entitySimilarity = 1 - Math.abs(testEntities - goldenEntities) / Math.max(testEntities, goldenEntities);
            score += entitySimilarity;
        }
        
        // Check structure completeness
        const sections = ['relationships', 'actions', 'endeavors', 'notes'];
        sections.forEach(section => {
            totalChecks++;
            if (test[section] && golden[section]) {
                score++;
            }
        });
        
        return totalChecks > 0 ? (score / totalChecks) * 100 : 0;
    }

    highlightJSON(elementId) {
        const element = document.getElementById(elementId);
        if (element && window.Prism) {
            element.className = 'language-json';
            window.Prism.highlightElement(element);
        }
    }

    resetConfiguration() {
        this.config = {
            apiKey: '',
            model: 'sonar-pro',
            temperature: 0.1,
            maxTokens: 4000,
            threshold: 90,
            autoValidate: true
        };
        
        document.getElementById('apiKey').value = '';
        document.getElementById('modelSelect').value = 'sonar-pro';
        document.getElementById('temperature').value = 0.1;
        document.getElementById('maxTokens').value = 4000;
        document.getElementById('threshold').value = 90;
        document.getElementById('autoValidate').checked = true;
        
        this.updateUI();
        this.updateApiStatus();
        this.showToast('Configuration reset to defaults', 'info');
    }

    resetPrompt() {
        document.getElementById('promptEditor').value = this.promptTemplate;
        this.showToast('Prompt reset to default template', 'info');
    }

    exportPrompt() {
        const prompt = document.getElementById('promptEditor').value;
        this.downloadFile('isolate_prompt.txt', prompt);
        this.showToast('Prompt exported successfully', 'success');
    }

    clearArticle() {
        document.getElementById('customArticle').value = '';
        document.getElementById('sampleSelect').value = '';
        document.getElementById('samplePreview').textContent = '';
        this.updateWordCount();
        this.showToast('Article input cleared', 'info');
    }

    exportResults() {
        if (!this.currentResponse) {
            this.showToast('No results to export', 'error');
            return;
        }
        
        const results = {
            timestamp: new Date().toISOString(),
            config: this.config,
            response: this.currentResponse,
            article: this.getArticleText()
        };
        
        this.downloadFile('isolate_results.json', JSON.stringify(results, null, 2));
        this.showToast('Results exported successfully', 'success');
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-color-scheme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        document.documentElement.setAttribute('data-color-scheme', newTheme);
        
        const button = document.getElementById('themeToggle');
        button.textContent = newTheme === 'dark' ? '☀️' : '🌙';
    }

    downloadFile(filename, content) {
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    showToast(message, type = 'info') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        
        container.appendChild(toast);
        
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 5000);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new StarPowerIsolateLab();
});