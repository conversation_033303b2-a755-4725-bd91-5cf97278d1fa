
function compareWithGoldenSet(testOutput, goldenSet) {
    const result = {
        passed: false,
        score: 0,
        differences: [],
        summary: ''
    };

    try {
        // Parse JSON if strings
        const test = typeof testOutput === 'string' ? JSON.parse(testOutput) : testOutput;
        const golden = typeof goldenSet === 'string' ? JSON.parse(goldenSet) : goldenSet;

        // Compare sections
        const sections = ['entities', 'relationships', 'actions', 'endeavors'];
        let totalChecks = 0;
        let passedChecks = 0;

        sections.forEach(section => {
            if (test[section] && golden[section]) {
                totalChecks++;
                if (JSON.stringify(test[section].sort()) === JSON.stringify(golden[section].sort())) {
                    passedChecks++;
                } else {
                    result.differences.push(`${section} section mismatch`);
                }
            }
        });

        result.score = totalChecks > 0 ? (passedChecks / totalChecks * 100) : 0;
        result.passed = result.score >= 90; // 90% threshold
        result.summary = `Score: ${result.score.toFixed(1)}% (${passedChecks}/${totalChecks} sections match)`;

    } catch (error) {
        result.differences.push(`JSON parsing error: ${error.message}`);
        result.summary = 'Failed to parse JSON';
    }

    return result;
}
