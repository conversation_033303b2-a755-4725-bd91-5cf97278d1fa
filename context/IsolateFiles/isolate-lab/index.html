<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Star Power Isolate Lab</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-dark.min.css" media="(prefers-color-scheme: dark)">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1>Star Power Isolate Lab</h1>
                <div class="header-controls">
                    <div class="api-status">
                        <span class="status-indicator" id="apiStatus">Disconnected</span>
                    </div>
                    <button class="btn btn--secondary" id="themeToggle">🌙</button>
                </div>
            </div>
        </header>

        <!-- Main Dashboard Grid -->
        <main class="dashboard">
            <!-- Configuration Panel -->
            <section class="panel config-panel">
                <div class="panel-header">
                    <h2>Configuration</h2>
                    <button class="btn btn--sm btn--outline" id="resetConfig">Reset</button>
                </div>
                <div class="panel-content">
                    <div class="form-group">
                        <label class="form-label">API Settings</label>
                        <div class="form-row">
                            <input type="password" class="form-control" id="apiKey" placeholder="Enter Perplexity API Key">
                        </div>
                        <div class="form-row">
                            <select class="form-control" id="modelSelect">
                                <option value="sonar-pro">sonar-pro</option>
                                <option value="sonar-reasoning">sonar-reasoning</option>
                                <option value="sonar">sonar</option>
                            </select>
                        </div>
                        <div class="form-row">
                            <label class="form-label">Temperature: <span id="tempValue">0.1</span></label>
                            <input type="range" class="slider" id="temperature" min="0" max="1" step="0.1" value="0.1">
                        </div>
                        <div class="form-row">
                            <input type="number" class="form-control" id="maxTokens" placeholder="Max Tokens" value="4000">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Test Settings</label>
                        <div class="form-row">
                            <label class="form-label">Golden Set Threshold: <span id="thresholdValue">90%</span></label>
                            <input type="range" class="slider" id="threshold" min="80" max="100" step="1" value="90">
                        </div>
                        <div class="form-row">
                            <label class="checkbox-label">
                                <input type="checkbox" id="autoValidate" checked>
                                Auto-validate schema
                            </label>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Prompt Editor -->
            <section class="panel prompt-panel">
                <div class="panel-header">
                    <h2>Prompt Editor</h2>
                    <div class="panel-controls">
                        <button class="btn btn--sm btn--outline" id="resetPrompt">Reset</button>
                        <button class="btn btn--sm btn--outline" id="exportPrompt">Export</button>
                    </div>
                </div>
                <div class="panel-content">
                    <div class="editor-container">
                        <textarea class="form-control editor" id="promptEditor" rows="20"></textarea>
                    </div>
                </div>
            </section>

            <!-- Article Input -->
            <section class="panel article-panel">
                <div class="panel-header">
                    <h2>Article Input</h2>
                    <div class="panel-controls">
                        <span class="word-count" id="wordCount">0 words</span>
                        <button class="btn btn--sm btn--outline" id="clearArticle">Clear</button>
                    </div>
                </div>
                <div class="panel-content">
                    <div class="tabs">
                        <button class="tab active" data-tab="custom">Custom Article</button>
                        <button class="tab" data-tab="samples">Sample Articles</button>
                    </div>
                    
                    <div class="tab-content active" id="custom">
                        <textarea class="form-control" id="customArticle" rows="15" placeholder="Paste your article text here..."></textarea>
                    </div>
                    
                    <div class="tab-content" id="samples">
                        <select class="form-control" id="sampleSelect">
                            <option value="">Select a sample article...</option>
                        </select>
                        <div class="sample-preview" id="samplePreview"></div>
                    </div>
                </div>
            </section>

            <!-- Results & Comparison -->
            <section class="panel results-panel">
                <div class="panel-header">
                    <h2>Results & Comparison</h2>
                    <div class="panel-controls">
                        <button class="btn btn--sm btn--outline" id="exportResults" disabled>Export</button>
                    </div>
                </div>
                <div class="panel-content">
                    <div class="results-section">
                        <h3>API Response</h3>
                        <div class="response-meta" id="responseMeta"></div>
                        <div class="response-container">
                            <pre class="response-output" id="apiResponse">No response yet. Run a test to see results.</pre>
                        </div>
                    </div>
                    
                    <div class="comparison-section">
                        <h3>Golden Set Comparison</h3>
                        <div class="comparison-score" id="comparisonScore"></div>
                        <div class="comparison-container">
                            <div class="comparison-side">
                                <h4>Test Output</h4>
                                <pre class="comparison-output" id="testOutput">Run a test to compare results</pre>
                            </div>
                            <div class="comparison-side">
                                <h4>Expected Output</h4>
                                <pre class="comparison-output" id="expectedOutput">Golden set will appear here</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Action Controls -->
        <div class="action-controls">
            <button class="btn btn--primary btn--lg" id="runTest">
                <span class="button-text">Run Isolate Test</span>
                <span class="loading-spinner hidden">⏳</span>
            </button>
            <button class="btn btn--secondary btn--lg" id="compareGolden" disabled>Compare with Golden Set</button>
        </div>

        <!-- Toast Notifications -->
        <div class="toast-container" id="toastContainer"></div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="app.js"></script>
</body>
</html>