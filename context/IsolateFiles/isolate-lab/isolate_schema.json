{"source": "string", "entities": [{"name": "string", "type": "person|organization|object|event|endeavor|ship|location", "subtype": "string", "roles": ["role1", "role2"], "primary": "true|false", "inception_date": "YYYY-MM-DD | null", "inception_time": "HH:MM | null", "inception_location": "City, State/Country | null", "estimated_inception": "true|false", "relevance_reason": "string"}], "relationships": [{"entity1": "string", "entity2": "string", "relation_type": "string", "both_primary": "true|false", "inception_date": "YYYY-MM-DD | null", "inception_time": "HH:MM | null", "inception_location": "string | null", "estimated_inception": "true|false"}], "actions": [{"entity1": "string", "entity2": "string", "action_type": "string", "both_primary": "true|false", "inception_date": "YYYY-MM-DD", "inception_time": "HH:MM | null", "inception_location": "string | null", "estimated_inception": "true|false"}], "endeavors": [{"entity": "string", "endeavor_type": "string", "endeavor_label": "string", "primary": "true|false", "inception_date": "YYYY-MM-DD | null", "inception_time": "HH:MM | null", "inception_location": "string | null", "estimated_inception": "true|false"}], "notes": ["string"]}