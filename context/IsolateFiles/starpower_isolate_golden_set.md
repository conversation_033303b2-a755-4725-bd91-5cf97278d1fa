# Star Power: Golden Set Examples

_Last updated: January 27, 2025_

## Overview

This document contains canonical examples of expected JSON output from the Star Power Isolate component. These examples serve as regression tests and training data for the extraction system.

## Golden Set Example 1: Buddy <PERSON> Crash (1959)

### Article Text
"On February 3, 1959, rock and roll musicians <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON> "The Big Bopper" <PERSON> died in a plane crash near Clear Lake, Iowa, shortly after takeoff from Mason City airport. The three performers had been touring as part of the Winter Dance Party tour and chartered the small aircraft to reach their next venue in Moorhead, Minnesota. The tragedy occurred around 1:00 AM during a snowstorm, killing all three musicians and pilot <PERSON> instantly."

### Expected JSON Output
```json
{
  "source": "Billboard, February 4, 1959",
  "entities": [
    {
      "name": "<PERSON>",
      "type": "person",
      "roles": ["performer"],
      "primary": true,
      "inception_date": "1936-09-07",
      "inception_time": null,
      "inception_location": "Lubbock, Texas, USA",
      "estimated_inception": true
    },
    {
      "entity1": "<PERSON>",
      "entity2": "The Rise and Fall of Zig<PERSON> and the Spiders from Mars",
      "relation_type": "creator",
      "both_primary": true,
      "inception_date": "1972-06-16",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    }
  ],
  "actions": [
    {
      "entity1": "David Bowie",
      "entity2": "Ziggy Stardust",
      "action_type": "introduced",
      "both_primary": true,
      "inception_date": "1972-01-01",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": true
    },
    {
      "entity1": "David Bowie",
      "entity2": "Ziggy Stardust",
      "action_type": "retired",
      "both_primary": true,
      "inception_date": "1973-07-03",
      "inception_time": null,
      "inception_location": "Hammersmith Odeon, London, England",
      "estimated_inception": false
    }
  ],
  "endeavors": [
    {
      "entity": "The Rise and Fall of Ziggy Stardust and the Spiders from Mars",
      "endeavor_type": "album",
      "endeavor_label": "The Rise and Fall of Ziggy Stardust and the Spiders from Mars",
      "primary": true,
      "inception_date": "1972-06-16",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    }
  ],
  "notes": [
    "Alter egos treated as separate entities for performance periods",
    "Character has own inception date for natal chart"
  ]
}
```

---

## Notes on Golden Set Design

### Key Testing Points
1. **Historical Compatibility**: Buddy Holly example tests 1950s celebrity news extraction
2. **Modern Celebrity Drama**: Kanye/Taylor tests complex multi-entity interactions with precise timing
3. **Fan Culture Integration**: Tomdaya example tests ship entity creation and dual relationship tracking
4. **Posthumous Handling**: Tupac example shows how to handle releases after death
5. **Band Dynamics**: Van Halen demonstrates member changes and rejoining
6. **Identity Changes**: Prince and Bowie examples show rebranding vs. alter ego handling

### Edge Cases Covered
- Non-primary entities (spouses, companies) with clear inclusion rationale
- Objects with astrological subtype classifications
- Rumor vs. official relationship status tracking
- Ship entities as composite celebrity profiles
- Estimated vs. confirmed inception dates
- Mutual casualties using "died_with" action
- Self-referential actions for rebranding
- Alter egos as separate entities

### Validation Criteria
- All primary entities explicitly mentioned in article text
- Non-primary entities pass Hellenistic House noun relationship test
- Ship entities automatically created for confirmed celebrity couples
- Romance rumors and official announcements tracked separately
- Object subtypes enable planetary/house correspondence analysis
- Actions avoid false agency assignment in mutual casualties
- Endeavor inception uses release date, not creation date

### Implementation Notes
- Alias system tracks name changes within single entity
- Alter egos get separate entity treatment for astrological analysis
- Member_of relationships use current join date, not historical
- Posthumous releases maintain creator relationships with estimated dates
- Location entities include venues for GPS coordinate lookup

**End of Golden Set Document** false
    },
    {
      "name": "Ritchie Valens",
      "type": "person",
      "roles": ["performer"],
      "primary": true,
      "inception_date": "1941-05-13",
      "inception_time": null,
      "inception_location": "Pacoima, California, USA",
      "estimated_inception": false
    },
    {
      "name": "The Big Bopper",
      "type": "person",
      "roles": ["performer"],
      "primary": true,
      "inception_date": "1930-10-24",
      "inception_time": null,
      "inception_location": "Sabine Pass, Texas, USA",
      "estimated_inception": false
    },
    {
      "name": "Roger Peterson",
      "type": "person",
      "roles": ["pilot"],
      "primary": true,
      "inception_date": null,
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    },
    {
      "name": "Plane",
      "type": "object",
      "subtype": "vehicle",
      "roles": ["object"],
      "primary": true,
      "inception_date": null,
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    },
    {
      "name": "Clear Lake",
      "type": "location",
      "roles": ["location"],
      "primary": true,
      "inception_date": null,
      "inception_time": null,
      "inception_location": "Clear Lake, Iowa, USA",
      "estimated_inception": false
    },
    {
      "name": "Winter Dance Party",
      "type": "event",
      "roles": ["event"],
      "primary": true,
      "inception_date": "1959-01-23",
      "inception_time": null,
      "inception_location": "Milwaukee, Wisconsin, USA",
      "estimated_inception": false
    },
    {
      "name": "Maria Elena Holly",
      "type": "person",
      "roles": ["spouse"],
      "primary": false,
      "relevance_reason": "Spouse directly impacted by primary entity's death",
      "inception_date": "1932-12-20",
      "inception_time": null,
      "inception_location": "San Juan, Puerto Rico",
      "estimated_inception": false
    }
  ],
  "relationships": [
    {
      "entity1": "Buddy Holly",
      "entity2": "Ritchie Valens",
      "relation_type": "collaborator",
      "both_primary": true,
      "inception_date": "1959-01-23",
      "inception_time": null,
      "inception_location": "Milwaukee, Wisconsin, USA",
      "estimated_inception": false
    },
    {
      "entity1": "Buddy Holly",
      "entity2": "The Big Bopper",
      "relation_type": "collaborator",
      "both_primary": true,
      "inception_date": "1959-01-23",
      "inception_time": null,
      "inception_location": "Milwaukee, Wisconsin, USA",
      "estimated_inception": false
    },
    {
      "entity1": "Buddy Holly",
      "entity2": "Maria Elena Holly",
      "relation_type": "spouse",
      "both_primary": false,
      "inception_date": "1958-08-15",
      "inception_time": null,
      "inception_location": "Lubbock, Texas, USA",
      "estimated_inception": false
    },
    {
      "entity1": "Roger Peterson",
      "entity2": "Plane",
      "relation_type": "pilot",
      "both_primary": true,
      "inception_date": "1959-02-03",
      "inception_time": "00:30",
      "inception_location": "Mason City, Iowa, USA",
      "estimated_inception": false
    }
  ],
  "actions": [
    {
      "entity1": "Buddy Holly",
      "entity2": "Roger Peterson",
      "action_type": "died_with",
      "both_primary": true,
      "inception_date": "1959-02-03",
      "inception_time": "01:00",
      "inception_location": "Clear Lake, Iowa, USA",
      "estimated_inception": false
    },
    {
      "entity1": "Ritchie Valens",
      "entity2": "Roger Peterson",
      "action_type": "died_with",
      "both_primary": true,
      "inception_date": "1959-02-03",
      "inception_time": "01:00",
      "inception_location": "Clear Lake, Iowa, USA",
      "estimated_inception": false
    },
    {
      "entity1": "The Big Bopper",
      "entity2": "Roger Peterson",
      "action_type": "died_with",
      "both_primary": true,
      "inception_date": "1959-02-03",
      "inception_time": "01:00",
      "inception_location": "Clear Lake, Iowa, USA",
      "estimated_inception": false
    }
  ],
  "endeavors": [
    {
      "entity": "Winter Dance Party",
      "endeavor_type": "tour",
      "endeavor_label": "Winter Dance Party Tour 1959",
      "primary": true,
      "inception_date": "1959-01-23",
      "inception_time": null,
      "inception_location": "Milwaukee, Wisconsin, USA",
      "estimated_inception": false
    }
  ],
  "notes": [
    "Used 'died_with' action to avoid assigning false agency in mutual casualty event",
    "Maria Elena Holly included as non-primary due to direct impact of spouse's death"
  ]
}
```

---

## Golden Set Example 2: Kanye West VMAs Interruption (2009)

### Article Text
"During the 2009 MTV Video Music Awards at Radio City Music Hall, Kanye West interrupted Taylor Swift's acceptance speech for Best Female Video. Swift, who won for 'You Belong With Me,' was giving her speech when West took the microphone and declared that Beyoncé had 'one of the best videos of all time' with 'Single Ladies.' The incident occurred at 9:51 PM on September 13, 2009, shocking the audience and Swift, who appeared visibly upset. West was holding a bottle of Hennessy during the interruption."

### Expected JSON Output
```json
{
  "source": "MTV News, September 14, 2009",
  "entities": [
    {
      "name": "Kanye West",
      "type": "person",
      "roles": ["performer"],
      "primary": true,
      "inception_date": "1977-06-08",
      "inception_time": null,
      "inception_location": "Atlanta, Georgia, USA",
      "estimated_inception": false
    },
    {
      "name": "Taylor Swift",
      "type": "person",
      "roles": ["performer"],
      "primary": true,
      "inception_date": "1989-12-13",
      "inception_time": null,
      "inception_location": "Reading, Pennsylvania, USA",
      "estimated_inception": false
    },
    {
      "name": "Beyoncé",
      "type": "person",
      "roles": ["performer"],
      "primary": true,
      "inception_date": "1981-09-04",
      "inception_time": null,
      "inception_location": "Houston, Texas, USA",
      "estimated_inception": false
    },
    {
      "name": "You Belong With Me",
      "type": "endeavor",
      "roles": ["endeavor"],
      "primary": true,
      "inception_date": "2009-04-18",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    },
    {
      "name": "Single Ladies",
      "type": "endeavor",
      "roles": ["endeavor"],
      "primary": true,
      "inception_date": "2008-10-13",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    },
    {
      "name": "MTV VMAs",
      "type": "event",
      "roles": ["event"],
      "primary": true,
      "inception_date": "1984-09-14",
      "inception_time": null,
      "inception_location": "Radio City Music Hall, NYC",
      "estimated_inception": false
    },
    {
      "name": "Radio City Music Hall",
      "type": "location",
      "roles": ["location"],
      "primary": true,
      "inception_date": "1932-12-27",
      "inception_time": null,
      "inception_location": "New York City, New York, USA",
      "estimated_inception": false
    },
    {
      "name": "Hennessy Bottle",
      "type": "object",
      "subtype": "beverage",
      "roles": ["object"],
      "primary": true,
      "inception_date": null,
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    },
    {
      "name": "Hennessy",
      "type": "organization",
      "roles": ["creator"],
      "primary": false,
      "relevance_reason": "Creator of primary object (Hennessy bottle) held during incident",
      "inception_date": "1765-01-01",
      "inception_time": null,
      "inception_location": "Cognac, France",
      "estimated_inception": false
    }
  ],
  "relationships": [
    {
      "entity1": "Taylor Swift",
      "entity2": "You Belong With Me",
      "relation_type": "creator",
      "both_primary": true,
      "inception_date": "2009-04-18",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    },
    {
      "entity1": "Beyoncé",
      "entity2": "Single Ladies",
      "relation_type": "creator",
      "both_primary": true,
      "inception_date": "2008-10-13",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    },
    {
      "entity1": "Kanye West",
      "entity2": "Taylor Swift",
      "relation_type": "feuding",
      "both_primary": true,
      "inception_date": "2009-09-13",
      "inception_time": "21:51",
      "inception_location": "Radio City Music Hall, NYC",
      "estimated_inception": false
    }
  ],
  "actions": [
    {
      "entity1": "Kanye West",
      "entity2": "Taylor Swift",
      "action_type": "interrupted",
      "both_primary": true,
      "inception_date": "2009-09-13",
      "inception_time": "21:51",
      "inception_location": "Radio City Music Hall, NYC",
      "estimated_inception": false
    }
  ],
  "endeavors": [
    {
      "entity": "Taylor Swift",
      "endeavor_type": "music_video",
      "endeavor_label": "You Belong With Me",
      "primary": true,
      "inception_date": "2009-04-18",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    },
    {
      "entity": "Beyoncé",
      "endeavor_type": "music_video",
      "endeavor_label": "Single Ladies",
      "primary": true,
      "inception_date": "2008-10-13",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    },
    {
      "entity": "MTV VMAs",
      "endeavor_type": "event",
      "endeavor_label": "2009 MTV Video Music Awards",
      "primary": true,
      "inception_date": "2009-09-13",
      "inception_time": "20:00",
      "inception_location": "Radio City Music Hall, NYC",
      "estimated_inception": false
    }
  ],
  "notes": [
    "Hennessy bottle is primary entity as explicitly mentioned in article",
    "Hennessy company included as non-primary (creator of primary object)"
  ]
}
```

---

## Golden Set Example 3: Celebrity "Ship" - Tom Holland & Zendaya Announcement (2021)

### Article Text
"Tom Holland and Zendaya confirmed their relationship on July 2, 2021, when paparazzi photos showed them kissing in a car in Los Angeles. The Spider-Man co-stars had been the subject of dating rumors since 2017, but had consistently denied being anything more than friends. Fans immediately began celebrating 'Tomdaya' on social media platforms, with the hashtag trending worldwide within hours of the photos surfacing."

### Expected JSON Output
```json
{
  "source": "People Magazine, July 3, 2021",
  "entities": [
    {
      "name": "Tom Holland",
      "type": "person",
      "roles": ["performer"],
      "primary": true,
      "inception_date": "1996-06-01",
      "inception_time": null,
      "inception_location": "Kingston upon Thames, England, UK",
      "estimated_inception": false
    },
    {
      "name": "Zendaya",
      "type": "person",
      "roles": ["performer"],
      "primary": true,
      "inception_date": "1996-09-01",
      "inception_time": null,
      "inception_location": "Oakland, California, USA",
      "estimated_inception": false
    },
    {
      "name": "Tomdaya",
      "type": "ship",
      "roles": ["ship"],
      "primary": true,
      "inception_date": "2021-07-02",
      "inception_time": null,
      "inception_location": "Los Angeles, California, USA",
      "estimated_inception": false
    },
    {
      "name": "Spider-Man",
      "type": "endeavor",
      "roles": ["endeavor"],
      "primary": true,
      "inception_date": "2017-07-07",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    }
  ],
  "relationships": [
    {
      "entity1": "Tom Holland",
      "entity2": "Zendaya",
      "relation_type": "romance_official_announce",
      "both_primary": true,
      "inception_date": "2021-07-02",
      "inception_time": null,
      "inception_location": "Los Angeles, California, USA",
      "estimated_inception": false
    },
    {
      "entity1": "Tom Holland",
      "entity2": "Zendaya",
      "relation_type": "romance_rumors",
      "both_primary": true,
      "inception_date": "2017-07-01",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": true
    },
    {
      "entity1": "Tom Holland",
      "entity2": "Tomdaya",
      "relation_type": "member_of",
      "both_primary": true,
      "inception_date": "2021-07-02",
      "inception_time": null,
      "inception_location": "Los Angeles, California, USA",
      "estimated_inception": false
    },
    {
      "entity1": "Zendaya",
      "entity2": "Tomdaya",
      "relation_type": "member_of",
      "both_primary": true,
      "inception_date": "2021-07-02",
      "inception_time": null,
      "inception_location": "Los Angeles, California, USA",
      "estimated_inception": false
    },
    {
      "entity1": "Tom Holland",
      "entity2": "Spider-Man",
      "relation_type": "performer",
      "both_primary": true,
      "inception_date": "2017-07-07",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    },
    {
      "entity1": "Zendaya",
      "entity2": "Spider-Man",
      "relation_type": "performer",
      "both_primary": true,
      "inception_date": "2017-07-07",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    }
  ],
  "actions": [
    {
      "entity1": "Tom Holland",
      "entity2": "Zendaya",
      "action_type": "kissed",
      "both_primary": true,
      "inception_date": "2021-07-02",
      "inception_time": null,
      "inception_location": "Los Angeles, California, USA",
      "estimated_inception": false
    }
  ],
  "endeavors": [
    {
      "entity": "Spider-Man",
      "endeavor_type": "film",
      "endeavor_label": "Spider-Man: Homecoming",
      "primary": true,
      "inception_date": "2017-07-07",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    }
  ],
  "notes": [
    "Ship entity 'Tomdaya' uses official confirmation date for natal chart",
    "Romance rumors relationship tracked separately with estimated date from 2017",
    "Ship news feed would start from first rumor date (2017)"
  ]
}
```

---

## Golden Set Example 4: Posthumous Release - Tupac Shakur

### Article Text
"Tupac Shakur's album 'Loyal to the Game' was released on December 14, 2004, eight years after his death. The album, produced by Eminem, featured previously unreleased vocals from Tupac's recording sessions between 1991-1994. Afeni Shakur, Tupac's mother and executor of his estate, approved the project."

### Expected JSON Output
```json
{
  "source": "Rolling Stone, December 15, 2004",
  "entities": [
    {
      "name": "Tupac Shakur",
      "type": "person",
      "roles": ["performer"],
      "primary": true,
      "inception_date": "1971-06-16",
      "inception_time": null,
      "inception_location": "New York City, NY, USA",
      "estimated_inception": false
    },
    {
      "name": "Loyal to the Game",
      "type": "endeavor",
      "roles": ["endeavor"],
      "primary": true,
      "inception_date": "2004-12-14",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    },
    {
      "name": "Eminem",
      "type": "person",
      "roles": ["producer"],
      "primary": true,
      "inception_date": "1972-10-17",
      "inception_time": null,
      "inception_location": "St. Joseph, Missouri, USA",
      "estimated_inception": false
    },
    {
      "name": "Afeni Shakur",
      "type": "person",
      "roles": ["parent", "executor"],
      "primary": true,
      "inception_date": "1947-01-10",
      "inception_time": null,
      "inception_location": "Lumberton, North Carolina, USA",
      "estimated_inception": false
    }
  ],
  "relationships": [
    {
      "entity1": "Tupac Shakur",
      "entity2": "Loyal to the Game",
      "relation_type": "creator",
      "both_primary": true,
      "inception_date": "1991-01-01",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": true
    },
    {
      "entity1": "Eminem",
      "entity2": "Loyal to the Game",
      "relation_type": "producer",
      "both_primary": true,
      "inception_date": "2004-01-01",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": true
    },
    {
      "entity1": "Afeni Shakur",
      "entity2": "Tupac Shakur",
      "relation_type": "parent",
      "both_primary": true,
      "inception_date": "1971-06-16",
      "inception_time": null,
      "inception_location": "New York City, NY, USA",
      "estimated_inception": false
    }
  ],
  "actions": [
    {
      "entity1": "Afeni Shakur",
      "entity2": "Loyal to the Game",
      "action_type": "approved",
      "both_primary": true,
      "inception_date": "2004-01-01",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": true
    }
  ],
  "endeavors": [
    {
      "entity": "Loyal to the Game",
      "endeavor_type": "album",
      "endeavor_label": "Loyal to the Game",
      "primary": true,
      "inception_date": "2004-12-14",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    }
  ],
  "notes": [
    "Posthumous release uses album release date for endeavor inception, not recording date",
    "Recording period estimated as 1991 for creator relationship inception"
  ]
}
```

---

## Golden Set Example 5: Band Member Changes - Van Halen

### Article Text
"Van Halen announced on September 8, 2004, that Sammy Hagar was rejoining the band for a reunion tour, replacing Gary Cherone who had left in 1999. This marked Hagar's return after his initial departure in 1996. Original singer David Lee Roth, who left in 1985, was not involved in the reunion."

### Expected JSON Output
```json
{
  "source": "Billboard, September 9, 2004",
  "entities": [
    {
      "name": "Van Halen",
      "type": "organization",
      "roles": ["performer"],
      "primary": true,
      "inception_date": "1972-01-01",
      "inception_time": null,
      "inception_location": "Pasadena, California, USA",
      "estimated_inception": true
    },
    {
      "name": "Sammy Hagar",
      "type": "person",
      "roles": ["performer"],
      "primary": true,
      "inception_date": "1947-10-13",
      "inception_time": null,
      "inception_location": "Salinas, California, USA",
      "estimated_inception": false
    },
    {
      "name": "Gary Cherone",
      "type": "person",
      "roles": ["performer"],
      "primary": true,
      "inception_date": "1961-07-26",
      "inception_time": null,
      "inception_location": "Malden, Massachusetts, USA",
      "estimated_inception": false
    },
    {
      "name": "David Lee Roth",
      "type": "person",
      "roles": ["performer"],
      "primary": true,
      "inception_date": "1954-10-10",
      "inception_time": null,
      "inception_location": "Bloomington, Indiana, USA",
      "estimated_inception": false
    }
  ],
  "relationships": [
    {
      "entity1": "Sammy Hagar",
      "entity2": "Van Halen",
      "relation_type": "member_of",
      "both_primary": true,
      "inception_date": "2004-09-08",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    }
  ],
  "actions": [
    {
      "entity1": "Van Halen",
      "entity2": "Sammy Hagar",
      "action_type": "hired",
      "both_primary": true,
      "inception_date": "2004-09-08",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    }
  ],
  "endeavors": [
    {
      "entity": "Van Halen",
      "endeavor_type": "tour",
      "endeavor_label": "Van Halen 2004 Reunion Tour",
      "primary": true,
      "inception_date": "2004-09-08",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    }
  ],
  "notes": [
    "Member_of relationship uses rejoin date, not original membership date",
    "Previous members mentioned for context but no actions extracted for past departures"
  ]
}
```

---

## Golden Set Example 6: Artist Rebranding - Prince / The Artist

### Article Text
"On June 7, 1993, Prince announced he would no longer go by his stage name, instead adopting an unpronounceable symbol as his identity. The artist, born Prince Rogers Nelson, would be referred to as 'The Artist Formerly Known as Prince' by media outlets during his dispute with Warner Bros Records."

### Expected JSON Output
```json
{
  "source": "MTV News, June 8, 1993",
  "entities": [
    {
      "name": "Prince",
      "type": "person",
      "roles": ["performer"],
      "primary": true,
      "inception_date": "1958-06-07",
      "inception_time": null,
      "inception_location": "Minneapolis, Minnesota, USA",
      "estimated_inception": false,
      "aliases": ["The Artist Formerly Known as Prince", "The Artist", "Prince Rogers Nelson"]
    },
    {
      "name": "Warner Bros Records",
      "type": "organization",
      "roles": ["record_label"],
      "primary": true,
      "inception_date": "1958-03-19",
      "inception_time": null,
      "inception_location": "Burbank, California, USA",
      "estimated_inception": false
    }
  ],
  "relationships": [
    {
      "entity1": "Prince",
      "entity2": "Warner Bros Records",
      "relation_type": "feuding",
      "both_primary": true,
      "inception_date": "1993-06-07",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    }
  ],
  "actions": [
    {
      "entity1": "Prince",
      "entity2": "Prince",
      "action_type": "rebranded",
      "both_primary": true,
      "inception_date": "1993-06-07",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    }
  ],
  "endeavors": [],
  "notes": [
    "Single entity with aliases rather than separate entities",
    "Self-referential action for rebranding"
  ]
}
```

---

## Golden Set Example 7: Pseudonym/Alter Ego - David Bowie / Ziggy Stardust

### Article Text
"David Bowie introduced his alter ego Ziggy Stardust during a January 1972 interview, ahead of the release of 'The Rise and Fall of Ziggy Stardust and the Spiders from Mars.' Bowie would perform as the androgynous alien rock star character throughout 1972 and 1973, officially retiring Ziggy at the Hammersmith Odeon on July 3, 1973."

### Expected JSON Output
```json
{
  "source": "NME, January 15, 1972",
  "entities": [
    {
      "name": "David Bowie",
      "type": "person",
      "roles": ["performer", "creator"],
      "primary": true,
      "inception_date": "1947-01-08",
      "inception_time": null,
      "inception_location": "Brixton, London, England",
      "estimated_inception": false
    },
    {
      "name": "Ziggy Stardust",
      "type": "person",
      "subtype": "alter_ego",
      "roles": ["character"],
      "primary": true,
      "inception_date": "1972-01-01",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": true,
      "relevance_reason": "Performed character treated as separate entity for astrological analysis"
    },
    {
      "name": "The Rise and Fall of Ziggy Stardust and the Spiders from Mars",
      "type": "endeavor",
      "roles": ["endeavor"],
      "primary": true,
      "inception_date": "1972-06-16",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    },
    {
      "name": "Hammersmith Odeon",
      "type": "location",
      "roles": ["location"],
      "primary": true,
      "inception_date": "1932-03-28",
      "inception_time": null,
      "inception_location": "London, England",
      "estimated_inception": false
    }
  ],
  "relationships": [
    {
      "entity1": "David Bowie",
      "entity2": "Ziggy Stardust",
      "relation_type": "creator",
      "both_primary": true,
      "inception_date": "1972-01-01",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": true
    },
    {
      "entity1": "David Bowie",
      "entity2": "Ziggy Stardust",
      "relation_type": "performer",
      "both_primary": true,
      "inception_date": "1972-01-01",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception":