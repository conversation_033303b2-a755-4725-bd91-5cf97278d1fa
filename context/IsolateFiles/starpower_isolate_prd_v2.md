# Star Power: "Isolate" Component PRD

_Last updated: January 27, 2025_

## Table of Contents

1. [Overview](#1-overview)
2. [Component Mission & Philosophy](#2-component-mission--philosophy)
3. [Entity and Relationship Extraction Design](#3-entity-and-relationship-extraction-design)
4. [Isolate Output Schema](#4-isolate-output-schema)
5. [Canonical Classification Lists](#5-canonical-classification-lists)
6. [Inclusion/Exclusion and Data Handling Guidelines](#6-inclusionexclusion-and-data-handling-guidelines)
7. [Golden Set (Canonical Examples)](#7-golden-set-canonical-examples)
8. [Success Metrics](#8-success-metrics)
9. [Next Steps & Revision Notes](#9-next-steps--revision-notes)

## 1. Overview

Star Power is an astrology-focused news app leveraging AI to extract, systematize, and analyze the natal data (date, time, location) of people, works, organizations, and major events in celebrity and cultural news. The core "Isolate" module converts news text into structured data, enabling astrological analysis, chart generation, and synastry logic across a wide spectrum of content (1920s–present).

## 2. Component Mission & Philosophy

**Mission:**
Extract precise natal chart data (inception dates, times, locations) from celebrity news to enable synastry, trend, and event analysis for professional and enthusiast astrologers.

**Foundational Principles:**

- **Historical Compatibility:** Entity/relationship types and roles must generalize across eras and pop culture formats (1920s-2020s)
- **Simplicity > Over-Segmentation:** Prefer clear category assignment, a small number of contextual roles, and binary or fallback flags over probabilistic/confidence scoring to maximize interpretability and dataset quality
- **Astrological Utility:** Only extract entity/action/relationship data that is meaningful in a natal or event astrology context

## 3. Entity and Relationship Extraction Design

### 3.1 Entities

- **Definition:** People, organizations, objects, events, ships (celebrity combos), works, and locations explicitly or implicitly central to an article
- **Primary Entities:** Directly mentioned or fundamentally involved in the article
- **Non-Primary Entities:** Included if:
  - (a) Directly connected to primary entities
  - (b) Can be labeled by a traditional Hellenistic house noun (parent, rival, collaborator)
  - (c) Add meaningful context to the event
- **Data Required:** Name, type, (subtype for objects), contextual roles (can be multiple), inception date/time/location (nullable), estimated flag

### 3.2 Celebrity "Ships" as Entities

Recognizing that fan culture creates composite entities from celebrity relationships, we treat "Ships" (Bennifer, Romione, Tomdaya) as their own entity type with dedicated profiles and timelines.

**Ship Entity Structure:**
- Individual celebrities have `dating`/`romance_official_announce` relationships with each other
- Both celebrities have `member_of` relationship with the Ship entity
- Ship entity gets its own natal chart:
  - Uses first credible rumor date for ship news feed start
  - Uses official confirmation date (if available) for actual natal chart
  - Ships that never confirm remain in `romance_rumors` status
- Ship timeline captures relationship-specific news, rumors, milestones

### 3.3 Relationships (Ongoing States)

- **Definition:** Durative associations between entities that persist over time
- **Relationship Types:** spouse, parent, child, collaborator, feuding, member_of, romance_rumors, romance_official_announce, performer, creator, manager, agent, represented_by, mentor, protege, rival, dating, separated, divorced
- **Attributes:** Parties, type, inception date/time/location, both_primary (true if both parties are primaries), estimated flag

### 3.4 Actions (Singular Events)

- **Definition:** Specific moments in time with exact inception data
- **Action Types:** interrupted, kissed, killed, died_with, announced, awarded, hired, fired
- **Attributes:** Actor, target, type, date/time/location, both_primary
- **Special Cases:** For mutual casualties (plane crashes, disasters), use `died_with` to avoid assigning false agency

### 3.5 Endeavors

- **Definition:** Major projects, releases, or significant activities tied to entities - an umbrella term that includes works but also side projects, business ventures, and life projects
- **Examples:** Albums, films, sports seasons, events, raising a child, running a business
- **Attributes:** Entity, type, label, primary, inception date/time/location, estimated flag
- **Note:** "Endeavor" is used everywhere instead of "work" to avoid overlap and confusion

## 4. Isolate Output Schema

```json
{
  "source": "Publication Name, Date",
  "entities": [
    {
      "name": "string",
      "type": "person|organization|object|event|endeavor|ship|location",
      "subtype": "string", // only for objects - see complete list in section 5.2
      "roles": ["role1", "role2"], // can be multiple
      "primary": true|false,
      "inception_date": "YYYY-MM-DD" | null,
      "inception_time": "HH:MM" | null,
      "inception_location": "City, State/Country" | null,
      "estimated_inception": true|false,
      "relevance_reason": "string" // only for non-primary entities
    }
  ],
  "relationships": [
    {
      "entity1": "string",
      "entity2": "string",
      "relation_type": "string", // from canonical list
      "both_primary": true|false,
      "inception_date": "YYYY-MM-DD" | null,
      "inception_time": "HH:MM" | null,
      "inception_location": "string" | null,
      "estimated_inception": true|false
    }
  ],
  "actions": [
    {
      "entity1": "string",
      "entity2": "string",
      "action_type": "string", // from canonical list
      "both_primary": true|false,
      "inception_date": "YYYY-MM-DD",
      "inception_time": "HH:MM" | null,
      "inception_location": "string" | null,
      "estimated_inception": true|false
    }
  ],
  "endeavors": [
    {
      "entity": "string",
      "endeavor_type": "string", // from canonical list
      "endeavor_label": "string",
      "primary": true|false,
      "inception_date": "YYYY-MM-DD" | null,
      "inception_time": "HH:MM" | null,
      "inception_location": "string" | null,
      "estimated_inception": true|false
    }
  ],
  "notes": ["string"] // implementation notes and edge case explanations
}
```

## 5. Canonical Classification Lists

### 5.1 Entity Types
- person
- organization
- object (requires subtype)
- event
- endeavor (replaces "work")
- ship (celebrity couple entity)
- location

### 5.2 Object Subtypes with Astrological Correspondences

| Subtype | Examples | Primary Planet | Secondary | House |
|---------|----------|----------------|-----------|-------|
| **beverage** | Wine, coffee, cocktails | Neptune | Moon | 12th |
| **vehicle** | Cars, planes, boats | Mercury | Mars | 3rd/9th |
| **instrument** | Guitar, piano, microphone | Venus | Mercury | 5th |
| **award** | Oscar, Grammy, trophy | Jupiter | Sun | 10th |
| **jewelry** | Rings, necklaces, watches | Venus | Sun | 2nd |
| **weapon** | Guns, knives, swords | Mars | Pluto | 8th |
| **clothing** | Dresses, suits, shoes | Venus | Mercury | 1st |
| **technology** | Phones, computers, cameras | Uranus | Mercury | 11th |
| **document** | Contracts, scripts, letters | Mercury | Saturn | 3rd |
| **food** | Meals, ingredients, snacks | Moon | Venus | 4th |
| **furniture** | Chairs, beds, tables | Saturn | Venus | 4th |
| **art** | Paintings, sculptures | Venus | Neptune | 5th |
| **substance** | Drugs, medicines | Neptune | Pluto | 12th |
| **tool** | Hammers, brushes | Mars | Mercury | 6th |
| **container** | Boxes, bags, bottles | Moon | Saturn | 4th |
| **sport_equipment** | Balls, rackets | Mars | Jupiter | 5th |
| **toy** | Dolls, games, puzzles | Moon | Mercury | 5th |
| **plant** | Flowers, trees, herbs | Venus | Moon | 6th |
| **animal** | Pets, wildlife | Moon | | 6th |
| **money** | Cash, checks, coins | Venus | Jupiter | 2nd |
| **book** | Novels, textbooks | Mercury | Jupiter | 9th |
| **other** | Uncategorized items | | | |

**Usage Notes:**
- Primary planet is the main astrological association
- Secondary planet provides additional context
- House placement indicates life area of influence
- Use for synastry analysis between object and person

### 5.3 Roles (Contextual, Multiple Allowed)
- performer
- creator
- public_figure
- executive
- collaborator
- pilot
- spouse
- parent
- child
- presenter
- manager
- agent
- mentor
- protege
- athlete
- politician
- influencer
- fan
- victim
- witness

### 5.4 Relationship Types
- spouse
- parent
- child
- sibling
- collaborator
- feuding
- member_of
- romance_rumors
- romance_official_announce
- dating
- separated
- divorced
- performer
- creator
- manager
- agent
- represented_by
- mentor
- protege
- rival
- friend
- employer
- employee

### 5.5 Action Types
- interrupted
- kissed
- killed
- died_with
- announced
- awarded
- hired
- fired
- married
- divorced
- born
- adopted
- arrested
- sued
- attacked
- defended
- praised
- criticized
- met
- departed

### 5.6 Endeavor Types
- album
- single
- music_video
- film
- tv_show
- book
- podcast
- tour
- event
- product_launch
- company_foundation
- charity_work
- political_campaign
- sports_season
- art_exhibition
- fashion_line
- restaurant
- real_estate
- child_rearing

## 6. Inclusion/Exclusion and Data Handling Guidelines

### 6.1 Primary Entity Rules
- **Always include if:** Central to the story or explicitly/implicitly referenced in the article
- **Special case:** Objects explicitly mentioned (like "Hennessy bottle") are primary entities

### 6.2 Non-Primary Entity Rules
- **Include if:** 
  - Directly connected to primary entities AND
  - Describable by Hellenistic house noun relationship AND
  - Adds meaningful context
- **Auto-include:** Creators of primary objects (e.g., Hennessy company for Hennessy bottle)
- **Document with:** `relevance_reason` field explaining inclusion

### 6.3 Inception Data Handling
- **Priority order:**
  1. Explicit date/time/location from article
  2. External source lookup for missing data
  3. Article publication date as fallback
- **Endeavor inception:** Use official release date (single release, film premiere, product launch)
- **Mark as estimated:** Set `estimated_inception: true` for approximations or fallbacks

### 6.4 Location Format
- **Goal:** GPS-coordinate-ready locations for astrological calculations
- **Format:** "Specific Venue, City, State/Country" or "City, State/Country"
- **Examples:** 
  - "Radio City Music Hall, NYC"
  - "Los Angeles, California, USA"
  - "Clear Lake, Iowa, USA"

### 6.5 Name/Alias Handling
- **System needs:** Alias tracking for name changes, stage names, rebranding
- **Examples to handle:**
  - Kanye West → Ye
  - The Big Bopper = J.P. Richardson
  - Prince → The Artist Formerly Known as Prince
- **Implementation:** Track primary name with alias array (future schema addition)

### 6.6 Decision Tree for Action Attribution in Accidents/Disasters

#### Mutual Casualty Events (Plane Crashes, Natural Disasters, etc.)

```
Is this a mutual casualty event where all parties died?
├─ YES → Use "died_with" action
│   └─ entity1: Any victim
│   └─ entity2: Another victim
│   └─ Repeat for all victim pairs if needed
│
└─ NO → Is there a clear perpetrator?
    ├─ YES → Use appropriate action (killed, attacked, etc.)
    │   └─ entity1: Perpetrator
    │   └─ entity2: Victim
    │
    └─ NO → Is it an accident with a responsible party who survived?
        ├─ YES → Consider context carefully
        │   ├─ Unintentional → Use "caused_accident" or similar
        │   └─ Negligent → Use "killed" with explanatory note
        │
        └─ NO → Is it a natural disaster or "act of god"?
            ├─ YES → Create event entity as entity1
            │   └─ Example: "Hurricane Katrina" killed "Victim Name"
            │
            └─ NO → Use "died" as reflexive action or create
                    appropriate context-specific action
```

**Examples:**
- **Plane crash (all died):** Buddy Holly `died_with` Roger Peterson
- **Shooting:** Shooter `killed` Victim
- **Car accident (drunk driver survived):** Driver `killed` Passenger
- **Earthquake:** "1906 San Francisco Earthquake" `killed` Victim
- **Medical procedure gone wrong:** Doctor `caused_death_of` Patient (with note)

## 7. Data Dictionary

### Entity Types

**person**
- Definition: Individual human being, living or deceased
- Examples: Celebrities, public figures, family members
- Required inception: Birth date/time/location

**organization**
- Definition: Formal group or company with legal/social structure
- Examples: Record labels, studios, bands, corporations
- Required inception: Founding/incorporation date

**object**
- Definition: Physical item mentioned in story
- Examples: Awards, vehicles, personal items
- Required inception: Creation/manufacture date (often null)
- Special: Requires subtype classification

**event**
- Definition: Significant occurrence with specific time/place
- Examples: Award shows, concerts, accidents
- Required inception: Event start date/time

**endeavor**
- Definition: Creative work or major project
- Examples: Albums, films, businesses, tours
- Required inception: Release/launch date

**ship**
- Definition: Celebrity couple as composite entity
- Examples: Bennifer, Tomdaya, Brangelina
- Required inception: Official confirmation date (or first rumor if never confirmed)

**location**
- Definition: Geographic place or venue
- Examples: Cities, venues, landmarks
- Required inception: Founding/opening date

### Relationship Types

**spouse**
- Definition: Legally married partners
- Inception: Wedding date
- Termination: Divorce finalized date

**dating**
- Definition: Romantic relationship (non-married)
- Inception: First public date or announcement
- Termination: Breakup announcement

**romance_rumors**
- Definition: Unconfirmed romantic connection
- Inception: First media report
- Note: Can coexist with romance_official_announce

**romance_official_announce**
- Definition: Confirmed romantic relationship
- Inception: Official confirmation date
- Note: Often follows romance_rumors

**parent/child**
- Definition: Biological or adoptive parent-child relationship
- Inception: Child's birth or adoption date
- Termination: Never (even after death)

**collaborator**
- Definition: Professional creative partnership
- Inception: First project together
- Examples: Bandmates, co-stars, writing partners

**feuding**
- Definition: Ongoing public conflict
- Inception: First public incident
- Termination: Public reconciliation

**member_of**
- Definition: Formal membership in group/organization
- Inception: Joining date
- Examples: Band member, cast member, ship member

### Action Types

**died_with**
- Definition: Mutual casualty in same incident
- Usage: All victims of accidents/disasters
- Avoids: False assignment of agency

**interrupted**
- Definition: Disrupted ongoing activity
- Context: Usually public events
- Example: Award speech interruption

**killed**
- Definition: Caused death (intentional or negligent)
- Usage: Only with clear perpetrator
- Avoid: For mutual casualties

**announced**
- Definition: Made public statement
- Context: News, relationships, projects
- Timing: Exact moment if known

**kissed**
- Definition: Romantic physical contact
- Context: Relationship confirmations
- Note: Often paired with photos

## 8. Golden Set Examples

## 8. Golden Set Examples

See separate document: **StarPower_Golden_Set_2025-01-27.md** for complete JSON examples including:
- Buddy Holly Plane Crash (1959)
- Kanye West VMAs Interruption (2009)
- Tom Holland & Zendaya "Ship" Announcement (2021)
- Posthumous releases, band member changes, artist rebranding, and alter egos

## 9. Success Metricsinception": false
    }
  ],
  "actions": [
    {
      "entity1": "Tom Holland",
      "entity2": "Zendaya",
      "action_type": "kissed",
      "both_primary": true,
      "inception_date": "2021-07-02",
      "inception_time": null,
      "inception_location": "Los Angeles, California, USA",
      "estimated_inception": false
    }
  ],
  "endeavors": [
    {
      "entity": "Spider-Man",
      "endeavor_type": "film",
      "endeavor_label": "Spider-Man: Homecoming",
      "primary": true,
      "inception_date": "2017-07-07",
      "inception_time": null,
      "inception_location": null,
      "estimated_inception": false
    }
  ],
  "notes": [
    "Ship entity 'Tomdaya' uses official confirmation date for natal chart",
    "Romance rumors relationship tracked separately with estimated date from 2017",
    "Ship news feed would start from first rumor date (2017)"
  ]
}
```

## 8. Success Metrics

- **Data Quality:** >95% accuracy on natal data extraction and consistent entity/action/relationship typing
- **Regression Robustness:** >95% Golden Set match after any Isolate function or LLM updates
- **Astrological Relevance:** All extracted data supports charting, synastry, and pattern analysis
- **Historical Coverage:** Equal effectiveness on content from 1920s through present day
- **Ship Tracking:** Accurate capture of both rumor timelines and official confirmations
- **Scalability:** New events and relationship types slot into schema without structural changes

## 9. Next Steps & Revision Notes

### Immediate Actions
1. **Implement alias system** for name changes and stage names
2. **Expand Golden Set** with edge cases (posthumous releases, band member changes, rebranding)
3. **Create decision tree** for action attribution in accidents/disasters
4. **Build data dictionary** with precise definitions for all types

### Future Enhancements
1. **Source tracking:** Add quote extraction for each data point
2. **Disambiguation system:** Handle common names and multiple entities
3. **Batch processing:** Handle multiple articles about same event
4. **Version control:** Track schema and prompt evolution

### Revision History
- v2.0 (2025-01-27): Updated based on review feedback
  - Changed "killed" to "died_with" for mutual casualties
  - Clarified primary object creators auto-include rule
  - Replaced "work" with "endeavor" throughout
  - Defined location format for GPS compatibility
  - Specified endeavor inception as release date

**End of PRD**d ship entity dating (rumors vs. official)
  - Expanded object subtypes to complete list
  - Added new relationship and action types
  - Clarifie