# Star Power: Celebrity Timeline Explorer
## Complete Build-from-Scratch Specification v3.1

**Last Updated**: January 27, 2025  
**Status**: Complete Standalone Specification  
**Target**: Lovable/Replit Frontend Teams  
**Build Context**: New application, no existing codebase dependencies

---

## 🎯 Application Overview

### What Star Power Is
Star Power is a **mobile-first celebrity timeline explorer** that combines celebrity news with astrological insights. Users discover celebrity relationships through interactive timelines, follow celebrity couples (called "ships"), and explore astrological explanations for celebrity events.

### Target Audience
**Primary Users**: TikTok astrology enthusiasts (18-35) who are interested in:
- Celebrity relationship gossip and timelines
- Astrological explanations for celebrity events
- Following favorite celebrity couples through their relationship journey
- Sharing interesting celebrity-astrology discoveries

### Core Value Proposition
Transform celebrity news consumption from static article reading into **dynamic timeline exploration** with astrological context and relationship tracking.

---

## 📱 Complete Application Structure

### App Architecture
```
Star Power App
├── Splash Screen (Loading)
├── Main Navigation (Bottom Tab Bar)
│   ├── Home (Article Feed)
│   ├── Profile (User's Astrology)
│   ├── Settings (Following & Preferences)
│   └── Saved (Bookmarked Content)
├── Article Reading System
│   ├── Article Detail View
│   └── Astrological Context Display
├── Profile System
│   ├── Celebrity Profiles (with timelines)
│   ├── Ship Profiles (relationship timelines)
│   └── User Profile (personal astrology)
└── Discovery System
    ├── Hashtag Article Collections
    └── Related Content Navigation
```

### Navigation Hierarchy
```
Root Level: Bottom Navigation (4 tabs)
│
├── Home Feed
│   ├── Article Cards (tap → Article Detail)
│   └── Hero Article (double-height featured story)
│
├── Article Detail
│   ├── Astrological Header
│   ├── News Summary
│   ├── Featured People Section
│   │   ├── Celebrity Cards (tap → Celebrity Profile)
│   │   └── Ship Cards (tap → Ship Profile)
│   └── Hashtag Links (tap → Hashtag Timeline)
│
├── Celebrity Profile
│   ├── Astrological Header (sun/moon/rising)
│   ├── Vibrational Astrology Tab
│   ├── Related People Section
│   ├── Related Ships Section
│   └── Timeline View (article checkpoints)
│
├── Ship Profile
│   ├── Relationship Header (status, dates)
│   ├── Vibrational Astrology Tab
│   ├── Interactive Timeline (NEW - signature feature)
│   ├── Ship Members Section
│   └── Related Ships Section
│
├── Profile (User)
│   ├── Personal Astrology Header
│   ├── Vibrations Tab
│   ├── Stars Tab (v2)
│   ├── Houses Tab (v2)
│   └── Friends Tab (v3)
│
├── Settings
│   ├── Following Management
│   ├── Notification Preferences
│   └── App Preferences
│
└── Saved
    ├── Saved Articles
    ├── Saved Celebrities
    └── Saved Ships
```

---

## 🏗️ Screen-by-Screen Detailed Build Guide

### 1. Splash Screen
**Purpose**: App loading with celebrity news download
**Layout**: Full-screen centered content
**Build Requirements**:

```jsx
SplashScreen {
  backgroundColor: "deep-purple" (#2D1B69),
  
  centerContent: {
    logo: "Star Power" (custom font, white),
    logoSize: "48px height",
    
    animation: {
      astroGlyphs: "subtle rotating planets around logo",
      duration: "2-3 seconds loop"
    },
    
    loadingIndicator: {
      style: "thin progress bar",
      color: "gold" (#F4A261),
      width: "60% screen width",
      position: "below logo, 32px margin"
    },
    
    loadingText: {
      text: "Downloading latest celebrity news...",
      font: "14px, white, centered",
      position: "below progress bar, 16px margin"
    }
  },
  
  transition: {
    duration: "2-3 seconds maximum",
    exitAnimation: "fade to Home Feed"
  }
}
```

**Implementation Notes**:
- Must complete loading within 3 seconds max
- Progress bar should fill smoothly during actual data loading
- Logo animation should be subtle, not distracting
- Auto-transition to Home Feed when ready

### 2. Home Feed (Primary Screen)
**Purpose**: Main celebrity article discovery feed
**Layout**: Vertical scrolling list with hero article
**Build Requirements**:

```jsx
HomeFeedScreen {
  header: {
    height: "60px",
    backgroundColor: "white",
    content: {
      title: "Star Power",
      font: "24px, bold, purple",
      position: "left-aligned, 16px margin",
      
      searchIcon: {
        icon: "magnifying glass",
        size: "24px",
        color: "purple",
        position: "right-aligned, 16px margin",
        action: "tap to search (future feature)"
      }
    }
  },
  
  content: {
    scrollType: "vertical infinite scroll",
    refreshType: "pull-down to refresh",
    
    heroArticle: {
      height: "320px (double normal height)",
      layout: "full-width card",
      image: {
        height: "200px",
        style: "cover, rounded corners 12px top"
      },
      
      content: {
        padding: "16px",
        
        category: {
          badge: "🎵 MUSIC | 🎬 FILM | ⚽ SPORTS",
          style: "small pill, colored background",
          colors: {
            music: "purple background, white text",
            film: "gold background, black text", 
            sports: "blue background, white text"
          }
        },
        
        title: {
          font: "18px, bold, black",
          maxLines: "2",
          marginTop: "8px"
        },
        
        astroGlyphs: {
          display: "horizontal row",
          glyphs: "colored circle icons for planets",
          size: "20px diameter each",
          margin: "8px between glyphs",
          position: "below title",
          
          colors: {
            mars: "red (#E63946)",
            venus: "orange (#F4A261)", 
            mercury: "blue (#2A9D8F)",
            jupiter: "orange (#E9C46A)",
            saturn: "brown (#8B4513)"
          },
          
          interaction: "tap glyph → tooltip with planet explanation"
        },
        
        socialActions: {
          display: "horizontal row at bottom",
          actions: ["❤️ Like", "↗️ Share", "🔖 Save"],
          style: "text buttons, gray color",
          spacing: "24px between actions"
        }
      }
    },
    
    articleList: {
      itemHeight: "160px each",
      itemSpacing: "12px between cards",
      
      articleCard: {
        layout: "horizontal: image left, content right",
        
        image: {
          width: "100px",
          height: "100px", 
          style: "rounded corners 8px, cover"
        },
        
        content: {
          padding: "12px",
          
          category: "same as hero but smaller",
          title: {
            font: "16px, bold, black",
            maxLines: "2"
          },
          
          astroGlyphs: {
            display: "horizontal row",
            size: "16px diameter each",
            margin: "4px between"
          },
          
          socialActions: "same as hero but smaller icons"
        }
      }
    }
  },
  
  bottomNavigation: {
    height: "80px",
    backgroundColor: "white",
    borderTop: "1px light gray",
    
    tabs: [
      {
        icon: "🏠", 
        label: "Home",
        active: true
      },
      {
        icon: "👤",
        label: "Profile", 
        active: false
      },
      {
        icon: "⚙️",
        label: "Settings",
        active: false
      },
      {
        icon: "🔖",
        label: "Saved",
        active: false
      }
    ],
    
    tabStyle: {
      activeColor: "purple",
      inactiveColor: "gray",
      font: "12px"
    }
  }
}
```

**Interaction Patterns**:
- Tap article → Navigate to Article Detail View
- Tap astro glyph → Show tooltip with planet meaning
- Tap like/share/save → Social action with haptic feedback
- Pull down → Refresh feed with loading animation
- Scroll to bottom → Load more articles (infinite scroll)

### 3. Article Detail View
**Purpose**: Full article reading with astrological context
**Build Requirements**:

```jsx
ArticleDetailView {
  header: {
    height: "80px",
    backgroundColor: "deep-purple",
    
    content: {
      backButton: {
        icon: "←",
        color: "white",
        size: "24px",
        position: "left 16px",
        action: "return to previous screen"
      },
      
      planetGroups: {
        display: "horizontal row center",
        description: "Major planet groups active in this article",
        glyphs: "larger astro glyphs, white colored",
        size: "28px diameter each"
      }
    }
  },
  
  content: {
    scrollType: "vertical",
    sections: [
      {
        sectionTitle: "News Summary",
        content: {
          text: "Article summary paragraph",
          font: "16px, line-height 1.5",
          
          originalLink: {
            text: "Read full article →",
            style: "blue link button",
            action: "open external article"
          }
        }
      },
      
      {
        sectionTitle: "Astrological Significance", 
        content: {
          text: "Explanation of astrological relevance",
          font: "16px, line-height 1.5",
          backgroundColor: "light purple tint"
        }
      },
      
      {
        sectionTitle: "Featured People",
        content: {
          celebrities: {
            display: "horizontal scrolling row",
            
            celebrityCard: {
              width: "120px",
              height: "140px",
              
              image: "80x80px circular photo",
              name: "14px, centered",
              sunSign: "12px, gray, centered",
              
              action: "tap → Celebrity Profile"
            }
          },
          
          ships: {
            display: "horizontal scrolling row",
            
            shipCard: {
              width: "140px", 
              height: "120px",
              
              memberPhotos: "two 40x40px circles, overlapping",
              shipName: "14px bold (e.g., 'Tomdaya')",
              status: "12px colored pill (Rumored/Official/Ended)",
              
              action: "tap → Ship Profile"
            }
          }
        }
      },
      
      {
        sectionTitle: "Related Topics",
        content: {
          hashtags: {
            display: "wrap horizontal",
            
            hashtagPill: {
              text: "#hashtag",
              style: "rounded pill, gray background",
              action: "tap → Hashtag Article List"
            }
          }
        }
      },
      
      {
        sectionTitle: "Social",
        content: {
          engagement: "Like/Share/Save counts and buttons"
        }
      }
    ]
  }
}
```

### 4. Celebrity Profile
**Purpose**: Individual celebrity's astrological profile and timeline
**Build Requirements**:

```jsx
CelebrityProfile {
  header: {
    height: "200px",
    backgroundColor: "gradient purple to deep purple",
    
    content: {
      backButton: "top-left, white",
      
      celebrityInfo: {
        photo: "100x100px circular, centered",
        name: "24px white, centered below photo",
        
        astroSigns: {
          display: "horizontal row, centered",
          format: "☉ Leo ☽ Virgo ↗ Scorpio",
          font: "16px white",
          fallback: "If moon/rising unknown, just sun sign"
        }
      }
    }
  },
  
  tabs: {
    height: "50px",
    backgroundColor: "white",
    
    tabItems: [
      {
        label: "Vibrations",
        active: true,
        description: "Vibrational astrology data"
      }
    ],
    
    futureTabsV2: [
      "Stars (planet locations)",
      "Houses (planet positions)"
    ]
  },
  
  content: {
    sections: [
      {
        title: "Vibrations",
        content: "Vibrational astrology explanation for this celebrity"
      },
      
      {
        title: "Featured People",
        content: {
          description: "Other celebrities connected to this person",
          display: "grid of celebrity cards",
          action: "tap card → navigate to that Celebrity Profile"
        }
      },
      
      {
        title: "Related Ships", 
        content: {
          description: "Celebrity couples involving this person",
          display: "list of ship cards",
          action: "tap card → navigate to Ship Profile"
        }
      },
      
      {
        title: "Related Hashtags",
        content: {
          description: "Topics associated with this celebrity", 
          display: "wrapped hashtag pills",
          action: "tap hashtag → Hashtag Article List View"
        }
      }
    ]
  }
}
```

### 5. Ship Profile (Signature Feature)
**Purpose**: Celebrity couple relationship tracking with timeline
**Build Requirements**:

```jsx
ShipProfile {
  header: {
    height: "220px",
    backgroundColor: "gradient pink to deep pink",
    
    content: {
      backButton: "top-left, white",
      
      shipInfo: {
        memberPhotos: {
          display: "two 80x80px circles, overlapping in center",
          style: "white border around each photo"
        },
        
        shipName: {
          text: "Tomdaya, Bennifer, etc.",
          font: "24px white, centered below photos"
        },
        
        relationshipStatus: {
          display: "colored pill below name",
          
          statusTypes: {
            rumored: {
              text: "Rumored",
              color: "soft pink background"
            },
            official: {
              text: "Official", 
              color: "bright pink background"
            },
            ended: {
              text: "Ended",
              color: "muted red background"
            }
          }
        },
        
        importantDates: {
          display: "small text below status",
          format: "Rumor: Jan 2021 | Official: Jul 2021",
          font: "14px white"
        }
      }
    }
  },
  
  tabs: {
    tabItems: [
      {
        label: "Vibrations",
        description: "Ship's combined vibrational astrology"
      }
    ]
  },
  
  content: {
    sections: [
      {
        title: "Vibrations",
        content: "Combined astrological analysis for this ship"
      },
      
      {
        title: "Ship Timeline",
        description: "SIGNATURE FEATURE - Interactive relationship timeline",
        
        component: {
          layout: "horizontal scrolling timeline",
          height: "300px",
          
          timelineAxis: {
            display: "horizontal line across screen",
            color: "light gray",
            
            timeLabels: {
              format: "Jan 2021, Jul 2021, Dec 2021...",
              position: "below axis",
              font: "12px gray"
            }
          },
          
          relationshipTrack: {
            display: "colored line above axis showing status",
            colors: {
              rumored: "soft pink",
              official: "bright pink", 
              ended: "muted red"
            }
          },
          
          articleCheckpoints: {
            display: "dots on timeline",
            size: "24px diameter circles",
            color: "white with colored border",
            
            checkpointTypes: {
              major: {
                size: "32px",
                innerIcon: "⭐"
              },
              minor: {
                size: "24px", 
                innerIcon: "●"
              }
            },
            
            hoverState: {
              size: "increase by 20%",
              shadow: "add glow effect"
            }
          },
          
          interaction: {
            scroll: "horizontal swipe to move through timeline",
            tap: "tap checkpoint → article preview slides up"
          }
        },
        
        articlePreviewPanel: {
          trigger: "tap timeline checkpoint",
          animation: "slide up from bottom",
          height: "40% of screen height",
          
          content: {
            articleSummary: {
              title: "Article title",
              date: "Publication date",
              summary: "Brief article description",
              thumbnail: "Small article image"
            },
            
            actions: {
              readFull: "Read Full Article button",
              dismiss: "X close button top-right"
            }
          },
          
          dismissal: {
            methods: ["tap X button", "swipe down", "tap outside panel"]
          }
        }
      },
      
      {
        title: "Ship Members",
        content: {
          display: "cards for each person in the ship",
          action: "tap → Celebrity Profile"
        }
      },
      
      {
        title: "Related Ships",
        content: {
          display: "other celebrity couples",
          action: "tap → Ship Profile"
        }
      },
      
      {
        title: "Related Hashtags", 
        content: {
          display: "hashtag pills",
          action: "tap → Hashtag Article List"
        }
      }
    ]
  }
}
```

**Ship Timeline Interaction Details**:
```jsx
ShipTimelineInteractions {
  initialLoad: {
    dataRange: "6 months around current date",
    centerPoint: "most recent major event or current date"
  },
  
  scrolling: {
    gesture: "horizontal swipe left/right",
    velocity: "smooth momentum scrolling",
    bounds: "elastic bounce at timeline ends",
    
    lazyLoading: {
      trigger: "scroll near timeline edges",
      loadAmount: "additional 3 months of data"
    }
  },
  
  checkpointTap: {
    gesture: "single tap on checkpoint circle",
    response: {
      hapticFeedback: "light tap",
      visualFeedback: "circle briefly scales up",
      panelAnimation: {
        duration: "300ms",
        easing: "ease-out",
        motion: "slide up from bottom"
      }
    }
  },
  
  panelDismissal: {
    methods: [
      "tap X button (fade out 200ms)",
      "swipe down on panel (follow finger motion)", 
      "tap outside panel area (fade out 200ms)"
    ]
  }
}
```

### 6. User Profile  
**Purpose**: User's personal astrology information
**Build Requirements**:

```jsx
UserProfile {
  header: {
    height: "200px",
    backgroundColor: "gradient purple",
    
    content: {
      userInfo: {
        avatar: "100x100px circular (user photo or default)",
        name: "User's name, 20px white",
        
        astroSigns: {
          display: "☉ [Sun] ☽ [Moon] ↗ [Rising]",
          font: "16px white",
          note: "User enters this in onboarding"
        }
      }
    }
  },
  
  tabs: {
    v1Tabs: [
      {
        label: "Vibrations",
        content: "User's vibrational astrology profile"
      }
    ],
    
    v2Tabs: [
      {
        label: "Stars", 
        content: "Current planet locations for user"
      },
      {
        label: "Houses",
        content: "Planet positions in houses for user"
      }
    ],
    
    v3Tabs: [
      {
        label: "Friends",
        content: "Friends list and chart comparisons"
      }
    ]
  },
  
  content: {
    activeTab: "Show content based on selected tab",
    initialFocus: "Vibrations tab for v1"
  }
}
```

### 7. Settings Screen
**Purpose**: Following management and app preferences
**Build Requirements**:

```jsx
SettingsScreen {
  header: {
    title: "Settings",
    backButton: "if accessed from profile"
  },
  
  sections: [
    {
      title: "Article View History",
      content: {
        display: "list of recently viewed articles",
        itemAction: "tap → return to article"
      }
    },
    
    {
      title: "Following",
      content: {
        celebrities: {
          title: "Celebrities",
          display: "list with toggle switches",
          
          listItem: {
            photo: "40x40px circular",
            name: "Celebrity name",
            toggle: {
              style: "iOS-style switch",
              action: "follow/unfollow celebrity"
            }
          }
        },
        
        ships: {
          title: "Ships", 
          display: "list with toggle switches",
          
          listItem: {
            photos: "two 30x30px overlapping circles",
            name: "Ship name",
            toggle: "follow/unfollow ship"
          }
        },
        
        hashtags: {
          title: "Hashtags",
          display: "list with toggle switches",
          
          listItem: {
            icon: "#",
            name: "hashtag name",
            toggle: "follow/unfollow hashtag"
          }
        }
      }
    },
    
    {
      title: "Notifications", 
      content: {
        options: [
          {
            label: "Followed Ships",
            description: "New articles about ships you follow",
            toggle: "boolean setting"
          },
          {
            label: "Followed Celebrities",
            description: "New articles about celebrities you follow", 
            toggle: "boolean setting"
          },
          {
            label: "Followed Hashtags",
            description: "New articles about hashtags you follow",
            toggle: "boolean setting"
          },
          {
            label: "Hot Stories",
            description: "Trending celebrity news",
            toggle: "boolean setting"
          }
        ]
      }
    },
    
    {
      title: "Appearance",
      content: {
        dayNightMode: {
          options: ["Light", "Dark", "Auto"],
          display: "segmented control"
        }
      }
    }
  ]
}
```

### 8. Saved Screen
**Purpose**: User's bookmarked content with search
**Build Requirements**:

```jsx
SavedScreen {
  header: {
    title: "Saved",
    
    searchBar: {
      placeholder: "Search saved items...",
      style: "rounded search input",
      functionality: "live search through saved content"
    }
  },
  
  categories: [
    {
      title: "Saved Articles",
      content: {
        display: "list of article cards",
        cardStyle: "compact version of home feed cards",
        action: "tap → Article Detail View"
      }
    },
    
    {
      title: "Saved Celebrities", 
      content: {
        display: "grid of celebrity cards",
        cardStyle: "photo + name",
        action: "tap → Celebrity Profile"
      }
    },
    
    {
      title: "Saved Ships",
      content: {
        display: "list of ship cards", 
        cardStyle: "overlapping photos + ship name",
        action: "tap → Ship Profile"
      }
    }
  ],
  
  emptyStates: {
    noSavedItems: {
      icon: "🔖",
      message: "No saved items yet",
      suggestion: "Save articles and profiles to find them here"
    },
    
    noSearchResults: {
      message: "No results for '[search term]'",
      suggestion: "Try a different search term"
    }
  }
}
```

### 9. Hashtag Article List View
**Purpose**: Timeline view of articles about specific hashtag
**Build Requirements**:

```jsx
HashtagArticleListView {
  header: {
    height: "80px",
    backgroundColor: "white",
    
    backButton: "left-aligned",
    hashtagTitle: {
      text: "#hashtagname",
      font: "20px bold purple",
      position: "centered"
    }
  },
  
  content: {
    layout: "vertical timeline",
    description: "Articles about this hashtag in chronological order",
    
    timelineVisualization: {
      axis: {
        display: "vertical line on left side", 
        color: "light gray",
        width: "2px"
      },
      
      dateMarkers: {
        display: "dates along timeline axis",
        format: "Jan 2024, Feb 2024...",
        font: "12px gray"
      },
      
      articleNodes: {
        display: "article cards connected to timeline",
        
        connector: {
          style: "horizontal line from axis to card",
          length: "16px"
        },
        
        articleCard: {
          style: "compact article card",
          content: "title, thumbnail, date",
          action: "tap → Article Detail View"
        }
      }
    }
  },
  
  navigation: {
    backButton: "return to previous screen (Article Detail or Profile)"
  }
}
```

---

## 🎨 Complete Visual Design System

### Color Palette
```css
/* Primary Colors */
--primary-purple: #2D1B69;
--primary-gold: #F4A261;
--primary-white: #FFFFFF;
--primary-black: #000000;

/* Celebrity Categories */
--music-primary: #8B5CF6;     /* Purple for music */
--film-primary: #F59E0B;       /* Gold for film */
--sports-primary: #3B82F6;     /* Blue for sports */

/* Relationship Status Colors */
--ship-rumored: #FDF2F8;       /* Very light pink background */
--ship-rumored-text: #EC4899;  /* Pink text */
--ship-official: #F9A8D4;      /* Medium pink background */
--ship-official-text: #BE185D; /* Deep pink text */
--ship-ended: #FEF2F2;         /* Very light red background */
--ship-ended-text: #DC2626;    /* Red text */

/* Astrological Planet Colors */
--mars-red: #E63946;
--venus-orange: #F4A261;
--mercury-blue: #2A9D8F;
--jupiter-yellow: #E9C46A;
--saturn-brown: #8B4513;

/* UI Grays */
--gray-50: #F9FAFB;
--gray-100: #F3F4F6;
--gray-200: #E5E7EB;
--gray-300: #D1D5DB;
--gray-400: #9CA3AF;
--gray-500: #6B7280;
--gray-600: #4B5563;
--gray-700: #374151;
--gray-800: #1F2937;
--gray-900: #111827;
```

### Typography System
```css
/* Font Family */
font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

/* Font Sizes */
--text-xs: 12px;      /* Small labels, captions */
--text-sm: 14px;      /* Body text, descriptions */
--text-base: 16px;    /* Standard body text */
--text-lg: 18px;      /* Card titles, emphasis */
--text-xl: 20px;      /* Section headers */
--text-2xl: 24px;     /* Page titles, names */
--text-3xl: 30px;     /* Hero elements */

/* Font Weights */
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;

/* Line Heights */
--leading-tight: 1.25;
--leading-normal: 1.5;
--leading-relaxed: 1.75;
```

### Spacing System
```css
/* Spacing Scale (use for margins, padding, gaps) */
--space-1: 4px;
--space-2: 8px;
--space-3: 12px;
--space-4: 16px;
--space-5: 20px;
--space-6: 24px;
--space-8: 32px;
--space-10: 40px;
--space-12: 48px;
--space-16: 64px;
--space-20: 80px;
--space-24: 96px;

/* Common Padding Patterns */
--padding-card: var(--space-4);           /* 16px */
--padding-section: var(--space-6);        /* 24px */
--padding-screen: var(--space-4);         /* 16px */
```

### Border Radius System
```css
--radius-sm: 4px;      /* Small elements */
--radius-md: 8px;      /* Cards, buttons */
--radius-lg: 12px;     /* Large cards */
--radius-xl: 16px;     /* Modal corners */
--radius-full: 9999px; /* Pills, circular elements */
```

### Shadow System
```css
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
```

### Astro Glyph Visual Specifications
```css
.astro-glyph {
  width: 20px;           /* Standard size (16px for compact) */
  height: 20px;
  border-radius: 50%;    /* Perfect circle */
  border: 2px solid white;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  color: white;
  margin-right: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.astro-glyph:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

/* Planet-specific styling */
.astro-glyph.mars { background-color: var(--mars-red); }
.astro-glyph.venus { background-color: var(--venus-orange); }
.astro-glyph.mercury { background-color: var(--mercury-blue); }
.astro-glyph.jupiter { background-color: var(--jupiter-yellow); color: black; }
.astro-glyph.saturn { background-color: var(--saturn-brown); }
```

### Button Styles
```css
/* Primary Button (main actions) */
.button-primary {
  background: var(--primary-purple);
  color: white;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-weight: var(--font-semibold);
  font-size: var(--text-base);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.button-primary:hover {
  background: #1E1347; /* Darker purple */
  transform: translateY(-1px);
}

/* Secondary Button (less important actions) */
.button-secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  border: 1px solid var(--gray-300);
}

/* Text Button (subtle actions) */
.button-text {
  background: none;
  color: var(--primary-purple);
  padding: var(--space-2) var(--space-4);
  border: none;
  font-weight: var(--font-medium);
  text-decoration: underline;
}
```

---

## 🔧 Technical Implementation Requirements

### Required Technologies
```json
{
  "framework": "React with TypeScript",
  "styling": "Tailwind CSS or CSS-in-JS",
  "routing": "React Router or Next.js routing",
  "stateManagement": "React Query + Zustand or Redux Toolkit",
  "animations": "Framer Motion or React Spring",
  "icons": "Lucide React or Heroicons",
  "charts": "Chart.js or D3.js (for timeline)",
  "gestures": "React Use Gesture (for timeline scrolling)"
}
```

### API Endpoints Required
```typescript
// Article System
GET /api/articles/feed                    // Home feed articles
GET /api/articles/{id}                    // Article details
GET /api/articles/{id}/related-people     // Featured people in article

// Celebrity System  
GET /api/celebrities/{id}/profile         // Celebrity profile data
GET /api/celebrities/{id}/timeline        // Celebrity article timeline
GET /api/celebrities/{id}/relationships   // Connected celebrities and ships

// Ship System
GET /api/ships/{id}/profile               // Ship profile data
GET /api/ships/{id}/timeline              // Ship relationship timeline
GET /api/ships/{id}/articles              // Articles about this ship

// Discovery System
GET /api/hashtags/{hashtag}/articles      // Articles for hashtag
GET /api/search?q={query}                 // Global search

// User System
GET /api/user/profile                     // User's astrology profile
GET /api/user/following                   // Followed entities
POST /api/user/follow                     // Follow celebrity/ship
DELETE /api/user/follow/{id}              // Unfollow entity
GET /api/user/saved                       // Saved articles/profiles
POST /api/user/save                       // Save article/profile
DELETE /api/user/save/{id}                // Remove saved item
```

### Data Models
```typescript
interface Article {
  id: string;
  title: string;
  summary: string;
  image_url: string;
  category: 'music' | 'film' | 'sports';
  published_at: string;
  astro_glyphs: PlanetGlyph[];
  featured_people: {
    celebrities: Celebrity[];
    ships: Ship[];
  };
  hashtags: string[];
  engagement: {
    likes: number;
    shares: number;
    saves: number;
  };
}

interface Celebrity {
  id: string;
  name: string;
  image_url: string;
  astro_data: {
    sun_sign: string;
    moon_sign?: string;
    rising_sign?: string;
    birth_date?: string;
    birth_time?: string;
    birth_location?: string;
  };
  related_ships: Ship[];
  related_celebrities: Celebrity[];
  article_count: number;
}

interface Ship {
  id: string;
  name: string; // "Tomdaya", "Bennifer"
  members: Celebrity[];
  status: 'rumored' | 'official' | 'ended';
  astro_data: {
    rumor_date?: string;
    official_date?: string;
    chart_date: string;
  };
  timeline_events: TimelineEvent[];
}

interface TimelineEvent {
  id: string;
  date: string;
  article_id: string;
  title: string;
  summary: string;
  significance: 'major' | 'minor';
  relationship_status?: 'rumored' | 'official' | 'ended';
}

interface PlanetGlyph {
  planet: 'mars' | 'venus' | 'mercury' | 'jupiter' | 'saturn';
  symbol: string;
  tooltip: string;
}

interface UserProfile {
  id: string;
  name: string;
  avatar_url?: string;
  astro_data: {
    sun_sign: string;
    moon_sign?: string;
    rising_sign?: string;
    birth_date?: string;
    birth_time?: string;
    birth_location?: string;
  };
  following: {
    celebrities: string[];
    ships: string[];
    hashtags: string[];
  };
  notifications: {
    followed_ships: boolean;
    followed_celebrities: boolean;
    followed_hashtags: boolean;
    hot_stories: boolean;
  };
  saved_items: {
    articles: string[];
    celebrities: string[];
    ships: string[];
  };
}
```

### Performance Requirements
```typescript
interface PerformanceTargets {
  // Loading Times
  splash_screen_max: "3 seconds";
  screen_transitions: "<300ms";
  article_loading: "<2 seconds";
  timeline_initial_load: "<1 second";
  
  // Scrolling Performance
  timeline_scrolling: "60fps on iPhone 12+";
  feed_scrolling: "60fps with infinite scroll";
  smooth_animations: "No janky transitions";
  
  // Data Loading
  cache_strategy: "Cache viewed timelines for instant return";
  prefetch_strategy: "Background load popular celebrity data";
  lazy_loading: "Timeline data loaded as needed";
  
  // Bundle Size
  initial_bundle: "<500KB gzipped";
  code_splitting: "Route-based chunks";
  image_optimization: "WebP with fallbacks";
}
```

---

## 📱 Mobile Interaction Patterns

### Gesture System
```typescript
interface GesturePatterns {
  // Timeline Interactions
  timeline_scroll: {
    gesture: "horizontal swipe left/right";
    physics: "momentum scrolling with bounce";
    feedback: "smooth following of finger";
  };
  
  timeline_tap: {
    gesture: "single tap on checkpoint";
    feedback: "haptic tap + visual scale animation";
    result: "article preview panel slides up";
  };
  
  // Panel Interactions  
  panel_dismiss: {
    gestures: [
      "swipe down on panel",
      "tap outside panel area", 
      "tap X button"
    ];
    animation: "smooth slide down with easing";
  };
  
  // Feed Interactions
  feed_refresh: {
    gesture: "pull down at top of feed";
    feedback: "loading spinner with elastic animation";
    threshold: "60px pull distance";
  };
  
  infinite_scroll: {
    trigger: "scroll within 200px of bottom";
    loading: "skeleton cards while fetching";
    feedback: "smooth append of new content";
  };
}
```

### Touch Target Specifications
```css
/* Minimum touch targets for accessibility */
.touch-target {
  min-width: 44px;
  min-height: 44px;
  padding: var(--space-2);
}

/* Timeline checkpoint sizing */
.timeline-checkpoint {
  width: 32px;
  height: 32px;
  padding: 8px; /* Extends touch area to 48px */
}

/* Navigation tab sizing */
.bottom-nav-tab {
  height: 60px;
  min-width: 60px;
  padding: var(--space-2);
}

/* Article card touch area */
.article-card {
  min-height: 120px;
  padding: var(--space-4);
  cursor: pointer;
}
```

### Animation Specifications
```typescript
interface AnimationSpecs {
  // Screen Transitions
  screen_enter: {
    duration: "300ms";
    easing: "cubic-bezier(0.4, 0, 0.2, 1)";
    motion: "slide from right";
  };
  
  screen_exit: {
    duration: "250ms"; 
    easing: "cubic-bezier(0.4, 0, 0.2, 1)";
    motion: "slide to left";
  };
  
  // Panel Animations
  panel_slide_up: {
    duration: "350ms";
    easing: "cubic-bezier(0.25, 0.46, 0.45, 0.94)";
    motion: "slide from bottom with overshoot";
  };
  
  panel_slide_down: {
    duration: "250ms";
    easing: "cubic-bezier(0.55, 0.06, 0.68, 0.19)";
    motion: "slide to bottom";
  };
  
  // Micro-interactions
  button_tap: {
    duration: "150ms";
    motion: "scale(0.95) with slight opacity change";
  };
  
  glyph_hover: {
    duration: "200ms";
    motion: "scale(1.1) with shadow increase";
  };
  
  checkpoint_tap: {
    duration: "100ms";
    motion: "scale(1.2) → scale(1.0)";
    haptic: "light impact";
  };
}
```

---

## 🚀 Implementation Roadmap

### Phase 1: Foundation (Week 1-2)
**Goal**: Core app structure and navigation

**Deliverables**:
- Splash screen with loading animation
- Bottom navigation system (4 tabs)
- Home feed with hero article and article cards
- Basic article detail view
- User profile with astrology header

**Success Criteria**:
- All screens accessible via navigation
- Article cards display properly with astro glyphs
- Smooth transitions between screens
- Responsive design works on mobile devices

**Technical Tasks**:
1. Set up React project with TypeScript and Tailwind
2. Implement routing system
3. Create base component library (buttons, cards, layouts)
4. Build color system and typography
5. Implement bottom navigation with active states
6. Create article card component with all variants
7. Add astro glyph component with tooltip system
8. Build splash screen with loading animation

### Phase 2: Profile System (Week 3-4)
**Goal**: Celebrity and ship profiles with basic functionality

**Deliverables**:
- Celebrity profile pages with astro headers
- Ship profile pages with relationship status
- Basic timeline visualization (horizontal scroll)
- Profile-to-profile navigation
- Featured people and related ships sections

**Success Criteria**:
- Profile pages render with correct astrology data
- Navigation between profiles works smoothly
- Timeline scroll interaction is responsive
- Profile sections (featured people, ships) are interactive

**Technical Tasks**:
1. Build BaseProfile component for reuse
2. Create celebrity profile with astro header
3. Create ship profile with relationship status
4. Implement basic horizontal timeline component
5. Add profile navigation system
6. Build featured people grid component
7. Create ship status indicator components
8. Add profile-to-profile linking

### Phase 3: Timeline Feature (Week 5-6)
**Goal**: Interactive ship timeline with article checkpoints

**Deliverables**:
- Full ship timeline with checkpoints
- Article preview panel (slide-up)
- Timeline scrolling with momentum
- Checkpoint tap interactions
- Article preview panel with dismiss gestures

**Success Criteria**:
- Timeline scrolls smoothly with momentum
- Checkpoints respond to taps with haptic feedback
- Article preview panel slides up/down smoothly
- Panel dismissal works with multiple gestures
- Timeline performs at 60fps on target devices

**Technical Tasks**:
1. Implement timeline axis and checkpoint rendering
2. Add horizontal scroll physics with momentum
3. Create article checkpoint components (major/minor)
4. Build slide-up panel component with animations
5. Implement panel dismiss gestures (swipe, tap outside)
6. Add timeline data loading and caching
7. Optimize timeline performance for smooth scrolling
8. Add timeline zoom functionality (bonus)

### Phase 4: Content Discovery (Week 7-8)
**Goal**: Settings, saved items, and hashtag discovery

**Deliverables**:
- Settings screen with following management
- Saved screen with search functionality
- Hashtag article list views
- Following/unfollowing system
- Search functionality in saved items

**Success Criteria**:
- Users can follow/unfollow celebrities and ships
- Settings toggles work properly
- Saved items are searchable and organized
- Hashtag views show articles in timeline format
- All navigation flows work end-to-end

**Technical Tasks**:
1. Build settings screen with toggle components
2. Implement following system with backend integration
3. Create saved items screen with categories
4. Add search functionality for saved content
5. Build hashtag article list with timeline view
6. Implement follow/unfollow API calls
7. Add notification preference system
8. Create empty states for all screens

### Phase 5: Polish & Performance (Week 9-10)
**Goal**: Final polish, performance optimization, and testing

**Deliverables**:
- Performance optimization for all interactions
- Enhanced animations and micro-interactions
- Comprehensive error handling
- Accessibility improvements
- Final visual polish

**Success Criteria**:
- App meets all performance targets
- Animations are smooth and delightful
- Error states are handled gracefully
- App is accessible to users with disabilities
- Visual design matches specification exactly

**Technical Tasks**:
1. Performance audit and optimization
2. Add loading states and error boundaries
3. Implement comprehensive error handling
4. Add accessibility labels and keyboard navigation
5. Polish all animations and transitions
6. Add haptic feedback where appropriate
7. Test on multiple device sizes
8. Final visual design adjustments

---

## ✅ Success Criteria & Testing

### User Experience Validation
```typescript
interface UXValidation {
  // Core User Flows
  onboarding_completion: ">90% users complete setup";
  timeline_engagement: ">60% users interact with ship timelines";
  profile_navigation: ">80% users navigate between profiles";
  content_discovery: ">70% users explore hashtag content";
  
  // Performance Validation
  load_times: "All screens load within target times";
  animation_smoothness: "60fps on iPhone 12 and equivalent Android";
  timeline_responsiveness: "Checkpoints respond within 100ms";
  
  // Content Engagement
  article_completion: ">50% users read full articles";
  ship_following: ">40% users follow at least one ship";
  celebrity_following: ">60% users follow celebrities";
  
  // Technical Validation
  crash_rate: "<1% crash rate";
  error_handling: "Graceful degradation for all error states";
  accessibility: "WCAG 2.1 AA compliance";
}
```

### Device Testing Matrix
```typescript
interface TestingMatrix {
  // iOS Devices
  ios_primary: ["iPhone 14", "iPhone 13", "iPhone 12"];
  ios_secondary: ["iPhone SE", "iPhone 11"];
  
  // Android Devices  
  android_primary: ["Samsung Galaxy S23", "Google Pixel 7"];
  android_secondary: ["Samsung Galaxy A54", "OnePlus Nord"];
  
  // Screen Sizes
  screen_sizes: [
    "375x667 (iPhone SE)",
    "390x844 (iPhone 14)", 
    "412x915 (Android Large)"
  ];
  
  // Performance Targets
  performance_test_device: "iPhone 12 / Samsung Galaxy S21";
  minimum_device: "iPhone 11 / Samsung Galaxy A52";
}
```

---

This complete specification provides everything needed to build Star Power from scratch, with detailed visual descriptions, interaction patterns, technical requirements, and implementation guidance suitable for Lovable/Replit development teams.

<function_calls>
<invoke name="TodoWrite">
<parameter name="todos">[{"id": "1", "content": "Review current frontend architecture and identify integration points for Isolate data", "status": "completed", "priority": "high"}, {"id": "2", "content": "Analyze Entity Relationship visualization requirements and component architecture", "status": "completed", "priority": "high"}, {"id": "3", "content": "Design Ship entity tracking system and celebrity couple timeline components", "status": "completed", "priority": "high"}, {"id": "4", "content": "Plan enhanced celebrity prominence system with multi-layer highlighting", "status": "pending", "priority": "medium"}, {"id": "5", "content": "Architect Timeline & Temporal visualization components for relationship evolution", "status": "completed", "priority": "high"}, {"id": "6", "content": "Design progressive complexity system for relationship network navigation", "status": "completed", "priority": "medium"}, {"id": "7", "content": "Plan API integration strategy for new Isolate endpoints", "status": "completed", "priority": "high"}, {"id": "8", "content": "Design performance optimization strategy for complex relationship graphs", "status": "completed", "priority": "medium"}, {"id": "9", "content": "Create explicit user flow diagram from screen to screen", "status": "completed", "priority": "high"}, {"id": "10", "content": "Identify component reuse opportunities across Celebrity and Ship profiles", "status": "completed", "priority": "medium"}, {"id": "11", "content": "Assess impact of removing Investment/Lifestyle content categories", "status": "completed", "priority": "medium"}, {"id": "12", "content": "Create comprehensive reduced UI specification document for Lovable/Replit teams", "status": "completed", "priority": "high"}, {"id": "13", "content": "Create standalone v3.1 specification with extensive supporting detail for building from scratch", "status": "completed", "priority": "high"}]