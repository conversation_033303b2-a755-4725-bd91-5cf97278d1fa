# Star Power: The Interactive Astrological Event Explorer

**Product:** Frontend Specification v2.0
**Last Updated:** August 4, 2025
**Status:** Ready for Frontend Development

## 1. Elevator Pitch

An interactive news reader that visualizes the hidden astrological connections between the people, places, and events shaping our world.

## 2. Executive Summary

<PERSON> Power transcends the traditional news reader by incorporating deep astrological context into current events. It utilizes a sophisticated AI pipeline to analyze news articles, identifying key entities (people, organizations, objects, events) and mapping the relationships and actions that connect them.

This structured data powers our core feature: the **Event Graph**, an interactive visualization that allows users to explore the cosmic web behind any story. Users can navigate from an article to a graph of its key players, dive into the profile of any entity to see its unique astrological chart and history, and trace the timeline of relationships and major endeavors.

## 3. What is the job to be done?

- Astrologers and enthusiasts love using celebrity and world events to explore and validate their craft, but lack the time and tools to perform deep, relational analysis on daily news.
- Pop culture fans are increasingly interested in astrology but find traditional chart analysis intimidating and disconnected from the narratives they follow.
- We utilize AI to create a content-driven astrology product that reveals the hidden connections within a story, making complex astrological concepts accessible and engaging through interactive visualization.

## 4. Core Product Experience

The application is built around a central user flow: moving from a familiar news article into a rich, explorable graph of its underlying astrological dynamics.

### Key Features

*   **Interactive Event Graph:** For each article, the system generates a visual network of all involved entities (people, places, things) and their relationships (e.g., `feuding`, `collaborator`, `spouse`). This is the primary way users "dive deeper" into a story.
*   **Unified Entity Profiles:** Every entity, whether it's a person (`Taylor Swift`), an endeavor (`The Eras Tour`), or a "ship" (`Tomdaya`), has its own profile page. This page features its inception chart, a chronological timeline of every article it has appeared in, and its defining relationships.
*   **Relational Timelines:** Users can explore the history of a specific relationship between two entities. For example, they can view a timeline showing every event in the "feuding" relationship between Kanye West and Taylor Swift.
*   **Dynamic Astro Glyphs:** The iconic, color-coded planet icons remain a key feature, providing an at-a-glance summary of the major astrological influences on any article, entity, or event.

### Primary User Flows

**1. The Daily Check-in Flow**
*As a regular user, I want to browse the latest astrologically-relevant news.*
1.  I open the app to a list of articles, each highlighted with prominent Astro Glyphs indicating the key planetary themes.
2.  I see that articles about major celebrities or events have colored accent bars, making them stand out.
3.  I tap an article that interests me, like "Kanye West Interrupts Taylor Swift at VMAs."

**2. The Article Deep Dive Flow**
*As an astrology enthusiast, I want to understand the astrological significance of an event.*
1.  After tapping the article, I read the brief news summary.
2.  I tap the "Explore Event Graph" button.
3.  The view transitions to a clean, interactive graph showing nodes for `Kanye West`, `Taylor Swift`, `Beyoncé`, the `MTV VMAs`, etc.
4.  Lines connect the nodes, labeled with relationships like `interrupted` and `feuding`.
5.  I can see the Astro Glyphs for the event itself, showing the dominant cosmic energies at that moment.

**3. The Entity Exploration Flow**
*As a user interested in a specific celebrity, I want to learn more about them.*
1.  From the Event Graph, I tap on the `Taylor Swift` node.
2.  I am taken to her profile page, which displays her basic natal chart info (Sun, Moon, Rising) and a timeline of all articles she has been featured in.
3.  I can tap on a relationship, like "feuding with Kanye West," to see a dedicated timeline of just their interactions.
4.  From her profile, I can choose to "Follow" her, so new articles about her appear in my personalized "My Stories" feed.

## 5. Aesthetic & Design Direction

We will build upon the established visual identity, extending it to the new interactive components.

### UX Component Aesthetic
*   **Foundation:** Material Design 3, adapted for our unique iconography and flat aesthetic.
*   **Key Constraint:** Continue to avoid default elevation/shadows in favor of a color-based hierarchy. Use solid background colors and layered geometric shapes.

### Iconography & Color
*   **Inspiration:** The "paper-cut," minimalist aesthetic of artists like **Mary Blair** and **Eiko Ojala** remains our north star.
*   **Astro Glyphs:** These are our signature. They will be used consistently across the app to represent planetary energies. They should be 1.5x larger and use colored spheres for planets.
*   **Color System:** A bold, limited, high-contrast palette. Dynamic color theming is enabled, where UI elements can adopt accent colors from the story or entity being viewed.

### New Component Design Language

*   **Event Graph Visualization:**
    *   **Inspiration:** Look to clean, minimalist network graphs like Obsidian's graph view or Kumu.io, but adapted to our flat, paper-cut style.
    *   **Nodes:** Entities should be represented by simple circles or squares containing their identifying icon (e.g., a person icon for a celebrity) and a clear label.
    *   **Links:** Relationships should be simple, clean lines connecting the nodes, with a small, legible text label on the line itself.
    *   **Interaction:** Tapping a node should bring it into focus and reveal a small info card with the option to navigate to its full profile. The graph should be pannable and zoomable.

*   **Timelines:**
    *   A vertical, single-column list of "Action Cards."
    *   Each card represents a specific event and contains a headline, a date, and the relevant Astro Glyphs for that moment in time.

## 6. Implementation Priority

This outlines the phased approach to building the v2 frontend.

**Phase 1: Core Navigation & Layout**
*   [ ] Implement the main app shell with horizontal top navigation and a 4-tab bottom navigation (`Home`, `Search`, `My Stories`, `Profile`).
*   [ ] Establish the Material Design 3 color system and typography.

**Phase 2: Article List & Detail View**
*   [ ] Build the article list view, displaying article cards with headlines and Astro Glyph previews.
*   [ ] Create the basic article detail view with the news summary.

**Phase 3: The Event Graph & Entity Profiles**
*   [ ] **Implement the interactive Event Graph visualization.** This is the highest priority new feature.
*   [ ] **Build the unified Entity Profile page,** including the entity's info, astrological data, and a placeholder for their timeline.
*   [ ] Ensure seamless navigation from an article to its graph, and from a graph node to its entity profile.

**Phase 4: Timelines & User Features**
*   [ ] Populate the entity profile pages with the chronological timeline of articles.
*   [ ] Implement following/unfollowing functionality for entities and hashtags.
*   [ ] Build the "My Stories" page to display content from followed entities.

**Phase 5: Polish & Optimization**
*   [ ] Add smooth animations, loading states, and error handling.
*   [ ] Implement pull-to-refresh, infinite scroll, and offline caching.
