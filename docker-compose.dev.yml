# Star Power Astrology Application - Development Services
# This docker-compose file provides PostgreSQL and Redis for local development

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: starpower_postgres_dev
    environment:
      POSTGRES_DB: starpower_dev
      POSTGRES_USER: starpower
      POSTGRES_PASSWORD: starpower_dev_pass
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U starpower -d starpower_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    networks:
      - starpower_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: starpower_redis_dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./scripts/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 5s
    restart: unless-stopped
    networks:
      - starpower_network

  # PostgreSQL Admin (Optional - for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: starpower_pgadmin_dev
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - starpower_network
    profiles:
      - admin

  # Redis Commander (Optional - for Redis management)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: starpower_redis_commander_dev
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - starpower_network
    profiles:
      - admin

volumes:
  postgres_data:
    driver: local
    name: starpower_postgres_data
  redis_data:
    driver: local
    name: starpower_redis_data
  pgadmin_data:
    driver: local
    name: starpower_pgadmin_data

networks:
  starpower_network:
    driver: bridge
    name: starpower_network
