# Crown Jewel VA Algorithm Protection Documentation

## Overview

This document outlines the protection mechanisms and procedures for the crown jewel Vibrational Astrology (VA) circuit detection algorithm.

## Algorithm Details

- **File**: `services/astrology_engine/va_circuit_detector.py`
- **Line Count**: 694 lines (including comprehensive error handling)
- **Algorithm Hash**: `81a33f786bd7c84e055570fc601b8a2a08fdccb9824dc23a92785d62471c3a59`
- **Source**: Extracted from `/Users/<USER>/n8n-docker/app/services/astrology_analyzer.py` lines 360-713
- **Status**: PROTECTED - NO MODIFICATIONS ALLOWED

## Dependencies

### Core Algorithm Dependencies

1. **Python Standard Library**:
   - `typing`: Type hints for Dict, List, Set, Tuple
   - `itertools.combinations`: For planet combination enumeration
   - `hashlib`: For algorithm integrity validation
   - `math.factorial`: For combination calculations

2. **Data Structures**:
   - `EphemerisData`: Container for celestial body positions
   - `CelestialBodyPosition`: Individual planet position data

3. **Configuration Constants**:
   - `VA_ORB_TOLERANCES`: Aspect orb tolerances for VA system
   - `ASPECT_DEFINITIONS`: Aspect angles and sign classifications
   - `BASE_HARMONICS`: David Cochrane's base harmonic set [1, 5, 7, 8, 9, 11, 13]

### External Dependencies

1. **Swiss Ephemeris** (`swisseph>=2.10.0`):
   - Required for accurate planetary position calculations
   - Must be available for ephemeris data generation

2. **Testing Framework** (`pytest>=7.4.0`):
   - Required for running the 45+ VA algorithm tests
   - Critical for validation after any system changes

## Integration Points

### Input Interface

```python
def _analyze_vibrational_astrology(self, ephemeris_data: EphemerisData) -> Dict
```

- **Input**: `EphemerisData` object containing celestial body positions
- **Output**: Complete VA analysis with circuits, harmonics, and summary

### Key Methods

1. **`_calculate_harmonic_positions()`**: Transforms planetary positions into harmonic coordinates
2. **`_detect_va_circuits()`**: Core circuit detection using graph theory
3. **`_build_aspect_graph_for_harmonic()`**: Constructs aspect relationships
4. **`_is_valid_va_circuit()`**: Validates circuit completeness and sign consistency
5. **`_extract_circuit_details()`**: Extracts comprehensive circuit information

### Output Structure

```python
{
    "harmonics_analyzed": [1, 5, 7, 8, 9, 11, 13],
    "circuits_by_harmonic": {...},
    "total_circuits_found": int,
    "active_harmonics": [...],
    "harmonic_positions": {...},
    "implementation_status": "full_va_v1",
    "summary": "Human-readable summary"
}
```

## Protection Mechanisms

### 1. Integrity Validation

- **Hash Checking**: Algorithm content is hashed and validated on initialization
- **Automatic Detection**: Any modifications trigger immediate error
- **Rollback Trigger**: Failed validation prevents algorithm execution

### 2. Test Coverage Protection

- **45+ Unit Tests**: Comprehensive test suite validates algorithm behavior
- **Performance Tests**: Ensure <2s execution time requirement
- **Integration Tests**: Validate algorithm within larger system

### 3. Access Control

- **Read-Only Intent**: Algorithm should only be called, never modified
- **Encapsulation**: Core methods are private (underscore prefix)
- **Documentation**: Clear warnings about modification restrictions

## Rollback Procedures

### Emergency Rollback (If Algorithm Modified)

1. **STOP IMMEDIATELY**
   ```bash
   # Stop all services using the algorithm
   pkill -f "star-power"
   ```

2. **RESTORE FROM BACKUP**
   ```bash
   # Restore original algorithm file
   cp backup/va_circuit_detector.py services/astrology_engine/
   ```

3. **VALIDATE RESTORATION**
   ```bash
   # Verify hash matches expected value
   python -c "
   import hashlib
   with open('services/astrology_engine/va_circuit_detector.py', 'r') as f:
       content = f.read()
   hash_value = hashlib.sha256(content.encode()).hexdigest()
   expected = '81a33f786bd7c84e055570fc601b8a2a08fdccb9824dc23a92785d62471c3a59'
   print('RESTORED' if hash_value == expected else 'FAILED')
   "
   ```

4. **RUN FULL TEST SUITE**
   ```bash
   # Validate all tests pass
   python -m pytest tests/unit/test_va_circuit_detector.py -v
   python -m pytest tests/performance/test_va_performance.py -v
   ```

5. **DOCUMENT INCIDENT**
   - Record what modifications were attempted
   - Document rollback procedure execution
   - Report to development team

### Backup Creation

```bash
# Create timestamped backup
mkdir -p backup/$(date +%Y%m%d_%H%M%S)
cp services/astrology_engine/va_circuit_detector.py backup/$(date +%Y%m%d_%H%M%S)/
```

### Validation Commands

```bash
# Quick integrity check
python -c "from services.astrology_engine import VACircuitDetector; VACircuitDetector()"

# Full test validation
pytest tests/unit/test_va_circuit_detector.py -v

# Performance validation
pytest tests/performance/test_va_performance.py -v
```

## Monitoring and Alerts

### Integrity Monitoring

- Algorithm hash is checked on every initialization
- Failed validation raises immediate exception
- System logs all integrity check results

### Performance Monitoring

- Track execution time for each VA analysis
- Alert if analysis exceeds 2-second target
- Monitor memory usage during circuit detection

### Test Coverage Monitoring

- Ensure all 45+ tests continue to pass
- Monitor test execution time
- Alert on any test failures

## Contact and Escalation

### For Algorithm Issues

1. **Immediate**: Stop using the algorithm
2. **Validate**: Run integrity and test checks
3. **Restore**: Use rollback procedures if needed
4. **Report**: Document and escalate to development team

### For Performance Issues

1. **Profile**: Identify performance bottlenecks
2. **Optimize**: Apply approved optimization strategies
3. **Validate**: Re-test against timing requirements
4. **Escalate**: Request scope reduction if optimization insufficient

## Authorized Modifications

Any modifications to the crown jewel VA algorithm require:

1. **Explicit Authorization**: Written approval from project lead
2. **Backup Creation**: Full backup before any changes
3. **Test Validation**: All tests must pass before and after
4. **Hash Update**: New hash calculation and documentation
5. **Rollback Plan**: Documented procedure for reverting changes

**REMEMBER**: The VA algorithm is the crown jewel technology. Preserve it exactly.
