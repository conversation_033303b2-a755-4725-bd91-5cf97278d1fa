# Frontend URL Construction Fix Guide

**Document Purpose**: To provide a definitive, actionable guide for the engineering team to resolve the production bug causing an "API Connection Error" by standardizing the API URL construction.

**Last Updated**: August 5, 2025
**Status**: Ready for Implementation

---

## 🎯 **The Core Problem**

The application is crashing due to a `404 Not Found` error on API calls. This is caused by an inconsistent URL strategy. The `VITE_API_URL` environment variable (`http://localhost:8000/api`) includes the `/api` path, while the API client code in `starPowerApi.ts` is inconsistent—some functions include `/api` in their paths, and others do not. This makes the code brittle and hard to maintain.

## ✅ **The Solution: Standardize URL Construction**

The most robust and standard practice is to define the `baseURL` without any path segments and ensure that every API call in the client code includes the full, absolute path from the domain root (e.g., `/api/articles/`).

This approach eliminates ambiguity and makes the API client much easier to read and maintain.

### **Step 1: Normalize the Environment Variable**

This step creates a clean, path-free base URL.

1.  **File to Edit**: `frontend-integration/.env.example`
2.  **Action**: Remove the `/api` suffix.
3.  **Change From**: `VITE_API_URL=http://localhost:8000/api`
4.  **Change To**: `VITE_API_URL=http://localhost:8000`
5.  **Instruction**: The engineering team must update their local `.env` files to match this change.

### **Step 2: Make API Call Paths Explicit**

This step ensures that all client-side code uses the full, unambiguous path for every endpoint.

1.  **File to Edit**: `frontend-integration/client/src/services/starPowerApi.ts`
2.  **Action**: Go through every `apiClient` call and ensure the path string starts with the full, absolute path (e.g., `/api/...` or `/health/`).
3.  **Example Changes**:

    ```typescript
    // In starPowerApi.ts

    export const starPowerApi = {
      // Health Check
      health: {
        // Change from: apiClient.get('/health/')
        // Change to:   apiClient.get('/health/')  // No change needed, but verify
      },

      // Articles
      articles: {
        // Change from: apiClient.get('/api/articles', { params })
        // Change to:   apiClient.get('/api/articles/', { params }) // Add trailing slash for consistency
      },

      // Celebrities
      celebrities: {
        // Change from: apiClient.get('/api/celebrities', { params })
        // Change to:   apiClient.get('/api/celebrities/', { params })
      },

      // ... and so on for every other endpoint.
    };
    ```

### **Step 3 (Optional but Recommended): Harden UI Components**

To prevent any future crashes from incomplete API data, ensure all components that render API data use null-safe access and default fallbacks, as outlined in `CODING_STANDARDS.md`.

*   **Example**: Instead of `article.hashtags.slice(0, 2)`, use `(article.hashtags || []).slice(0, 2)`.

---

## 🧪 **Verification Plan**

1.  **Implement** the changes in `.env.example` and `starPowerApi.ts`.
2.  **Rebuild and restart** the frontend server.
3.  **Open the browser** to `http://localhost:5173`.
4.  **Confirm** that the page loads correctly and displays the list of articles without any errors.
