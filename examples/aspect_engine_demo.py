#!/usr/bin/env python3
"""
Aspect Engine Demo

This script demonstrates the capabilities of the new Aspect Engine,
showing how it can detect planetary aspects using different astrological traditions.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from services.astrology_engine import AspectEngine
import json


def demo_basic_aspect_detection():
    """Demonstrate basic aspect detection with default Placidus configuration."""
    print("🌟 ASPECT ENGINE DEMO - Basic Detection")
    print("=" * 50)
    
    # Create aspect engine with default Placidus configuration
    engine = AspectEngine()
    
    # Example chart data (simplified planetary positions)
    chart_data = {
        "planet_positions": {
            "sun": 0.0,      # 0° Aries
            "moon": 120.0,   # 0° Leo (trine to Sun)
            "mars": 90.0,    # 0° Cancer (square to Sun)
            "venus": 60.0,   # 0° Gemini (sextile to Sun)
            "mercury": 2.0,  # 2° Aries (conjunction to Sun)
            "jupiter": 180.0 # 0° Libra (opposition to Sun)
        }
    }
    
    print("Chart Positions:")
    for planet, position in chart_data["planet_positions"].items():
        print(f"  {planet.title()}: {position}°")
    
    print("\nDetected Aspects:")
    aspects = engine.calculate_aspects(chart_data)
    
    for aspect in aspects:
        print(f"  {aspect.planet1.title()} {aspect.aspect_name} {aspect.planet2.title()}")
        print(f"    Angle: {aspect.exact_angle}° | Orb: {aspect.orb_difference:.1f}° | Strength: {aspect.strength:.2f}")
        print(f"    Nature: {aspect.nature} | Applying: {aspect.is_applying}")
        print()


def demo_vibrational_astrology():
    """Demonstrate Vibrational Astrology with tight orbs."""
    print("🔮 VIBRATIONAL ASTROLOGY DEMO")
    print("=" * 50)
    
    # Load vibrational configuration
    config_path = "services/astrology_engine/aspect_configs/vibrational.json"
    engine = AspectEngine(config_path)
    
    # Chart with precise positions for vibrational analysis
    chart_data = {
        "planet_positions": {
            "sun": 0.0,
            "moon": 120.2,   # Very close trine (within 1° orb)
            "mars": 90.8,    # Close square (within 1° orb)
            "venus": 61.5,   # Outside sextile orb for vibrational
            "mercury": 0.3   # Very tight conjunction
        }
    }
    
    print("Vibrational Chart Positions:")
    for planet, position in chart_data["planet_positions"].items():
        print(f"  {planet.title()}: {position}°")
    
    print(f"\nVibrational Aspects (tight orbs ≤ 1°):")
    aspects = engine.calculate_aspects(chart_data)
    
    if aspects:
        for aspect in aspects:
            print(f"  {aspect.planet1.title()} {aspect.aspect_name} {aspect.planet2.title()}")
            print(f"    Angle: {aspect.exact_angle}° | Orb: {aspect.orb_difference:.2f}°")
            print()
    else:
        print("  No aspects found within tight vibrational orbs")


def demo_custom_configuration():
    """Demonstrate custom aspect configuration."""
    print("⚙️  CUSTOM CONFIGURATION DEMO")
    print("=" * 50)
    
    # Create custom aspect configuration
    custom_config = {
        "default_orb": 5.0,
        "aspects": [
            {"name": "conjunction", "angle": 0, "orb": 10, "nature": "neutral"},
            {"name": "opposition", "angle": 180, "orb": 10, "nature": "challenging"},
            {"name": "trine", "angle": 120, "orb": 8, "nature": "harmonious"},
            {"name": "square", "angle": 90, "orb": 8, "nature": "challenging"},
            {"name": "quintile", "angle": 72, "orb": 2, "nature": "creative"}
        ]
    }
    
    engine = AspectEngine()
    engine.configure_aspects(custom_config)
    
    chart_data = {
        "planet_positions": {
            "sun": 0.0,
            "moon": 73.0,    # Close to quintile (72°)
            "mars": 122.0    # Close to trine (120°)
        }
    }
    
    print("Custom Configuration:")
    for aspect_name, aspect_def in engine.get_aspect_definitions().items():
        print(f"  {aspect_name}: {aspect_def.angle}° (orb: {aspect_def.orb}°)")
    
    print("\nChart Positions:")
    for planet, position in chart_data["planet_positions"].items():
        print(f"  {planet.title()}: {position}°")
    
    print("\nDetected Aspects:")
    aspects = engine.calculate_aspects(chart_data)
    
    for aspect in aspects:
        print(f"  {aspect.planet1.title()} {aspect.aspect_name} {aspect.planet2.title()}")
        print(f"    Angle: {aspect.exact_angle}° | Orb: {aspect.orb_difference:.1f}°")
        print()


def demo_json_output():
    """Demonstrate JSON output format."""
    print("📄 JSON OUTPUT DEMO")
    print("=" * 50)
    
    engine = AspectEngine()
    
    chart_data = {
        "planet_positions": {
            "sun": 0.0,
            "moon": 120.0,
            "mars": 90.0
        }
    }
    
    aspects = engine.calculate_aspects(chart_data)
    json_output = engine.to_json(aspects)
    
    print("JSON Format Output:")
    print(json.dumps(json_output, indent=2))


def main():
    """Run all aspect engine demonstrations."""
    print("🌟 STAR POWER ASPECT ENGINE DEMONSTRATION")
    print("=" * 60)
    print()
    
    try:
        demo_basic_aspect_detection()
        print("\n" + "=" * 60 + "\n")
        
        demo_vibrational_astrology()
        print("\n" + "=" * 60 + "\n")
        
        demo_custom_configuration()
        print("\n" + "=" * 60 + "\n")
        
        demo_json_output()
        
        print("\n" + "=" * 60)
        print("✅ Aspect Engine Demo Complete!")
        print("The aspect engine successfully supports multiple astrological traditions")
        print("with configurable orbs and comprehensive aspect detection.")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
