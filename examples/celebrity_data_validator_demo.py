#!/usr/bin/env python3
"""
Celebrity Data Validator Demo

This script demonstrates the capabilities of the Celebrity Data Validator,
showing how it enhances and validates celebrity data from multiple sources.
"""

import sys
import os
import asyncio
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from services.llm_processor.celebrity_data_validator import CelebrityDataValidator
from services.llm_processor.models import CelebrityMatch
from services.database.models import CelebrityData, DataSource
from datetime import datetime


async def demo_name_alias_expansion():
    """Demonstrate name alias expansion functionality."""
    print("🔍 NAME ALIAS EXPANSION DEMO")
    print("=" * 50)
    
    validator = CelebrityDataValidator()
    
    test_names = [
        "<PERSON>",
        "<PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON>",
        "<PERSON>"
    ]
    
    for name in test_names:
        aliases = await validator._expand_name_aliases(name)
        print(f"\nOriginal: {name}")
        print(f"Aliases: {', '.join(aliases[:5])}...")  # Show first 5


async def demo_birth_date_validation():
    """Demonstrate birth date validation."""
    print("\n📅 BIRTH DATE VALIDATION DEMO")
    print("=" * 50)
    
    validator = CelebrityDataValidator()
    
    test_dates = [
        "1974-11-11",      # Valid ISO format
        "November 11, 1974",  # Valid long format
        "11/11/1974",      # Valid US format
        "1974",            # Year only
        "1800-01-01",      # Too old
        "2020-01-01",      # Too young
        "invalid-date"     # Invalid format
    ]
    
    for date_str in test_dates:
        validation = await validator._validate_birth_date(date_str)
        status = "✅ Valid" if validation['valid'] else "❌ Invalid"
        confidence = validation['confidence']
        issues = ", ".join(validation['issues']) if validation['issues'] else "None"
        
        print(f"{date_str:20} | {status} | Confidence: {confidence:.2f} | Issues: {issues}")


async def demo_context_validation():
    """Demonstrate context validation."""
    print("\n📰 CONTEXT VALIDATION DEMO")
    print("=" * 50)
    
    validator = CelebrityDataValidator()
    
    celebrity_match = CelebrityMatch(
        name="Leonardo DiCaprio",
        confidence=0.8,
        context="Environmental activist",
        profession="Actor",
        aliases=["Leo"]
    )
    
    test_articles = [
        {
            "title": "High Context Article",
            "content": """
            Leonardo DiCaprio, the famous actor known for his environmental activism,
            spoke at the climate summit yesterday. The Oscar-winning performer has
            been a vocal advocate for environmental causes. Leo has starred in many films.
            """
        },
        {
            "title": "Medium Context Article", 
            "content": """
            The climate summit featured several celebrity speakers including
            a famous actor who has been involved in environmental causes.
            """
        },
        {
            "title": "Low Context Article",
            "content": """
            This article discusses quantum physics and theoretical frameworks
            with no mention of entertainment or celebrities.
            """
        }
    ]
    
    for article in test_articles:
        confidence = await validator._validate_context(celebrity_match, article["content"])
        print(f"{article['title']:25} | Context Confidence: {confidence:.2f}")


async def demo_full_validation_workflow():
    """Demonstrate the complete validation workflow."""
    print("\n🔄 FULL VALIDATION WORKFLOW DEMO")
    print("=" * 50)
    
    # Mock the database service to avoid actual database calls
    validator = CelebrityDataValidator()
    
    # Create test celebrity matches
    test_celebrities = [
        CelebrityMatch(
            name="Leonardo DiCaprio",
            confidence=0.85,
            context="Environmental activist actor",
            aliases=["Leo", "Leonardo Wilhelm DiCaprio"],
            birth_date="1974-11-11",
            birth_location="Los Angeles, California",
            profession="Actor"
        ),
        CelebrityMatch(
            name="Taylor Swift",
            confidence=0.9,
            context="Pop music superstar",
            aliases=["T-Swift", "Tay"],
            birth_date="1989-12-13",
            birth_location="West Reading, Pennsylvania",
            profession="Singer-Songwriter"
        ),
        CelebrityMatch(
            name="Unknown Person",
            confidence=0.3,
            context="Briefly mentioned",
            aliases=[],
            birth_date=None,
            birth_location=None,
            profession=None
        )
    ]
    
    article_content = """
    Leonardo DiCaprio and Taylor Swift were both spotted at the environmental
    gala last night. The actor and singer have been longtime advocates for
    climate action. DiCaprio gave a passionate speech about renewable energy.
    """
    
    for celebrity_match in test_celebrities:
        print(f"\n--- Validating: {celebrity_match.name} ---")
        
        try:
            enhanced_celebrity, confidence = await validator.validate_and_enhance_celebrity(
                celebrity_match, article_content
            )
            
            print(f"✅ Enhanced successfully!")
            print(f"   Original Confidence: {celebrity_match.confidence:.2f}")
            print(f"   Final Confidence: {confidence:.2f}")
            print(f"   Enhanced: {enhanced_celebrity.enhanced}")
            print(f"   Data Sources: {[ds.value for ds in enhanced_celebrity.data_sources]}")
            print(f"   Birth Date: {enhanced_celebrity.birth_date}")
            print(f"   Aliases: {enhanced_celebrity.aliases[:3]}...")  # Show first 3
            
        except Exception as e:
            print(f"❌ Validation failed: {e}")


async def demo_confidence_scoring():
    """Demonstrate confidence scoring components."""
    print("\n📊 CONFIDENCE SCORING DEMO")
    print("=" * 50)
    
    validator = CelebrityDataValidator()
    
    # Test different confidence scenarios
    scenarios = [
        {
            "name": "High Confidence Scenario",
            "llm_confidence": 0.9,
            "birth_confidence": 0.8,
            "context_confidence": 0.85,
            "database_matches": [{"similarity": 0.95}]
        },
        {
            "name": "Medium Confidence Scenario", 
            "llm_confidence": 0.7,
            "birth_confidence": 0.5,
            "context_confidence": 0.6,
            "database_matches": [{"similarity": 0.7}]
        },
        {
            "name": "Low Confidence Scenario",
            "llm_confidence": 0.4,
            "birth_confidence": 0.2,
            "context_confidence": 0.3,
            "database_matches": []
        }
    ]
    
    for scenario in scenarios:
        validation_results = {"database_matches": scenario["database_matches"]}
        
        overall_confidence = validator._calculate_overall_confidence(
            scenario["llm_confidence"],
            scenario["birth_confidence"], 
            scenario["context_confidence"],
            validation_results
        )
        
        print(f"\n{scenario['name']}:")
        print(f"  LLM: {scenario['llm_confidence']:.2f} | Birth: {scenario['birth_confidence']:.2f} | Context: {scenario['context_confidence']:.2f}")
        print(f"  Database Matches: {len(scenario['database_matches'])}")
        print(f"  📈 Overall Confidence: {overall_confidence:.2f}")


async def main():
    """Run all celebrity data validator demonstrations."""
    print("🌟 CELEBRITY DATA VALIDATOR DEMONSTRATION")
    print("=" * 60)
    print()
    
    try:
        await demo_name_alias_expansion()
        await demo_birth_date_validation()
        await demo_context_validation()
        await demo_confidence_scoring()
        await demo_full_validation_workflow()
        
        print("\n" + "=" * 60)
        print("✅ Celebrity Data Validator Demo Complete!")
        print("The validator successfully enhances celebrity data through:")
        print("• Multi-source validation and confidence scoring")
        print("• Cultural name matching with alias expansion")
        print("• Birth data validation with multiple date formats")
        print("• Context validation using article content analysis")
        print("• Comprehensive error handling and graceful degradation")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(asyncio.run(main()))
