#!/usr/bin/env python3
"""
Enhanced Celebrity Database Service Demo

This script demonstrates the enhanced capabilities of the Celebrity Database Service,
showing advanced search, performance optimization, and historical accuracy tracking.
"""

import sys
import os
import asyncio
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from services.database.celebrity_db_service import (
    CelebrityDBService, 
    CelebrityMatchResult, 
    SearchMetrics
)
from services.database.models import CelebrityData, DataSource
from datetime import datetime


async def demo_enhanced_similarity_calculation():
    """Demonstrate enhanced similarity calculation."""
    print("🔍 ENHANCED SIMILARITY CALCULATION DEMO")
    print("=" * 50)
    
    service = CelebrityDBService()
    
    # Test celebrity data
    celebrity = CelebrityData(
        id="demo-id-1",
        name="<PERSON>",
        birth_date=datetime(1974, 11, 11),
        birth_location="Los Angeles, California",
        confidence_score=0.95,
        data_sources=[DataSource.ASTRO_DATABANK],
        enhanced=True,
        verified=True,
        aliases=["<PERSON>", "<PERSON>"],
        professional_name="<PERSON>"
    )
    
    # Test different query variations
    test_queries = [
        "leonardo dicaprio",      # Exact match (lowercase)
        "leo dicaprio",          # Alias match
        "leonardo wilhelm",       # Partial name match
        "dicaprio",              # Last name only
        "leonardo di caprio"     # Slight variation
    ]
    
    print("Testing similarity calculation for different queries:")
    print(f"Celebrity: {celebrity.name}")
    print(f"Aliases: {celebrity.aliases}")
    print()
    
    for query in test_queries:
        match_result = service._calculate_enhanced_similarity(query, celebrity)
        
        print(f"Query: '{query}'")
        print(f"  Similarity Score: {match_result.similarity_score:.3f}")
        print(f"  Match Type: {match_result.match_type}")
        print(f"  Confidence Factors: {', '.join(match_result.confidence_factors)}")
        print(f"  Metadata: {match_result.search_metadata.get('similarities', {})}")
        print()


async def demo_search_metrics_tracking():
    """Demonstrate search metrics tracking."""
    print("📊 SEARCH METRICS TRACKING DEMO")
    print("=" * 50)
    
    service = CelebrityDBService()
    
    # Simulate search metrics tracking
    test_searches = [
        ("leonardo dicaprio", 3, 150.5),
        ("brad pitt", 2, 120.0),
        ("angelina jolie", 4, 180.2),
        ("leonardo dicaprio", 3, 140.0),  # Repeat search
        ("tom cruise", 1, 95.5)
    ]
    
    print("Simulating search metrics tracking:")
    for query, results_count, search_time in test_searches:
        await service._track_search_metrics(query, results_count, search_time)
        print(f"  Tracked: '{query}' -> {results_count} results in {search_time}ms")
    
    print("\n✅ Search metrics tracked successfully!")
    print("In a real system, these would be stored in cache for performance analysis.")


async def demo_confidence_factors():
    """Demonstrate confidence factor analysis."""
    print("🎯 CONFIDENCE FACTORS DEMO")
    print("=" * 50)
    
    service = CelebrityDBService()
    
    # Create test celebrities with different confidence profiles
    test_celebrities = [
        {
            "name": "High Confidence Celebrity",
            "celebrity": CelebrityData(
                name="Verified Celebrity",
                confidence_score=0.95,
                data_sources=[DataSource.ASTRO_DATABANK, DataSource.WIKIPEDIA],
                enhanced=True,
                verified=True,
                aliases=["VC", "Verified Celeb"]
            ),
            "query": "verified celebrity"
        },
        {
            "name": "Medium Confidence Celebrity", 
            "celebrity": CelebrityData(
                name="Enhanced Celebrity",
                confidence_score=0.75,
                data_sources=[DataSource.LLM_ENHANCED],
                enhanced=True,
                verified=False,
                aliases=["EC"]
            ),
            "query": "enhanced celebrity"
        },
        {
            "name": "Low Confidence Celebrity",
            "celebrity": CelebrityData(
                name="Basic Celebrity",
                confidence_score=0.45,
                data_sources=[DataSource.USER_INPUT],
                enhanced=False,
                verified=False,
                aliases=[]
            ),
            "query": "basic celebrity"
        }
    ]
    
    print("Analyzing confidence factors for different celebrity profiles:")
    print()
    
    for test_case in test_celebrities:
        match_result = service._calculate_enhanced_similarity(
            test_case["query"], 
            test_case["celebrity"]
        )
        
        print(f"Celebrity: {test_case['name']}")
        print(f"  Base Confidence: {test_case['celebrity'].confidence_score:.2f}")
        print(f"  Similarity Score: {match_result.similarity_score:.3f}")
        print(f"  Verified: {test_case['celebrity'].verified}")
        print(f"  Enhanced: {test_case['celebrity'].enhanced}")
        print(f"  Data Sources: {[ds.value for ds in test_case['celebrity'].data_sources]}")
        print(f"  Confidence Factors: {', '.join(match_result.confidence_factors)}")
        print()


async def demo_batch_operations():
    """Demonstrate batch operations concept."""
    print("⚡ BATCH OPERATIONS DEMO")
    print("=" * 50)
    
    service = CelebrityDBService()
    
    # Create sample celebrities for batch operations
    sample_celebrities = []
    for i in range(5):
        celebrity = CelebrityData(
            name=f"Celebrity {i+1}",
            confidence_score=0.7 + (i * 0.05),
            data_sources=[DataSource.LLM_ENHANCED],
            enhanced=True,
            verified=i % 2 == 0,  # Every other one verified
            aliases=[f"Celeb{i+1}", f"C{i+1}"]
        )
        sample_celebrities.append(celebrity)
    
    print(f"Created {len(sample_celebrities)} sample celebrities for batch operations:")
    for celebrity in sample_celebrities:
        print(f"  {celebrity.name} (confidence: {celebrity.confidence_score:.2f}, verified: {celebrity.verified})")
    
    print(f"\nBatch size setting: {service.batch_size}")
    print("✅ In a real system, these would be processed in optimized batches")


async def demo_performance_settings():
    """Demonstrate performance optimization settings."""
    print("🚀 PERFORMANCE OPTIMIZATION DEMO")
    print("=" * 50)
    
    service = CelebrityDBService()
    
    print("Current performance settings:")
    print(f"  Cache TTL: {service.cache_ttl} seconds ({service.cache_ttl/3600:.1f} hours)")
    print(f"  Search Cache TTL: {service.search_cache_ttl} seconds ({service.search_cache_ttl/60:.1f} minutes)")
    print(f"  Fuzzy Search Limit: {service.fuzzy_search_limit} candidates")
    print(f"  Batch Size: {service.batch_size} items")
    print(f"  Name Similarity Threshold: {service.name_similarity_threshold}")
    print(f"  Search Caching Enabled: {service.enable_search_caching}")
    print(f"  Accuracy Tracking Enabled: {service.accuracy_tracking_enabled}")
    print(f"  Metrics Retention: {service.search_metrics_retention_days} days")
    
    print("\n🎯 Optimization Features:")
    print("  • Multi-level caching (celebrity data + search results)")
    print("  • Configurable similarity thresholds")
    print("  • Batch processing for bulk operations")
    print("  • Performance metrics tracking")
    print("  • Adaptive search strategies")


async def demo_search_strategies():
    """Demonstrate different search strategies."""
    print("🔎 SEARCH STRATEGIES DEMO")
    print("=" * 50)
    
    service = CelebrityDBService()
    
    # Test celebrity for demonstration
    test_celebrity = CelebrityData(
        name="Emma Watson",
        professional_name="Emma Charlotte Duerre Watson",
        confidence_score=0.92,
        data_sources=[DataSource.ASTRO_DATABANK, DataSource.WIKIPEDIA],
        enhanced=True,
        verified=True,
        aliases=["Emma", "Hermione"]
    )
    
    # Different search strategies
    search_strategies = [
        {
            "name": "Exact Name Match",
            "query": "emma watson",
            "description": "Direct name matching with case insensitivity"
        },
        {
            "name": "Professional Name Match",
            "query": "emma charlotte duerre watson",
            "description": "Full professional name matching"
        },
        {
            "name": "Alias Match",
            "query": "hermione",
            "description": "Character name or nickname matching"
        },
        {
            "name": "Partial Match",
            "query": "emma",
            "description": "Partial name matching with fuzzy logic"
        },
        {
            "name": "Fuzzy Match",
            "query": "ema watsen",
            "description": "Typo-tolerant fuzzy matching"
        }
    ]
    
    print("Testing different search strategies:")
    print(f"Target Celebrity: {test_celebrity.name}")
    print(f"Professional Name: {test_celebrity.professional_name}")
    print(f"Aliases: {test_celebrity.aliases}")
    print()
    
    for strategy in search_strategies:
        match_result = service._calculate_enhanced_similarity(
            strategy["query"].lower(), 
            test_celebrity
        )
        
        print(f"Strategy: {strategy['name']}")
        print(f"  Query: '{strategy['query']}'")
        print(f"  Description: {strategy['description']}")
        print(f"  Similarity Score: {match_result.similarity_score:.3f}")
        print(f"  Match Type: {match_result.match_type}")
        print(f"  Would Match: {'✅ Yes' if match_result.similarity_score >= service.name_similarity_threshold else '❌ No'}")
        print()


async def main():
    """Run all enhanced celebrity database service demonstrations."""
    print("🌟 ENHANCED CELEBRITY DATABASE SERVICE DEMONSTRATION")
    print("=" * 70)
    print()
    
    try:
        await demo_enhanced_similarity_calculation()
        print("\n" + "=" * 70 + "\n")
        
        await demo_search_metrics_tracking()
        print("\n" + "=" * 70 + "\n")
        
        await demo_confidence_factors()
        print("\n" + "=" * 70 + "\n")
        
        await demo_batch_operations()
        print("\n" + "=" * 70 + "\n")
        
        await demo_performance_settings()
        print("\n" + "=" * 70 + "\n")
        
        await demo_search_strategies()
        
        print("\n" + "=" * 70)
        print("✅ Enhanced Celebrity Database Service Demo Complete!")
        print("\nKey Enhancements Demonstrated:")
        print("• Advanced similarity calculation with multiple factors")
        print("• Performance optimization with caching and batching")
        print("• Historical accuracy tracking and metrics")
        print("• Multiple search strategies and confidence scoring")
        print("• Configurable thresholds and optimization settings")
        print("• Enhanced metadata and search result analysis")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(asyncio.run(main()))
