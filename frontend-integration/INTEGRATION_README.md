# Star Power Frontend Integration

This directory contains the integrated Star Power frontend that connects to the new API Gateway.

## 🚀 Quick Start

### Prerequisites
1. **API Gateway Running**: Ensure the Star Power API Gateway is running on port 8000
   ```bash
   cd /Users/<USER>/Documents/GitHub/star-power
   python run_api_gateway.py
   ```

2. **Dependencies**: Install frontend dependencies
   ```bash
   cd frontend-integration
   npm install
   ```

### Start Integration Test
```bash
./start-integration-test.sh
```

This will:
- Check API Gateway availability
- Install dependencies if needed
- Check port availability and handle conflicts
- Start the frontend development server
- Open integration test pages

### Port Configuration
The frontend server runs on port **5173** by default (configurable via `PORT` in `.env`):
- **Frontend**: http://localhost:5173
- **API Gateway**: http://localhost:8000 (separate service)

**Note**: Port 5000 is avoided due to conflicts with macOS Control Center.

## 📋 Integration Status

### ✅ Completed
- **API Client**: Complete TypeScript API client with all endpoints
- **Type Definitions**: Comprehensive types bridging existing and new data models
- **Custom Hooks**: React hooks for easy API integration
- **Error Handling**: Graceful fallback when API is unavailable
- **Health Monitoring**: Real-time API health status display
- **Environment Configuration**: Configurable API URL and settings

### 🔄 In Progress
- **Home Page Integration**: Updated to use new API with fallback to existing data
- **API Test Page**: Comprehensive testing interface for all API features

### ⏳ Pending
- **Complete UI Integration**: Update all existing components to use new API
- **Data Transformation**: Seamless mapping between API and UI data models
- **Performance Optimization**: Caching and loading state improvements

## 🏗️ Architecture

### API Integration Layer
```
frontend-integration/
├── client/src/
│   ├── services/
│   │   └── starPowerApi.ts          # Main API client
│   ├── types/
│   │   └── api.ts                   # Type definitions
│   ├── hooks/
│   │   └── useStarPowerApi.ts       # React hooks
│   └── pages/
│       ├── home.tsx                 # Updated home page
│       └── api-test.tsx             # API testing page
```

### Key Components

#### 1. API Client (`starPowerApi.ts`)
- Axios-based HTTP client
- Automatic error handling and logging
- Typed responses matching API Gateway models
- Configurable base URL and timeouts

#### 2. Custom Hooks (`useStarPowerApi.ts`)
- `useArticles()` - Article listing and filtering
- `useCelebrities()` - Celebrity data and identification
- `useAstrology()` - Astrological analysis
- `usePipeline()` - Background job processing
- `useHealth()` - API health monitoring

#### 3. Type System (`api.ts`)
- Bridges existing frontend types with new API models
- Extended types for additional API features
- Request/response wrappers
- UI state management types

## 🧪 Testing

### API Test Page
Visit `http://localhost:5173/api-test` to test:

1. **Health Check**: API Gateway status and service health
2. **Article Processing**: Process articles from URLs
3. **Celebrity Identification**: Identify celebrities from text
4. **Astrology Analysis**: Generate astrological analyses
5. **Pipeline Processing**: Background news processing

### Integration Validation

#### ✅ Success Criteria
- [ ] Frontend loads without errors
- [ ] API calls successfully reach API Gateway
- [ ] Health status displays correctly
- [ ] Article processing works end-to-end
- [ ] Celebrity identification returns results
- [ ] Astrology analysis completes successfully
- [ ] Error handling works gracefully

#### 🔧 Troubleshooting

**API Connection Errors**
- Ensure API Gateway is running: `python run_api_gateway.py`
- Check API URL in `.env`: `VITE_API_URL=http://localhost:8000/api`
- Verify CORS settings in API Gateway

**Type Errors**
- Run `npm run type-check` to validate TypeScript
- Check import paths for new API types
- Ensure all dependencies are installed

**Build Errors**
- Clear node_modules: `rm -rf node_modules && npm install`
- Check for missing UI components
- Verify environment variables

## 🔄 Migration Strategy

### Phase 1: Parallel Operation ✅
- New API client alongside existing mock server
- Graceful fallback when API unavailable
- Health monitoring and error display

### Phase 2: Component Migration (Current)
- Update existing components to use new API
- Maintain existing UI/UX patterns
- Add new features (celebrity ID, astrology)

### Phase 3: Full Integration (Next)
- Remove mock server dependencies
- Optimize performance and caching
- Add advanced features and analytics

## 🌟 New Features Available

### Celebrity Identification
```typescript
const { identify } = useCelebrities();
const results = await identify("Brad Pitt and Angelina Jolie");
```

### Astrological Analysis
```typescript
const { analyze } = useAstrology();
const analysis = await analyze(celebrityId);
```

### Pipeline Processing
```typescript
const { processNews } = usePipeline();
const jobIds = await processNews("celebrity news", 10);
```

### Real-time Health Monitoring
```typescript
const { data: health } = useHealth();
// Automatically updates every 30 seconds
```

## 📊 Performance Considerations

- **API Timeouts**: 30s for astrology analysis, 10s for other operations
- **Caching**: React Query with 5-15 minute stale times
- **Error Boundaries**: Graceful handling of API failures
- **Loading States**: Comprehensive loading and skeleton states

## 🔮 Next Steps

1. **Complete Component Migration**: Update all pages to use new API
2. **Enhanced Error Handling**: Better error messages and retry logic
3. **Performance Optimization**: Implement advanced caching strategies
4. **Feature Enhancement**: Add new UI for astrology and celebrity features
5. **Testing**: Comprehensive integration and unit tests
6. **Documentation**: API usage examples and best practices

## 🤝 Contributing

When adding new API integrations:

1. **Add Types**: Define types in `types/api.ts`
2. **Create Hooks**: Add custom hooks in `hooks/useStarPowerApi.ts`
3. **Update Client**: Add API methods to `services/starPowerApi.ts`
4. **Test Integration**: Use the API test page to validate
5. **Update Components**: Integrate with existing UI components

## 📞 Support

For integration issues:
- Check API Gateway logs for backend errors
- Use browser dev tools to inspect network requests
- Review React Query dev tools for cache state
- Test individual API endpoints using the test page
