import { Target, Calendar, MapPin, Star, Briefcase, Music, Film } from "lucide-react";
import { cn } from "@/lib/utils";
import type { IsolateEndeavor } from "@/types/api";

interface EndeavorCardProps {
  endeavor: IsolateEndeavor;
  onClick?: (endeavor: IsolateEndeavor) => void;
  compact?: boolean;
}

export default function EndeavorCard({ endeavor, onClick, compact = false }: EndeavorCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick(endeavor);
    }
  };

  const getEndeavorIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'music':
      case 'album':
      case 'song':
        return <Music className="w-4 h-4" />;
      case 'movie':
      case 'film':
      case 'tv':
        return <Film className="w-4 h-4" />;
      case 'business':
      case 'company':
        return <Briefcase className="w-4 h-4" />;
      case 'project':
      case 'collaboration':
        return <Target className="w-4 h-4" />;
      default:
        return <Target className="w-4 h-4" />;
    }
  };

  const getEndeavorColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'music':
      case 'album':
      case 'song':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'movie':
      case 'film':
      case 'tv':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'business':
      case 'company':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'project':
      case 'collaboration':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'tour':
      case 'event':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (compact) {
    return (
      <div
        className={cn(
          "inline-flex items-center px-3 py-2 rounded-lg text-sm border cursor-pointer hover:shadow-sm transition-shadow",
          getEndeavorColor(endeavor.endeavor_type),
          endeavor.primary && "ring-2 ring-yellow-300"
        )}
        onClick={handleClick}
      >
        {getEndeavorIcon(endeavor.endeavor_type)}
        <span className="ml-2 font-medium">{endeavor.endeavor_label}</span>
        <span className="ml-1 text-xs text-gray-500">({endeavor.entity})</span>
        {endeavor.primary && <Star className="w-3 h-3 ml-1 fill-current" />}
      </div>
    );
  }

  return (
    <div
      className={cn(
        "bg-white rounded-lg border p-4 cursor-pointer hover:shadow-md transition-shadow",
        endeavor.primary && "ring-2 ring-yellow-300",
        onClick && "hover:bg-gray-50"
      )}
      onClick={handleClick}
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className={cn(
            "p-2 rounded-full",
            getEndeavorColor(endeavor.endeavor_type)
          )}>
            {getEndeavorIcon(endeavor.endeavor_type)}
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{endeavor.endeavor_label}</h3>
            <p className="text-sm text-gray-500">
              <span className="capitalize">{endeavor.endeavor_type}</span>
              <span className="mx-1">•</span>
              <span>{endeavor.entity}</span>
            </p>
          </div>
        </div>
        {endeavor.primary && (
          <div className="flex items-center text-yellow-600">
            <Star className="w-4 h-4 fill-current" />
            <span className="text-xs ml-1">Primary</span>
          </div>
        )}
      </div>

      {/* Inception Information */}
      {(endeavor.inception_date || endeavor.inception_location) && (
        <div className="space-y-2 text-sm text-gray-600">
          {endeavor.inception_date && (
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4" />
              <span>
                {endeavor.inception_date}
                {endeavor.inception_time && ` at ${endeavor.inception_time}`}
                {endeavor.estimated_inception && (
                  <span className="text-gray-400 ml-1">(estimated)</span>
                )}
              </span>
            </div>
          )}
          {endeavor.inception_location && (
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4" />
              <span>{endeavor.inception_location}</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
