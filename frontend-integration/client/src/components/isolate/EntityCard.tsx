import { User, Calendar, MapPin, Star } from "lucide-react";
import { cn } from "@/lib/utils";
import type { IsolateEntity } from "@/types/api";

interface EntityCardProps {
  entity: IsolateEntity;
  onClick?: (entity: IsolateEntity) => void;
  compact?: boolean;
}

export default function EntityCard({ entity, onClick, compact = false }: EntityCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick(entity);
    }
  };

  const getEntityIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'person':
      case 'celebrity':
        return <User className="w-4 h-4" />;
      default:
        return <Star className="w-4 h-4" />;
    }
  };

  const getEntityColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'person':
      case 'celebrity':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'organization':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'location':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'event':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (compact) {
    return (
      <div
        className={cn(
          "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border cursor-pointer hover:shadow-sm transition-shadow",
          getEntityColor(entity.entity_type),
          entity.primary && "ring-2 ring-yellow-300"
        )}
        onClick={handleClick}
      >
        {getEntityIcon(entity.entity_type)}
        <span className="ml-1">{entity.name}</span>
        {entity.primary && <Star className="w-3 h-3 ml-1 fill-current" />}
      </div>
    );
  }

  return (
    <div
      className={cn(
        "bg-white rounded-lg border p-4 cursor-pointer hover:shadow-md transition-shadow",
        entity.primary && "ring-2 ring-yellow-300",
        onClick && "hover:bg-gray-50"
      )}
      onClick={handleClick}
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-2">
          <div className={cn(
            "p-2 rounded-full",
            getEntityColor(entity.entity_type)
          )}>
            {getEntityIcon(entity.entity_type)}
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{entity.name}</h3>
            <p className="text-sm text-gray-500 capitalize">{entity.entity_type}</p>
          </div>
        </div>
        {entity.primary && (
          <div className="flex items-center text-yellow-600">
            <Star className="w-4 h-4 fill-current" />
            <span className="text-xs ml-1">Primary</span>
          </div>
        )}
      </div>

      {/* Birth/Creation Information */}
      {(entity.birth_date || entity.birth_location) && (
        <div className="space-y-2 text-sm text-gray-600">
          {entity.birth_date && (
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4" />
              <span>
                {entity.birth_date}
                {entity.birth_time && ` at ${entity.birth_time}`}
                {entity.estimated_birth && (
                  <span className="text-gray-400 ml-1">(estimated)</span>
                )}
              </span>
            </div>
          )}
          {entity.birth_location && (
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4" />
              <span>{entity.birth_location}</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
