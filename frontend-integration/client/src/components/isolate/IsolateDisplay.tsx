import { useState } from "react";
import { Users, Heart, Zap, Target, ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "@/lib/utils";
import type { IsolateData } from "@/types/api";
import EntityCard from "./EntityCard";
import RelationshipCard from "./RelationshipCard";
import ActionCard from "./ActionCard";
import EndeavorCard from "./EndeavorCard";

interface IsolateDisplayProps {
  isolateData: IsolateData;
  compact?: boolean;
  showSections?: {
    entities?: boolean;
    relationships?: boolean;
    actions?: boolean;
    endeavors?: boolean;
  };
}

export default function IsolateDisplay({ 
  isolateData, 
  compact = false,
  showSections = {
    entities: true,
    relationships: true,
    actions: true,
    endeavors: true
  }
}: IsolateDisplayProps) {
  const [expandedSections, setExpandedSections] = useState({
    entities: true,
    relationships: false,
    actions: false,
    endeavors: false
  });

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const getSectionIcon = (section: string) => {
    switch (section) {
      case 'entities':
        return <Users className="w-4 h-4" />;
      case 'relationships':
        return <Heart className="w-4 h-4" />;
      case 'actions':
        return <Zap className="w-4 h-4" />;
      case 'endeavors':
        return <Target className="w-4 h-4" />;
      default:
        return null;
    }
  };

  const getSectionTitle = (section: string, count: number) => {
    const titles = {
      entities: `Entities (${count})`,
      relationships: `Relationships (${count})`,
      actions: `Actions (${count})`,
      endeavors: `Endeavors (${count})`
    };
    return titles[section as keyof typeof titles] || section;
  };

  if (compact) {
    return (
      <div className="space-y-4">
        {/* Compact Entity Summary */}
        {showSections.entities && isolateData.entities.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Key People & Entities</h4>
            <div className="flex flex-wrap gap-2">
              {isolateData.entities.slice(0, 6).map((entity, index) => (
                <EntityCard key={index} entity={entity} compact />
              ))}
              {isolateData.entities.length > 6 && (
                <span className="text-xs text-gray-500 px-2 py-1">
                  +{isolateData.entities.length - 6} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Compact Relationship Summary */}
        {showSections.relationships && isolateData.relationships.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Key Relationships</h4>
            <div className="space-y-2">
              {isolateData.relationships.slice(0, 3).map((relationship, index) => (
                <RelationshipCard key={index} relationship={relationship} compact />
              ))}
              {isolateData.relationships.length > 3 && (
                <span className="text-xs text-gray-500 px-2 py-1">
                  +{isolateData.relationships.length - 3} more relationships
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Entities Section */}
      {showSections.entities && isolateData.entities.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <button
            onClick={() => toggleSection('entities')}
            className="flex items-center justify-between w-full text-left mb-3"
          >
            <div className="flex items-center space-x-2">
              {getSectionIcon('entities')}
              <h3 className="text-lg font-semibold text-gray-900">
                {getSectionTitle('entities', isolateData.entities.length)}
              </h3>
            </div>
            {expandedSections.entities ? (
              <ChevronUp className="w-5 h-5 text-gray-500" />
            ) : (
              <ChevronDown className="w-5 h-5 text-gray-500" />
            )}
          </button>
          
          {expandedSections.entities && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {isolateData.entities.map((entity, index) => (
                <EntityCard key={index} entity={entity} />
              ))}
            </div>
          )}
        </div>
      )}

      {/* Relationships Section */}
      {showSections.relationships && isolateData.relationships.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <button
            onClick={() => toggleSection('relationships')}
            className="flex items-center justify-between w-full text-left mb-3"
          >
            <div className="flex items-center space-x-2">
              {getSectionIcon('relationships')}
              <h3 className="text-lg font-semibold text-gray-900">
                {getSectionTitle('relationships', isolateData.relationships.length)}
              </h3>
            </div>
            {expandedSections.relationships ? (
              <ChevronUp className="w-5 h-5 text-gray-500" />
            ) : (
              <ChevronDown className="w-5 h-5 text-gray-500" />
            )}
          </button>
          
          {expandedSections.relationships && (
            <div className="space-y-3">
              {isolateData.relationships.map((relationship, index) => (
                <RelationshipCard key={index} relationship={relationship} />
              ))}
            </div>
          )}
        </div>
      )}

      {/* Actions Section */}
      {showSections.actions && isolateData.actions.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <button
            onClick={() => toggleSection('actions')}
            className="flex items-center justify-between w-full text-left mb-3"
          >
            <div className="flex items-center space-x-2">
              {getSectionIcon('actions')}
              <h3 className="text-lg font-semibold text-gray-900">
                {getSectionTitle('actions', isolateData.actions.length)}
              </h3>
            </div>
            {expandedSections.actions ? (
              <ChevronUp className="w-5 h-5 text-gray-500" />
            ) : (
              <ChevronDown className="w-5 h-5 text-gray-500" />
            )}
          </button>
          
          {expandedSections.actions && (
            <div className="space-y-3">
              {isolateData.actions.map((action, index) => (
                <ActionCard key={index} action={action} />
              ))}
            </div>
          )}
        </div>
      )}

      {/* Endeavors Section */}
      {showSections.endeavors && isolateData.endeavors.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4">
          <button
            onClick={() => toggleSection('endeavors')}
            className="flex items-center justify-between w-full text-left mb-3"
          >
            <div className="flex items-center space-x-2">
              {getSectionIcon('endeavors')}
              <h3 className="text-lg font-semibold text-gray-900">
                {getSectionTitle('endeavors', isolateData.endeavors.length)}
              </h3>
            </div>
            {expandedSections.endeavors ? (
              <ChevronUp className="w-5 h-5 text-gray-500" />
            ) : (
              <ChevronDown className="w-5 h-5 text-gray-500" />
            )}
          </button>
          
          {expandedSections.endeavors && (
            <div className="space-y-3">
              {isolateData.endeavors.map((endeavor, index) => (
                <EndeavorCard key={index} endeavor={endeavor} />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
