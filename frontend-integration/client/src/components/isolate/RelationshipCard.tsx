import { Heart, Users, Calendar, MapPin, ArrowRight } from "lucide-react";
import { cn } from "@/lib/utils";
import type { IsolateRelationship } from "@/types/api";

interface RelationshipCardProps {
  relationship: IsolateRelationship;
  onClick?: (relationship: IsolateRelationship) => void;
  compact?: boolean;
}

export default function RelationshipCard({ relationship, onClick, compact = false }: RelationshipCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick(relationship);
    }
  };

  const getRelationshipIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'romantic':
      case 'dating':
      case 'married':
        return <Heart className="w-4 h-4" />;
      case 'family':
      case 'sibling':
      case 'parent':
        return <Users className="w-4 h-4" />;
      default:
        return <Users className="w-4 h-4" />;
    }
  };

  const getRelationshipColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'romantic':
      case 'dating':
      case 'married':
        return 'bg-pink-100 text-pink-800 border-pink-200';
      case 'family':
      case 'sibling':
      case 'parent':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'professional':
      case 'colleague':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'friendship':
      case 'friend':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (compact) {
    return (
      <div
        className={cn(
          "inline-flex items-center px-3 py-2 rounded-lg text-sm border cursor-pointer hover:shadow-sm transition-shadow",
          getRelationshipColor(relationship.relationship_type),
          relationship.both_primary && "ring-2 ring-yellow-300"
        )}
        onClick={handleClick}
      >
        <span className="font-medium">{relationship.entity1}</span>
        <ArrowRight className="w-3 h-3 mx-2" />
        <span className="font-medium">{relationship.entity2}</span>
        <div className="ml-2 flex items-center">
          {getRelationshipIcon(relationship.relationship_type)}
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "bg-white rounded-lg border p-4 cursor-pointer hover:shadow-md transition-shadow",
        relationship.both_primary && "ring-2 ring-yellow-300",
        onClick && "hover:bg-gray-50"
      )}
      onClick={handleClick}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          <div className={cn(
            "p-2 rounded-full",
            getRelationshipColor(relationship.relationship_type)
          )}>
            {getRelationshipIcon(relationship.relationship_type)}
          </div>
          <div>
            <div className="flex items-center space-x-2">
              <span className="font-semibold text-gray-900">{relationship.entity1}</span>
              <ArrowRight className="w-4 h-4 text-gray-400" />
              <span className="font-semibold text-gray-900">{relationship.entity2}</span>
            </div>
            <p className="text-sm text-gray-500 capitalize">{relationship.relationship_type}</p>
          </div>
        </div>
        {relationship.both_primary && (
          <div className="flex items-center text-yellow-600">
            <span className="text-xs">Both Primary</span>
          </div>
        )}
      </div>

      {/* Inception Information */}
      {(relationship.inception_date || relationship.inception_location) && (
        <div className="space-y-2 text-sm text-gray-600">
          {relationship.inception_date && (
            <div className="flex items-center space-x-2">
              <Calendar className="w-4 h-4" />
              <span>
                Started {relationship.inception_date}
                {relationship.inception_time && ` at ${relationship.inception_time}`}
                {relationship.estimated_inception && (
                  <span className="text-gray-400 ml-1">(estimated)</span>
                )}
              </span>
            </div>
          )}
          {relationship.inception_location && (
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4" />
              <span>{relationship.inception_location}</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
