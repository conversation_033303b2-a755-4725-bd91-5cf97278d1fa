/**
 * ==========================================
 * OWNERSHIP: SHARED (Coordination Required)
 * ==========================================
 * This file integrates Layout components with Logic data flows.
 * 
 * CHANGE PROTOCOL:
 * - Layout team: Can modify UI structure, styling, component composition
 * - Logic team: Can modify data fetching, business logic, API integration
 * - Both teams: Must coordinate on data flow and component interfaces
 * 
 * LAYOUT RESPONSIBILITIES:
 * - Component rendering and visual design
 * - User interaction patterns and navigation
 * - Styling and responsive layout
 * 
 * LOGIC RESPONSIBILITIES:
 * - Data fetching with useArticles, useHealth hooks
 * - State management and error handling
 * - Business logic for filtering and data transformation
 * 
 * CRITICAL INTEGRATION POINTS:
 * - Hook interfaces (useArticles, useHealth) - maintain return types
 * - Component props (ArticleCard) - preserve prop structure
 * - Type imports from shared schema - coordinate changes
 * 
 * CONTACT: Both teams required for modifications
 * ==========================================
 */

import { useState, useMemo } from "react";
import TopNavigation from "@/components/TopNavigation";
import ArticleCard from "@/components/ArticleCard";
import { Skeleton } from "@/components/ui/skeleton";
import { useArticles, useHealth } from "@/hooks/useStarPowerApi";
import type { Category, ArticleWithDetails, Actor } from "@shared/schema";
import type { ExtendedArticle } from "@/types/api";

const staticCategories: Category[] = [
  { id: 1, name: "Top Stories", slug: "top", color: "#8b5cf6", icon: "⭐" },
  { id: 2, name: "Celebrity", slug: "celebrity", color: "#ec4899", icon: "🎭" },
  { id: 3, name: "Entertainment", slug: "entertainment", color: "#f59e0b", icon: "🎬" },
  { id: 4, name: "Lifestyle", slug: "lifestyle", color: "#10b981", icon: "✨" },
  { id: 5, name: "World", slug: "world", color: "#3b82f6", icon: "🌍" },
  { id: 6, name: "Tech", slug: "tech", color: "#6366f1", icon: "💻" }
];

export default function Home() {
  const [activeCategory, setActiveCategory] = useState("top");

  const {
    articles: starPowerArticles,
    loading: articlesLoading,
    error: articlesError,
  } = useArticles({
    page: 1,
    limit: 20,
    ...(activeCategory !== "top" && { category: activeCategory })
  });

  const { data: healthStatus } = useHealth();

  const articles: ExtendedArticle[] = starPowerArticles.map(article => {
    let articleCategory = staticCategories.find(c => c.slug === "top");

    if (article.celebrity_mentions && article.celebrity_mentions.length > 0) {
      articleCategory = staticCategories.find(c => c.slug === "celebrity");
    } else if (article.entities && article.entities.length > 0) {
      const entityText = article.entities.join(' ').toLowerCase();
      if (entityText.includes('entertainment') || entityText.includes('movie') || entityText.includes('film')) {
        articleCategory = staticCategories.find(c => c.slug === "entertainment");
      } else if (entityText.includes('tech') || entityText.includes('technology')) {
        articleCategory = staticCategories.find(c => c.slug === "tech");
      } else if (entityText.includes('lifestyle') || entityText.includes('health')) {
        articleCategory = staticCategories.find(c => c.slug === "lifestyle");
      } else if (entityText.includes('world') || entityText.includes('politics')) {
        articleCategory = staticCategories.find(c => c.slug === "world");
      }
    }

    const actors: Actor[] = (article.celebrity_mentions || []).map((name, index) => ({
      id: index + 1000,
      name: name || 'Unknown Actor',
      slug: (name || 'unknown-actor').toLowerCase().replace(/\s+/g, '-'),
      category: 'celebrity',
      sunSign: undefined,
      moonSign: undefined,
      risingSign: undefined,
      profileImage: undefined,
    }));

    return {
      ...(article as any),
      category: articleCategory || staticCategories.find(c => c.slug === 'top')!,
      actors: actors,
      isCelebrity: actors.length > 0,
      astroGlyphs: [],
      likeCount: 0,
      shareCount: 0,
      bookmarkCount: 0,
    };
  });

  const filteredArticles = useMemo(() => {
    if (activeCategory === "top") {
      return articles;
    }
    return articles.filter(article => article.category?.slug === activeCategory);
  }, [articles, activeCategory]);

  if (articlesLoading) {
    return (
      <div className="mobile-container">
        <div className="bg-primary text-white">
          <div className="status-bar">
            <span className="font-medium">9:41</span>
            <div className="flex items-center space-x-1">
              <span>📶</span>
              <span>📶</span>
              <span>🔋</span>
            </div>
          </div>
          <div className="app-header">
            <h1 className="text-xl font-bold">Star Power</h1>
            <p className="text-xs text-purple-200">Astrological News Reader</p>
            {healthStatus && (
              <div className="text-xs mt-1">
                API: <span className={`${healthStatus.status === 'healthy' ? 'text-green-300' : 'text-yellow-300'}`}>
                  {healthStatus.status}
                </span>
              </div>
            )}
          </div>
          <div className="px-4 pb-4">
            <Skeleton className="h-8 w-full bg-white/20" />
          </div>
        </div>
        <div className="flex-1 pb-20 bg-[hsl(var(--surface-variant))] min-h-screen">
          <div className="space-y-4 p-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg p-4 border-l-4 border-gray-200">
                <Skeleton className="h-4 w-20 mb-2" />
                <Skeleton className="h-6 w-full mb-2" />
                <Skeleton className="h-4 w-3/4 mb-3" />
                <div className="flex space-x-2">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-16" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <TopNavigation
        categories={staticCategories}
        activeCategory={activeCategory}
        onCategoryChange={setActiveCategory}
      />
      
      <div className="flex-1 pb-20 bg-[hsl(var(--surface-variant))] min-h-screen">
        {articlesError && (
          <div className="p-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h3 className="text-red-800 font-medium">API Connection Error</h3>
              <p className="text-red-600 text-sm mt-1">
                {articlesError}
              </p>
              <p className="text-red-600 text-xs mt-2">
                Unable to connect to the Star Power API Gateway. Check if the service is running on port 8000.
              </p>
            </div>
          </div>
        )}

        <div className="space-y-4 p-4">
          {filteredArticles?.map((article) => (
            <ArticleCard key={article.id} article={article} />
          ))}
          {filteredArticles?.length === 0 && !articlesError && (
            <div className="text-center py-8 text-gray-500">
              {activeCategory === "top" ? (
                <>
                  No articles available yet.
                  <br />
                  <span className="text-xs">
                    Try processing some news articles using the Star Power API.
                  </span>
                </>
              ) : (
                <>
                  No {staticCategories.find(c => c.slug === activeCategory)?.name.toLowerCase()} articles found.
                  <br />
                  <span className="text-xs">
                    Switch to "Top Stories" to see all available articles.
                  </span>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
}
