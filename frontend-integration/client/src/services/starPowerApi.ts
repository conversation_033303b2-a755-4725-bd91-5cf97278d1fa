/**
 * ==========================================
 * OWNERSHIP: LOGIC (AI)
 * ==========================================
 * This file contains API integration and business logic.
 * 
 * ALLOWED CHANGES:
 * - API endpoint modifications and data fetching logic
 * - Request/response transformation and error handling
 * - Business logic and data processing functions
 * - Authentication and request interceptors
 * 
 * RESTRICTIONS:
 * - Do NOT modify UI components or styling
 * - Do NOT change component prop interfaces used by Layout files
 * - Must maintain API type definitions used by SHARED files
 * 
 * INTEGRATION POINTS:
 * - API types exported to frontend components (SHARED contract)
 * - transformers.* functions used by UI (maintain interface)
 * 
 * CONTACT: AI team for API/business logic changes
 * ==========================================
 * 
 * Star Power API Client
 * 
 * Integration layer for the new Star Power API Gateway.
 * Provides typed API methods for all Star Power services.
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import type { ExtendedActor } from '@/types/api';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

// Create axios instance with base configuration
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds for astrology analysis
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// API Response Types (matching API Gateway Pydantic models)
export interface ApiIsolateEntity {
  name: string;
  entity_type: string;
  primary: boolean;
  birth_date?: string;
  birth_time?: string;
  birth_location?: string;
  estimated_birth?: boolean;
}

export interface ApiIsolateRelationship {
  entity1: string;
  entity2: string;
  relationship_type: string;
  both_primary: boolean;
  inception_date?: string;
  inception_time?: string;
  inception_location?: string;
  estimated_inception?: boolean;
}

export interface ApiIsolateAction {
  entity1: string;
  entity2: string;
  action_type: string;
  both_primary: boolean;
  inception_date?: string;
  inception_time?: string;
  inception_location?: string;
  estimated_inception?: boolean;
}

export interface ApiIsolateEndeavor {
  entity: string;
  endeavor_type: string;
  endeavor_label: string;
  primary: boolean;
  inception_date?: string;
  inception_time?: string;
  inception_location?: string;
  estimated_inception?: boolean;
}

export interface ApiArticle {
  id: string;
  title: string;
  content: string;
  url: string;
  published_at: string;
  source: string;
  entities: string[];
  celebrity_mentions: string[];
  extraction_confidence: number;
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  language?: string;
  word_count?: number;
  // Isolate entity data (optional, populated when include_entities=true)
  isolate_entities?: ApiIsolateEntity[];
  isolate_relationships?: ApiIsolateRelationship[];
  isolate_actions?: ApiIsolateAction[];
  isolate_endeavors?: ApiIsolateEndeavor[];
  isolate_processing_status?: string;
  isolate_confidence_score?: number;
  created_at: string;
  updated_at: string;
}

export interface ApiCelebrity {
  id: string;
  name: string;
  birth_date?: string;
  birth_time?: string;
  birth_location?: string;
  confidence_score: number;
  data_sources: string[];
  verified: boolean;
  professional_name?: string;
  aliases: string[];
  enhanced: boolean;
  created_at: string;
  updated_at: string;
}

export interface ApiAstrologyAnalysis {
  celebrity_id: string;
  analysis_date: string;
  analysis_results: {
    planetary_positions: Record<string, any>;
    aspects: any[];
    houses: Record<string, any>;
  };
  va_circuits?: any[];
  confidence_score: number;
  processing_time_ms: number;
}

export interface ApiPipelineJob {
  job_id: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  result?: {
    articles_processed: number;
    celebrities_identified: number;
    analyses_completed: number;
  };
  error_message?: string;
  created_at: string;
  updated_at: string;
}

export interface ApiListResponse<T> {
  articles: T[];
  total: number;
  page: number;
  per_page: number;
  pages: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface ApiHealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  services: Record<string, {
    status: string;
    response_time_ms?: number;
    error?: string;
  }>;
}

// API Methods
export const starPowerApi = {
  // Health Check
  health: {
    check: (): Promise<AxiosResponse<ApiHealthStatus>> =>
      apiClient.get('/health/'),

    detailed: (): Promise<AxiosResponse<ApiHealthStatus>> =>
      apiClient.get('/health/detailed/'),
  },

  // Articles
  articles: {
    list: (params?: {
      page?: number;
      limit?: number;
      status?: string;
      source?: string;
      search?: string;
    }): Promise<AxiosResponse<ApiListResponse<ApiArticle>>> =>
      apiClient.get('/api/articles/', { params }),

    getById: (id: string, includeEntities: boolean = true): Promise<AxiosResponse<ApiArticle>> =>
      apiClient.get(`/api/articles/${id}/`, { params: { include_entities: includeEntities } }),

    process: (data: {
      url?: string;
      content?: string;
      title?: string;
      source?: string;
    }): Promise<AxiosResponse<ApiArticle>> =>
      apiClient.post('/api/articles/process/', data),
  },

  // Celebrities
  celebrities: {
    list: (params?: {
      page?: number;
      limit?: number;
      search?: string;
      verified?: boolean;
    }): Promise<AxiosResponse<ApiListResponse<ApiCelebrity>>> =>
      apiClient.get('/api/celebrities/', { params }),

    getById: (id: string): Promise<AxiosResponse<ApiCelebrity>> =>
      apiClient.get(`/api/celebrities/${id}/`),

    identify: (data: {
      text: string;
      context?: string;
      confidence_threshold?: number;
    }): Promise<AxiosResponse<{
      celebrities: Array<{
        name: string;
        confidence: number;
        context: string;
        birth_date?: string;
        birth_location?: string;
      }>;
      total_matches: number;
      processing_time_ms: number;
    }>> =>
      apiClient.post('/api/celebrities/identify/', data),

    search: (query: string): Promise<AxiosResponse<ApiListResponse<ApiCelebrity>>> =>
      apiClient.get('/api/celebrities/', { params: { search: query } }),
  },

  // Astrology
  astrology: {
    analyze: (data: {
      celebrity_id: string;
      analysis_date?: string;
      include_va_circuits?: boolean;
      include_transits?: boolean;
    }): Promise<AxiosResponse<ApiAstrologyAnalysis>> =>
      apiClient.post('/api/astrology/analyze/', data),

    getChart: (celebrityId: string): Promise<AxiosResponse<{
      celebrity_id: string;
      chart_data: any;
      planetary_positions: Record<string, any>;
      houses: Record<string, any>;
      aspects: any[];
    }>> =>
      apiClient.get(`/api/astrology/charts/${celebrityId}/`),
  },

  // Pipeline
  pipeline: {
    process: (data: {
      query?: string;
      max_articles?: number;
      article_url?: string;
    }): Promise<AxiosResponse<{
      message: string;
      job_ids: string[];
      total_jobs: number;
      query?: string;
      max_articles?: number;
    }>> =>
      apiClient.post('/api/pipeline/process/', data),

    getStatus: (jobId: string): Promise<AxiosResponse<ApiPipelineJob>> =>
      apiClient.get(`/api/pipeline/status/${jobId}/`),

    cancel: (jobId: string): Promise<AxiosResponse<{ message: string }>> =>
      apiClient.post(`/api/pipeline/cancel/${jobId}/`),
  },
};

// Utility functions for data transformation
export const transformers = {
  // Convert API article to frontend article format
  articleToFrontend: (apiArticle: ApiArticle) => {
    const summary = (apiArticle.content || '').substring(0, 200) + '...';
    const hashtags = apiArticle.entities || [];
    const celebrityMentions = apiArticle.celebrity_mentions || [];

    return {
      id: apiArticle.id,
      title: apiArticle.title || 'Untitled Article',
      summary: summary,
      content: apiArticle.content || '',
      categoryId: 1, // Default category, could be enhanced
      publishedAt: new Date(apiArticle.published_at || Date.now()),
      astroAnalysis: 'Analysis pending...', // Placeholder
      astroGlyphs: [], // Placeholder
      hashtags: hashtags,
      actorIds: [], // Will be populated from celebrity mentions
      likeCount: 0,
      shareCount: 0,
      bookmarkCount: 0,
      isCelebrity: celebrityMentions.length > 0,
      // Extended fields from API Gateway
      url: apiArticle.url,
      source: apiArticle.source,
      entities: hashtags,
      celebrity_mentions: celebrityMentions,
      extraction_confidence: apiArticle.extraction_confidence || 0,
      processing_status: apiArticle.processing_status || 'pending',
      language: apiArticle.language,
      word_count: apiArticle.word_count,
      // Isolate entity data
      isolate_data: apiArticle.isolate_entities ? {
        entities: apiArticle.isolate_entities,
        relationships: apiArticle.isolate_relationships || [],
        actions: apiArticle.isolate_actions || [],
        endeavors: apiArticle.isolate_endeavors || []
      } : undefined,
    };
  },

  // Convert API celebrity to frontend actor format
  celebrityToActor: (apiCelebrity: ApiCelebrity): ExtendedActor => {
    const id = apiCelebrity.id ? parseInt(apiCelebrity.id) : 0;
    const name = apiCelebrity.name || 'Unknown Celebrity';
    const slug = name.toLowerCase().replace(/\s+/g, '-');
    
    return {
    id: isNaN(id) ? 0 : id,
    name: name,
    slug: slug,
    category: 'celebrity',
    sunSign: undefined, // Will be populated from astrology analysis
    moonSign: undefined,
    risingSign: undefined,
    profileImage: undefined,
    // Extended fields from API Gateway
    birth_date: apiCelebrity.birth_date,
    birth_time: apiCelebrity.birth_time,
    birth_location: apiCelebrity.birth_location,
    confidence_score: apiCelebrity.confidence_score,
    data_sources: apiCelebrity.data_sources,
    verified: apiCelebrity.verified,
    professional_name: apiCelebrity.professional_name,
    aliases: apiCelebrity.aliases || [],
    enhanced: apiCelebrity.enhanced || false,
  };
  },
};

export default starPowerApi;
