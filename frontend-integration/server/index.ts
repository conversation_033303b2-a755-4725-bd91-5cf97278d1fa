import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    // Production-specific middleware
    // API routes are already registered, now serve static files
    serveStatic(app);
  }

  // ALWAYS serve the app on the port and host specified in the environment variables
  // Default to 5173 to avoid conflicts with macOS Control Center (port 5000)
  // Default host to 0.0.0.0 to be accessible from outside containers
  const port = parseInt(process.env.PORT || '5173', 10);
  const host = process.env.HOST || "0.0.0.0";
  
  server.listen(port, host, () => {
    log(`serving on port ${port} at ${host}`);
  }).on('error', (err: any) => {
    if (err.code === 'EADDRINUSE') {
      log(`❌ Port ${port} is already in use. Please:`, 'error');
      log(`   1. Change PORT in .env file to a different port`, 'error');
      log(`   2. Or stop the process using port ${port}`, 'error');
      log(`   3. On macOS, port 5000 is often used by Control Center`, 'error');
      process.exit(1);
    } else {
      log(`❌ Server error: ${err.message}`, 'error');
      throw err;
    }
  });
})();
