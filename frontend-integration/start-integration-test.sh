#!/bin/bash

# Star Power Frontend Integration Test Script
# 
# This script starts both the API Gateway and the frontend for integration testing.

set -e

echo "🌟 Star Power Frontend Integration Test"
echo "======================================="

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the frontend-integration directory"
    exit 1
fi

# Check if API Gateway is available
echo "🔍 Checking API Gateway availability..."
API_URL="http://localhost:8000/api/health"

if curl -s "$API_URL" > /dev/null 2>&1; then
    echo "✅ API Gateway is running on port 8000"
else
    echo "⚠️  API Gateway not detected on port 8000"
    echo "   Please start the API Gateway first:"
    echo "   cd /Users/<USER>/Documents/GitHub/star-power"
    echo "   python run_api_gateway.py"
    echo ""
    echo "   Or start it in a separate terminal and run this script again."
    
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "📝 Creating .env file..."
    cp .env.example .env
fi

# Load PORT from .env if it exists
if [ -f ".env" ]; then
    export $(grep -v '^#' .env | xargs)
fi

# Use PORT from environment or default to 5173
FRONTEND_PORT=${PORT:-5173}

# Check if the intended port is available
if lsof -Pi :$FRONTEND_PORT -sTCP:LISTEN -t >/dev/null 2>&1 ; then
    echo "⚠️  Port $FRONTEND_PORT is already in use"
    echo "   Checking what's using the port..."
    lsof -Pi :$FRONTEND_PORT -sTCP:LISTEN
    echo ""
    echo "   The server will attempt to start anyway with reusePort option."
    echo "   If this fails, please stop the conflicting process or change PORT in .env"
    echo ""
fi

echo ""
echo "🚀 Starting frontend development server..."
echo "   Frontend: http://localhost:$FRONTEND_PORT"
echo "   API Gateway: http://localhost:8000"
echo "   API Docs: http://localhost:8000/docs"
echo ""
echo "📋 Integration Test Checklist:"
echo "   1. ✅ Frontend loads without errors"
echo "   2. ⏳ API calls reach the API Gateway"
echo "   3. ⏳ Health status shows in UI"
echo "   4. ⏳ Article processing works"
echo "   5. ⏳ Celebrity identification works"
echo "   6. ⏳ Astrology analysis works"
echo ""
echo "🧪 Test Pages Available:"
echo "   • Home: http://localhost:$FRONTEND_PORT/"
echo "   • API Test: http://localhost:$FRONTEND_PORT/api-test"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start the development server
npm run dev
