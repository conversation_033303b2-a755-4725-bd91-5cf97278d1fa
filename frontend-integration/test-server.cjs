const http = require('http');

const server = http.createServer((req, res) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  
  res.writeHead(200, { 
    'Content-Type': 'text/html',
    'Access-Control-Allow-Origin': '*'
  });
  
  if (req.url === '/api-test' || req.url === '/api-test/') {
    res.end(`
    <!DOCTYPE html>
    <html>
    <head>
        <title>Star Power API Test</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .card { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 8px; }
            .status-healthy { color: green; }
            .status-unhealthy { color: red; }
            button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
            input { padding: 8px; width: 300px; margin-right: 10px; }
        </style>
    </head>
    <body>
        <h1>🌟 Star Power API Test Interface</h1>
        
        <div class="card">
            <h3>API Health Status</h3>
            <div id="health-status">Loading...</div>
            <button onclick="checkHealth()">Refresh Health</button>
        </div>
        
        <div class="card">
            <h3>Article Processing Test</h3>
            <input type="text" id="article-url" placeholder="Enter celebrity news article URL..." />
            <button onclick="processArticle()">Process Article</button>
            <div id="article-result"></div>
        </div>
        
        <div class="card">
            <h3>Celebrity Identification Test</h3>
            <input type="text" id="celebrity-text" placeholder="Enter text mentioning celebrities..." />
            <button onclick="identifyCelebrities()">Identify Celebrities</button>
            <div id="celebrity-result"></div>
        </div>
        
        <script>
            async function checkHealth() {
                try {
                    const response = await fetch('http://localhost:8000/health/');
                    const data = await response.json();
                    
                    document.getElementById('health-status').innerHTML = \`
                        <div class="status-\${data.status}">Status: \${data.status.toUpperCase()}</div>
                        <div>Services:</div>
                        <ul>
                            \${Object.entries(data.services).map(([name, service]) => 
                                \`<li class="status-\${service.status}">\${name}: \${service.status}</li>\`
                            ).join('')}
                        </ul>
                    \`;
                } catch (error) {
                    document.getElementById('health-status').innerHTML = 
                        '<div class="status-unhealthy">API Gateway not accessible</div>';
                }
            }
            
            async function processArticle() {
                const url = document.getElementById('article-url').value;
                if (!url) return;
                
                document.getElementById('article-result').innerHTML = 'Processing...';
                
                try {
                    const response = await fetch('http://localhost:8000/api/articles/process', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ url: url })
                    });
                    
                    const data = await response.json();
                    document.getElementById('article-result').innerHTML = \`
                        <div style="background: #d4edda; padding: 10px; margin-top: 10px; border-radius: 4px;">
                            <strong>Article Processed Successfully!</strong><br>
                            Title: \${data.title}<br>
                            Status: \${data.processing_status}<br>
                            Entities: \${data.entities?.length || 0} extracted
                        </div>
                    \`;
                } catch (error) {
                    document.getElementById('article-result').innerHTML = \`
                        <div style="background: #f8d7da; padding: 10px; margin-top: 10px; border-radius: 4px;">
                            Error: \${error.message}
                        </div>
                    \`;
                }
            }
            
            async function identifyCelebrities() {
                const text = document.getElementById('celebrity-text').value;
                if (!text) return;
                
                document.getElementById('celebrity-result').innerHTML = 'Identifying...';
                
                try {
                    const response = await fetch('http://localhost:8000/api/celebrities/identify', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text })
                    });
                    
                    const data = await response.json();
                    document.getElementById('celebrity-result').innerHTML = \`
                        <div style="background: #cce5ff; padding: 10px; margin-top: 10px; border-radius: 4px;">
                            <strong>\${data.celebrities_identified.length} Celebrities Identified:</strong><br>
                            \${data.celebrities_identified.map(c => 
                                \`\${c.name} (confidence: \${(c.confidence * 100).toFixed(1)}%)\`
                            ).join('<br>')}
                        </div>
                    \`;
                } catch (error) {
                    document.getElementById('celebrity-result').innerHTML = \`
                        <div style="background: #f8d7da; padding: 10px; margin-top: 10px; border-radius: 4px;">
                            Error: \${error.message}
                        </div>
                    \`;
                }
            }
            
            // Load health status on page load
            checkHealth();
            
            // Auto-refresh health every 30 seconds
            setInterval(checkHealth, 30000);
        </script>
    </body>
    </html>
    `);
  } else {
    res.end(`
    <!DOCTYPE html>
    <html>
    <head><title>Star Power Frontend</title></head>
    <body>
        <h1>🌟 Star Power - Interactive Testing</h1>
        <p><a href="/api-test">Go to API Test Interface</a></p>
        <p>Backend API: <a href="http://localhost:8000/health/">Health Check</a></p>
        <p>API Docs: <a href="http://localhost:8000/docs">Swagger Documentation</a></p>
    </body>
    </html>
    `);
  }
});

server.listen(3000, '127.0.0.1', () => {
  console.log('🌟 Star Power Test Server running on http://localhost:3000');
  console.log('📋 API Test Interface: http://localhost:3000/api-test');
});