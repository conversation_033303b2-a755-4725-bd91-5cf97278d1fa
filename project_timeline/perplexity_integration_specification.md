# Perplexity API Integration Specification

**Document Type**: Technical Implementation Specification  
**Created**: August 2, 2025  
**Status**: Ready for Implementation  
**Priority**: Critical for MVP Completion

## Executive Summary

The Perplexity API integration is the **critical missing component** for Star Power MVP completion. Without this integration, the system can fetch news articles via GNews but cannot perform the core intelligence function: converting articles into astrological JSON format and extracting celebrity natal chart data.

**Current Gap**: Articles → [MISSING PERPLEXITY] → Astrological Intelligence → VA Circuit Analysis

## Core Requirements

### 1. Article Intelligence Pipeline
**Input**: Raw news articles from GNews  
**Output**: Structured astrological JSON with celebrity data

#### Required Transformations:
- **Content Analysis**: Extract astrological themes, planetary influences, timing relevance
- **Celebrity Identification**: Find all celebrities/public figures mentioned
- **Natal Chart Data**: Birth date, birth time, birth location for each celebrity
- **Confidence Scoring**: Rate the reliability of extracted information
- **Context Mapping**: Link celebrities to specific article events and themes

### 2. Celebrity Data Enhancement
**Input**: Celebrity names from articles  
**Output**: Complete natal chart specifications

#### Required Data Points:
```json
{
  "celebrity": {
    "name": "Primary name",
    "aliases": ["Alternative names", "Stage names"],
    "birth_date": "YYYY-MM-DD",
    "birth_time": "HH:MM (if available)",
    "birth_location": {
      "city": "Birth city",
      "state_province": "State/Province",
      "country": "Country",
      "coordinates": {"lat": 0.0, "lng": 0.0}
    },
    "confidence_scores": {
      "birth_date": 0.95,
      "birth_time": 0.3,
      "birth_location": 0.8
    },
    "data_sources": ["Source 1", "Source 2"],
    "verification_status": "verified|unverified|disputed"
  }
}
```

### 3. Astrological Context Extraction
**Input**: Article content + celebrity data  
**Output**: Astrological relevance mapping

#### Required Elements:
- **Planetary Themes**: Which planets/signs are relevant to the story
- **Timing Analysis**: Astrological significance of event dates
- **Celebrity Connections**: How celebrities' charts relate to events
- **Transit Analysis**: Current planetary movements affecting the celebrities
- **Synastry Opportunities**: Relationships between multiple celebrities in the story

## Technical Implementation Plan

### Phase 1: Perplexity Client Creation (1 hour)

#### 1.1 Add Perplexity Provider
**File**: `services/llm_processor/models.py`
```python
class LLMProvider(str, Enum):
    OLLAMA = "ollama"
    OPENAI = "openai"
    PERPLEXITY = "perplexity"  # NEW
    LOCAL = "local"
```

#### 1.2 Create Perplexity Client
**File**: `services/llm_processor/perplexity_client.py`
- API authentication and request handling
- Rate limiting and error management
- Response parsing and validation
- Integration with existing LLMOrchestrator

#### 1.3 Update LLM Configuration
**File**: `services/llm_processor/llm_orchestrator.py`
- Add Perplexity as preferred provider for article analysis
- Configure fallback logic: Perplexity → OpenAI → Ollama
- Add task-specific routing (articles → Perplexity, other tasks → existing logic)

### Phase 2: Article JSONification Pipeline (2-3 hours)

#### 2.1 Article Analysis Prompts
**File**: `services/llm_processor/prompts/article_analysis.py`

```python
ARTICLE_ANALYSIS_PROMPT = """
Analyze this news article for astrological relevance and celebrity information.

Article: {article_content}

Extract and return JSON with:
1. All celebrities/public figures mentioned
2. Astrological themes present in the story
3. Timing significance of events
4. Planetary/zodiacal connections

Required JSON format:
{json_schema}
"""
```

#### 2.2 Celebrity Extraction Engine
**File**: `services/llm_processor/celebrity_extractor.py`
- Parse Perplexity responses for celebrity mentions
- Validate celebrity names against known databases
- Handle disambiguation (John Smith actor vs John Smith politician)
- Extract context clues for better identification

#### 2.3 Astrological Context Mapper
**File**: `services/llm_processor/astrological_context_mapper.py`
- Map article themes to astrological concepts
- Identify planetary rulerships and influences
- Extract timing-related astrological significance
- Connect events to current transits

### Phase 3: Celebrity Data Enhancement (2 hours)

#### 3.1 Natal Chart Data Collector
**File**: `services/llm_processor/natal_data_collector.py`

```python
NATAL_DATA_PROMPT = """
Find complete birth information for: {celebrity_name}

Context: {article_context}

Return JSON with:
- Full name and common aliases
- Birth date (YYYY-MM-DD)
- Birth time (HH:MM) if available
- Birth location (city, state/province, country)
- Confidence scores for each data point
- Sources used for verification

JSON format: {schema}
"""
```

#### 3.2 Birth Data Validation
**File**: `services/llm_processor/birth_data_validator.py`
- Cross-reference multiple sources
- Validate date formats and plausibility
- Handle conflicting information
- Score confidence levels based on source quality

#### 3.3 Geographic Coordinate Resolution
**File**: `services/llm_processor/location_resolver.py`
- Convert birth locations to precise coordinates
- Handle historical location name changes
- Validate time zone information for birth times
- Ensure accuracy for astrological calculations

### Phase 4: Integration and Testing (1 hour)

#### 4.1 Pipeline Integration
**File**: `services/api_gateway/routes/pipeline.py`
- Update article processing endpoint to use Perplexity
- Add celebrity data enhancement workflow
- Implement progress tracking for multi-step processing

#### 4.2 Database Schema Updates
**Files**: `services/database/models.py`
- Add fields for Perplexity-extracted data
- Include confidence scores and data sources
- Support for astrological context metadata

#### 4.3 API Response Models
**File**: `services/api_gateway/models.py`
- Update API responses to include astrological context
- Add celebrity natal chart data to responses
- Include confidence scoring in API documentation

#### 4.4 End-to-End Testing
- Test GNews → Perplexity → Database → Frontend flow
- Validate astrological JSON format compatibility with VA circuits
- Ensure celebrity data feeds correctly to astrology engine
- Performance testing for article processing pipeline

## Configuration Requirements

### Environment Variables
```env
PERPLEXITY_API_KEY=your_api_key_here
PERPLEXITY_BASE_URL=https://api.perplexity.ai
PERPLEXITY_MODEL=llama-3.1-sonar-huge-128k-online
PERPLEXITY_TIMEOUT=60
PERPLEXITY_MAX_TOKENS=4000
```

### LLM Task Routing
```python
TASK_PROVIDER_MAPPING = {
    LLMTaskType.ARTICLE_ANALYSIS: LLMProvider.PERPLEXITY,
    LLMTaskType.CELEBRITY_IDENTIFICATION: LLMProvider.PERPLEXITY,
    LLMTaskType.NATAL_DATA_COLLECTION: LLMProvider.PERPLEXITY,
    LLMTaskType.CONTENT_ANALYSIS: LLMProvider.OLLAMA,  # Keep existing
    LLMTaskType.VALIDATION: LLMProvider.OPENAI        # Keep existing
}
```

## Success Criteria

### Functional Requirements
- [ ] Article processed into structured astrological JSON
- [ ] All celebrities identified with confidence scores
- [ ] Birth data collected for 90%+ of identified celebrities
- [ ] Astrological context extracted and mapped
- [ ] Data flows correctly to VA circuit analysis

### Performance Requirements
- [ ] Article processing < 30 seconds
- [ ] Celebrity identification accuracy > 95%
- [ ] Birth data accuracy > 85%
- [ ] System handles 100+ articles/day
- [ ] API responses maintain < 2 second target

### Quality Requirements
- [ ] Confidence scoring works accurately
- [ ] Geographic coordinates resolve correctly
- [ ] Historical birth data validated
- [ ] Multiple source cross-referencing
- [ ] Graceful handling of incomplete data

## Implementation Dependencies

### External Services
- **Perplexity API**: Primary intelligence provider
- **GNews API**: Article source (already implemented)
- **Swiss Ephemeris**: Astrological calculations (already implemented)

### Internal Systems
- **LLM Orchestrator**: Requires Perplexity provider addition
- **Database Models**: Need enhancement for new data fields
- **API Gateway**: Update for extended article processing
- **Frontend**: Update to display astrological context

## Risk Assessment

### High Risk
- **API Rate Limits**: Perplexity usage costs and quotas
- **Data Quality**: Birth data accuracy varies by celebrity
- **Response Parsing**: LLM responses may not always match expected JSON format

### Medium Risk
- **Performance**: Complex multi-step processing may be slow
- **Integration**: Changes to existing celebrity identification logic
- **Validation**: Cross-referencing multiple sources adds complexity

### Mitigation Strategies
- Implement robust error handling and retry logic
- Use structured prompts with clear JSON schema requirements
- Add fallback options for when Perplexity is unavailable
- Cache celebrity birth data to reduce repeated API calls

## Post-Implementation Enhancements

### Phase 2 Features (Future)
- **Real-time Transit Analysis**: Live planetary movement effects
- **Synastry Analysis**: Relationship compatibility between celebrities
- **Predictive Elements**: Future astrological influences
- **Historical Context**: Past astrological events for comparison

### Integration Opportunities
- **Wikipedia API**: Additional birth data validation
- **IMDb API**: Entertainment industry celebrity data
- **Wikidata**: Structured celebrity information
- **TimeZone APIs**: Accurate historical time zone data

## Conclusion

The Perplexity integration is essential for transforming Star Power from a basic news aggregator into the intelligent astrological analysis platform envisioned. This specification provides a clear roadmap for implementation that maintains the existing architecture while adding the critical intelligence layer.

**Estimated Implementation Time**: 4-6 hours  
**Business Impact**: Enables true MVP functionality  
**Technical Complexity**: Medium (leverages existing LLM infrastructure)  
**Priority**: Critical for project completion