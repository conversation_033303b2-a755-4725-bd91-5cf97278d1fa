#!/bin/bash

# ==========================================
# Logic Changes Push Script
# ==========================================
# Pushes API integration and business logic changes
# to both the main star-power repo and star-power-fe repo.
#
# USAGE: ./push-logic-changes.sh "commit message"
# ==========================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FE_REPO_URL="https://github.com/VerbalClaymore/star-power-fe.git"
FE_BRANCH="main"
FRONTEND_DIR="frontend-integration"
TEMP_CLONE_DIR="temp-fe-push"

# Get commit message from parameter
COMMIT_MESSAGE="${1:-Update API integration and business logic}"

echo -e "${BLUE}📤 Starting Logic Changes Push...${NC}"

# Function to identify ownership
get_file_ownership() {
    local file="$1"
    
    if grep -q "OWNERSHIP: LAYOUT" "$file" 2>/dev/null; then
        echo "LAYOUT"
    elif grep -q "OWNERSHIP: LOGIC" "$file" 2>/dev/null; then
        echo "LOGIC"
    elif grep -q "OWNERSHIP: SHARED" "$file" 2>/dev/null; then
        echo "SHARED"
    else
        # Default rules based on file path
        case "$file" in
            */services/*|*/hooks/useStarPowerApi.ts|server/*|*/pages/api-test.tsx)
                echo "LOGIC" ;;
            */types/api.ts|shared/schema.ts|*/pages/home.tsx|*/pages/article.tsx|*/pages/actor-profile.tsx|*/pages/search.tsx)
                echo "SHARED" ;;
            package.json|tsconfig.json|vite.config.ts)
                echo "SHARED" ;;
            *)
                echo "UNKNOWN" ;;
        esac
    fi
}

# Function to commit to main repo
commit_to_main_repo() {
    echo -e "${BLUE}📝 Committing to main star-power repository...${NC}"
    
    # Add all changes
    git add .
    
    # Check if there are changes to commit
    if git diff --staged --quiet; then
        echo -e "${YELLOW}⚠️  No changes to commit in main repository${NC}"
        return 0
    fi
    
    # Commit changes
    git commit -m "$COMMIT_MESSAGE

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>"
    
    echo -e "${GREEN}✅ Changes committed to main repository${NC}"
}

# Function to push logic changes to frontend repo
push_to_frontend_repo() {
    echo -e "${BLUE}🔄 Pushing LOGIC changes to star-power-fe repository...${NC}"
    
    # Clone the frontend repo to temp directory
    if [ -d "$TEMP_CLONE_DIR" ]; then
        rm -rf "$TEMP_CLONE_DIR"
    fi
    
    git clone --branch "$FE_BRANCH" "$FE_REPO_URL" "$TEMP_CLONE_DIR"
    
    # Copy LOGIC and SHARED files to the frontend repo
    find "$FRONTEND_DIR" -type f \( -name "*.tsx" -o -name "*.ts" -o -name "*.css" -o -name "*.json" -o -name "*.js" \) | while read -r file; do
        # Get relative path
        rel_path="${file#$FRONTEND_DIR/}"
        
        # Determine ownership
        ownership=$(get_file_ownership "$file")
        
        # Only copy LOGIC and SHARED files
        if [ "$ownership" = "LOGIC" ] || [ "$ownership" = "SHARED" ]; then
            target_file="$TEMP_CLONE_DIR/$rel_path"
            
            # Create directory if it doesn't exist
            mkdir -p "$(dirname "$target_file")"
            
            # Copy the file
            cp "$file" "$target_file"
            echo -e "${GREEN}📄 Copied: $rel_path ($ownership)${NC}"
        elif [ "$ownership" = "LAYOUT" ]; then
            echo -e "${YELLOW}⏭️  Skipped: $rel_path (LAYOUT - preserved in FE repo)${NC}"
        fi
    done
    
    # Commit and push changes to frontend repo
    cd "$TEMP_CLONE_DIR"
    
    # Configure git (use existing config)
    git config user.name "$(git config --global user.name || echo 'AI Assistant')"
    git config user.email "$(git config --global user.email || echo '<EMAIL>')"
    
    # Add changes
    git add .
    
    # Check if there are changes to commit
    if git diff --staged --quiet; then
        echo -e "${YELLOW}⚠️  No changes to push to frontend repository${NC}"
        cd ..
        rm -rf "$TEMP_CLONE_DIR"
        return 0
    fi
    
    # Commit changes
    git commit -m "$COMMIT_MESSAGE (Logic/Shared updates)

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>"
    
    # Push changes
    echo -e "${BLUE}🚀 Pushing to star-power-fe repository...${NC}"
    git push origin "$FE_BRANCH"
    
    cd ..
    rm -rf "$TEMP_CLONE_DIR"
    
    echo -e "${GREEN}✅ Changes pushed to frontend repository${NC}"
}

# Function to verify the push
verify_push() {
    echo -e "${BLUE}🔍 Verifying push integrity...${NC}"
    
    # Check that we have the right files committed
    local logic_files=(
        "$FRONTEND_DIR/client/src/services/starPowerApi.ts"
        "$FRONTEND_DIR/client/src/hooks/useStarPowerApi.ts"
        "$FRONTEND_DIR/client/src/types/api.ts"
    )
    
    for file in "${logic_files[@]}"; do
        if [ -f "$file" ]; then
            echo -e "${GREEN}✅ Verified: $(basename "$file")${NC}"
        else
            echo -e "${RED}⚠️  Missing: $(basename "$file")${NC}"
        fi
    done
}

# Main execution
main() {
    echo -e "${BLUE}=================================${NC}"
    echo -e "${BLUE}Logic Changes Push - Star Power${NC}"
    echo -e "${BLUE}=================================${NC}"
    
    # Verify we're in the right directory
    if [ ! -d ".git" ]; then
        echo -e "${RED}❌ Error: Must be run from the root of the star-power repository${NC}"
        exit 1
    fi
    
    # Check if frontend directory exists
    if [ ! -d "$FRONTEND_DIR" ]; then
        echo -e "${RED}❌ Error: $FRONTEND_DIR directory not found${NC}"
        exit 1
    fi
    
    # Verify git credentials are configured
    if ! git config user.name >/dev/null || ! git config user.email >/dev/null; then
        echo -e "${YELLOW}⚠️  Warning: Git user not configured. Using defaults.${NC}"
    fi
    
    echo -e "${BLUE}Commit message: ${YELLOW}'$COMMIT_MESSAGE'${NC}"
    echo
    
    # Execute push process
    commit_to_main_repo
    push_to_frontend_repo
    verify_push
    
    echo -e "${BLUE}=================================${NC}"
    echo -e "${GREEN}✅ Logic changes pushed successfully!${NC}"
    echo -e "${BLUE}=================================${NC}"
    echo
    echo -e "${YELLOW}Changes pushed to:${NC}"
    echo -e "1. Main repository (star-power)"
    echo -e "2. Frontend repository (star-power-fe)"
    echo
    echo -e "${YELLOW}Next steps:${NC}"
    echo -e "1. Run ./sync-frontend.sh to pull any layout updates"
    echo -e "2. Test integration: cd $FRONTEND_DIR && npm run dev"
    echo
}

# Run main function
main "$@"