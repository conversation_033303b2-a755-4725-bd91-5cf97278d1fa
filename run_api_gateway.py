#!/usr/bin/env python3
"""
Star Power API Gateway Startup Script

Starts the FastAPI server with proper configuration for development and production.
"""

import os
import sys
import uvicorn
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """Start the API Gateway server."""
    
    # Environment configuration
    host = os.getenv("API_HOST", "127.0.0.1")
    port = int(os.getenv("API_PORT", "8000"))
    reload = os.getenv("API_RELOAD", "true").lower() == "true"
    log_level = os.getenv("API_LOG_LEVEL", "info")
    
    print("🌟 Starting Star Power API Gateway")
    print("=" * 50)
    print(f"Host: {host}")
    print(f"Port: {port}")
    print(f"Reload: {reload}")
    print(f"Log Level: {log_level}")
    print("=" * 50)
    print()
    print("📖 API Documentation will be available at:")
    print(f"   • Swagger UI: http://{host}:{port}/docs")
    print(f"   • ReDoc: http://{host}:{port}/redoc")
    print(f"   • OpenAPI JSON: http://{host}:{port}/openapi.json")
    print()
    print("🔍 Health Check:")
    print(f"   • http://{host}:{port}/health")
    print()
    print("🚀 Starting server...")
    print()
    
    # Start the server
    uvicorn.run(
        "services.api_gateway.main:app",
        host=host,
        port=port,
        reload=reload,
        log_level=log_level,
        access_log=True
    )

if __name__ == "__main__":
    main()
