# cl_runner.py

"""
AI Task Runner for Claude/Cursor

Usage:
    Run this script inside any /tasks/TASK-XXX/ folder containing:
    - task.yaml
    - README_context.md
    - inputs/ (optional)
    - expected_output/ (optional)

<PERSON> should be instructed to read both task.yaml and README_context.md
before generating code or running scripts.
"""

import yaml
import os

def load_task(path="task.yaml"):
    with open(path, "r") as f:
        return yaml.safe_load(f)

def print_context():
    if os.path.exists("README_context.md"):
        with open("README_context.md", "r") as f:
            print(f.read())
    else:
        print("No context file found.")

if __name__ == "__main__":
    task = load_task()
    print(f"🧠 TASK LOADED: {task['task_id']} – {task['description']}")
    print("👓 Inputs:", task.get("inputs", "None"))
    print("🛠️ Outputs:", task.get("output_format", "None"))
    print("✅ Criteria:", task.get("completion_criteria", "None"))
    print("📖 Context from README:
")
    print_context()
