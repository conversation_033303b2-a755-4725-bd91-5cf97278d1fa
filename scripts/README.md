# Star Power Scripts Directory

This directory contains automation scripts for development, testing, and verification workflows.

## Frontend Verification Scripts

### `verify_frontend.sh` ⭐ **Primary Frontend Testing Script**

**Purpose**: Automated frontend verification without manual browser interaction.

**Usage**:
```bash
# Run comprehensive frontend verification
./scripts/verify_frontend.sh

# Check exit code for automation
./scripts/verify_frontend.sh && echo "✅ PASSED" || echo "❌ FAILED"
```

**What it tests**:
- ✅ Frontend server accessibility (`localhost:5173`)
- ✅ API server health (`localhost:8000/health/`)  
- ✅ Articles endpoint data (`localhost:8000/api/articles/`)
- ✅ Frontend error message detection
- ✅ JavaScript error detection
- ✅ URL construction validation

**When to use**:
- After making frontend code changes
- Before manual browser testing
- In CI/CD pipelines
- When troubleshooting "API Connection Error"
- Instead of manual "refresh browser and check"

**Exit codes**:
- `0` = All tests passed ✅
- `1` = One or more tests failed ❌

## Development Workflow Integration

### Recommended Development Flow

1. **Make frontend changes**
2. **Run verification**: `./scripts/verify_frontend.sh`
3. **If passed**: Proceed to manual browser testing
4. **If failed**: Fix issues identified in script output

### CI/CD Integration

```yaml
# Example GitHub Actions usage
- name: Verify Frontend
  run: ./scripts/verify_frontend.sh
```

### Debugging Integration

The verification script replaces manual browser checks mentioned in:
- `docs/frontend_debugging_log.md` 
- `docs/frontend_url_fix_guide.md`
- `README.md` debugging checklist

## Other Scripts

### Database Scripts
- `init_database.py` - Database initialization
- `create_sample_data.py` - Sample data generation

### Testing Scripts  
- `test_*.py` - Various API and integration tests
- `run_golden_set_tests.py` - Golden set validation
- `validate_isolate_feature.py` - Feature validation

## Script Maintenance

- **Add new scripts** with clear documentation
- **Update this README** when adding functionality
- **Follow naming convention**: `verb_noun.extension`
- **Include usage examples** and exit code documentation

---

**For Questions**: Contact development team
**Last Updated**: Scripts directory created with automated frontend verification