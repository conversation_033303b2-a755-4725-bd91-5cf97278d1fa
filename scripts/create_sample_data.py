#!/usr/bin/env python3
"""
Sample Data Creation Script

Creates sample articles and celebrity data for testing the Star Power system.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import services
sys.path.append(str(Path(__file__).parent.parent))

from services.database.connection_manager import get_connection_manager
from services.database.config import get_database_config, get_redis_config
from services.database.connection_manager import initialize_connections
from services.database.models import ArticleData, CelebrityData, ProcessingStatus, DataSource
from services.database.article_db_service import ArticleDBService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_sample_celebrities():
    """Create sample celebrity data."""
    celebrities = [
        CelebrityData(
            name="<PERSON>",
            birth_date=datetime(1989, 12, 13),
            birth_time="17:30",
            birth_location="West Reading, Pennsylvania, USA",
            birth_latitude=40.3356,
            birth_longitude=-75.9269,
            birth_timezone="America/New_York",
            confidence_score=0.95,
            data_sources=[DataSource.ASTRO_DATABANK, DataSource.WIKIPEDIA],
            enhanced=<PERSON>,
            verified=<PERSON>,
            aliases=["T-<PERSON>", "<PERSON>y <PERSON>y"],
            professional_name="<PERSON> Swift",
            roles=["singer", "songwriter", "actor"]
        ),
        CelebrityData(
            name="<PERSON> DiCaprio",
            birth_date=datetime(1974, 11, 11),
            birth_time="02:47",
            birth_location="Los Angeles, California, USA",
            birth_latitude=34.0522,
            birth_longitude=-118.2437,
            birth_timezone="America/Los_Angeles",
            confidence_score=0.90,
            data_sources=[DataSource.ASTRO_DATABANK],
            enhanced=True,
            verified=True,
            aliases=["Leo"],
            professional_name="Leonardo DiCaprio",
            roles=["actor", "producer", "environmentalist"]
        ),
        CelebrityData(
            name="Oprah Winfrey",
            birth_date=datetime(1954, 1, 29),
            birth_time="04:30",
            birth_location="Kosciusko, Mississippi, USA",
            birth_latitude=33.0576,
            birth_longitude=-89.5867,
            birth_timezone="America/Chicago",
            confidence_score=0.85,
            data_sources=[DataSource.ASTRO_DATABANK, DataSource.WIKIPEDIA],
            enhanced=True,
            verified=True,
            aliases=["The Big O"],
            professional_name="Oprah Winfrey",
            roles=["television_host", "producer", "author", "philanthropist"]
        )
    ]
    
    # Insert celebrities into database
    connection_manager = await get_connection_manager()
    
    for celebrity in celebrities:
        insert_sql = """
        INSERT INTO celebrities (
            id, name, birth_date, birth_time, birth_location, birth_latitude, 
            birth_longitude, birth_timezone, confidence_score, data_sources,
            enhanced, verified, created_at, updated_at, aliases, professional_name,
            entity_type, roles
        ) VALUES (
            :id, :name, :birth_date, :birth_time, :birth_location, :birth_latitude,
            :birth_longitude, :birth_timezone, :confidence_score, :data_sources,
            :enhanced, :verified, :created_at, :updated_at, :aliases, :professional_name,
            :entity_type, :roles
        )
        """
        
        async with connection_manager.database.get_session() as session:
            from sqlalchemy import text
            import json
            
            await session.execute(text(insert_sql), {
                "id": celebrity.id,
                "name": celebrity.name,
                "birth_date": celebrity.birth_date,
                "birth_time": celebrity.birth_time,
                "birth_location": celebrity.birth_location,
                "birth_latitude": celebrity.birth_latitude,
                "birth_longitude": celebrity.birth_longitude,
                "birth_timezone": celebrity.birth_timezone,
                "confidence_score": celebrity.confidence_score,
                "data_sources": json.dumps([ds.value for ds in celebrity.data_sources]),
                "enhanced": celebrity.enhanced,
                "verified": celebrity.verified,
                "created_at": celebrity.created_at,
                "updated_at": celebrity.updated_at,
                "aliases": json.dumps(celebrity.aliases),
                "professional_name": celebrity.professional_name,
                "entity_type": celebrity.entity_type,
                "roles": json.dumps(celebrity.roles)
            })
            await session.commit()
        
        logger.info(f"Created celebrity: {celebrity.name}")
    
    logger.info(f"Created {len(celebrities)} sample celebrities")
    return celebrities


async def create_sample_articles():
    """Create sample article data."""
    
    # Get current date for realistic articles
    now = datetime.utcnow()
    yesterday = now - timedelta(days=1)
    two_days_ago = now - timedelta(days=2)
    
    articles = [
        ArticleData(
            title="Taylor Swift Announces New Album with Astrological Themes",
            content="""Pop superstar Taylor Swift surprised fans today by announcing her upcoming album 
            "Cosmic Connections," which explores astrological themes and planetary influences. The album, 
            set to release next month, features songs inspired by her Sagittarius sun sign and other 
            celestial phenomena. Swift mentioned in an interview that she's been studying astrology 
            during her downtime and finds fascinating connections between planetary movements and her 
            creative process. The lead single "Mercury Retrograde" is already climbing the charts.""",
            url="https://example.com/taylor-swift-astrology-album",
            published_at=yesterday,
            source="Entertainment Weekly",
            entities=["Taylor Swift", "astrology", "album", "Sagittarius"],
            processing_status=ProcessingStatus.COMPLETED,
            celebrity_mentions=["Taylor Swift"],
            extraction_confidence=0.95,
            language="en",
            word_count=156
        ),
        ArticleData(
            title="Leonardo DiCaprio's Environmental Foundation Hosts Star-Studded Gala",
            content="""Oscar winner Leonardo DiCaprio hosted his annual environmental foundation gala 
            last night, bringing together Hollywood's biggest names to raise awareness about climate change. 
            The event, held under a remarkable full moon in Scorpio, seemed to inspire passionate speeches 
            about planetary stewardship. DiCaprio, known for his Scorpio sun sign's intensity, delivered 
            a powerful keynote about the urgent need for environmental action. The gala raised over $5 
            million for ocean conservation projects worldwide.""",
            url="https://example.com/dicaprio-environmental-gala",
            published_at=two_days_ago,
            source="Variety",
            entities=["Leonardo DiCaprio", "environment", "gala", "Scorpio", "full moon"],
            processing_status=ProcessingStatus.COMPLETED,
            celebrity_mentions=["Leonardo DiCaprio"],
            extraction_confidence=0.88,
            language="en",
            word_count=142
        ),
        ArticleData(
            title="Oprah Winfrey Launches New Wellness Initiative Based on Ancient Wisdom",
            content="""Media mogul Oprah Winfrey unveiled her latest wellness initiative yesterday, 
            combining modern psychology with ancient astrological wisdom. The program, called "Soul 
            Alignment," offers participants personalized guidance based on their birth charts and 
            planetary transits. Winfrey, an Aquarius known for her humanitarian vision, explained 
            how understanding cosmic influences has helped her make better life decisions. The 
            initiative will be available through her media network starting next quarter.""",
            url="https://example.com/oprah-wellness-astrology",
            published_at=now - timedelta(hours=8),
            source="People Magazine",
            entities=["Oprah Winfrey", "wellness", "astrology", "Aquarius", "birth charts"],
            processing_status=ProcessingStatus.COMPLETED,
            celebrity_mentions=["Oprah Winfrey"],
            extraction_confidence=0.92,
            language="en",
            word_count=128
        ),
        ArticleData(
            title="Celebrity Astrologer Predicts Major Industry Changes",
            content="""Renowned celebrity astrologer Susan Miller released her annual Hollywood 
            predictions, forecasting significant changes in the entertainment industry based on 
            upcoming planetary alignments. Miller points to a rare Jupiter-Saturn conjunction 
            happening later this year that could reshape how celebrities connect with their audiences. 
            Several A-list stars, including those with prominent Capricorn placements, are expected 
            to make surprising career pivots. The predictions have already sparked discussions 
            among industry insiders.""",
            url="https://example.com/celebrity-astrology-predictions",
            published_at=now - timedelta(hours=12),
            source="The Hollywood Reporter",
            entities=["Susan Miller", "astrology", "predictions", "Jupiter", "Saturn", "Capricorn"],
            processing_status=ProcessingStatus.PENDING,
            celebrity_mentions=[],
            extraction_confidence=0.75,
            language="en",
            word_count=118
        )
    ]
    
    # Create articles using the ArticleDBService
    article_service = ArticleDBService()
    created_articles = []
    
    for article in articles:
        created_article = await article_service.create_article(article)
        created_articles.append(created_article)
        logger.info(f"Created article: {article.title[:50]}...")
    
    logger.info(f"Created {len(created_articles)} sample articles")
    return created_articles


async def main():
    """Main function to create all sample data."""
    logger.info("Starting sample data creation...")
    
    # Initialize connection manager
    db_config = get_database_config("development")
    redis_config = get_redis_config("development")
    await initialize_connections(db_config, redis_config)
    
    try:
        # Create sample celebrities
        celebrities = await create_sample_celebrities()
        
        # Create sample articles
        articles = await create_sample_articles()
        
        logger.info(f"Sample data creation completed!")
        logger.info(f"Created {len(celebrities)} celebrities and {len(articles)} articles")
        
    except Exception as e:
        logger.error(f"Failed to create sample data: {e}")
        raise
    
    finally:
        connection_manager = await get_connection_manager()
        await connection_manager.close()


if __name__ == "__main__":
    asyncio.run(main())