#!/usr/bin/env python3
"""
Database Initialization Script

Creates all necessary database tables for the Star Power backend system.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import services
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import text
from services.database.connection_manager import get_connection_manager
from services.database.config import get_database_config, get_redis_config
from services.database.connection_manager import initialize_connections

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_tables():
    """Create all database tables."""
    
    # Initialize connection manager
    db_config = get_database_config("development")
    redis_config = get_redis_config("development")
    await initialize_connections(db_config, redis_config)
    
    connection_manager = await get_connection_manager()
    
    # SQL commands to create tables
    create_table_commands = [
        # Articles table (backend schema)
        """
        CREATE TABLE IF NOT EXISTS articles (
            id VARCHAR(255) PRIMARY KEY,
            title VARCHAR(500) NOT NULL,
            content TEXT NOT NULL,
            url VARCHAR(1000) NOT NULL,
            published_at TIMESTAMP NOT NULL,
            source VARCHAR(100) NOT NULL,
            entities JSONB DEFAULT '[]',
            processing_status VARCHAR(50) DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            celebrity_mentions JSONB DEFAULT '[]',
            extraction_confidence FLOAT,
            language VARCHAR(10),
            word_count INTEGER,
            isolate_processed_at TIMESTAMP,
            isolate_entity_ids JSONB DEFAULT '[]',
            isolate_confidence FLOAT
        )
        """,
        
        # Celebrities table (backend schema)
        """
        CREATE TABLE IF NOT EXISTS celebrities (
            id VARCHAR(255) PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            birth_date TIMESTAMP,
            birth_time VARCHAR(10),
            birth_location VARCHAR(200),
            birth_latitude FLOAT,
            birth_longitude FLOAT,
            birth_timezone VARCHAR(50),
            confidence_score FLOAT NOT NULL DEFAULT 0.5,
            data_sources JSONB DEFAULT '[]',
            enhanced BOOLEAN DEFAULT FALSE,
            verified BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_verified_at TIMESTAMP,
            aliases JSONB DEFAULT '[]',
            professional_name VARCHAR(200),
            entity_type VARCHAR(50) DEFAULT 'person',
            roles JSONB DEFAULT '[]'
        )
        """,
        
        # Charts table (backend schema)
        """
        CREATE TABLE IF NOT EXISTS charts (
            id VARCHAR(255) PRIMARY KEY,
            celebrity_id VARCHAR(255) NOT NULL,
            chart_type VARCHAR(50) NOT NULL,
            calculation_time TIMESTAMP NOT NULL,
            location_name VARCHAR(200),
            latitude FLOAT NOT NULL,
            longitude FLOAT NOT NULL,
            timezone VARCHAR(50) NOT NULL,
            planetary_positions JSONB DEFAULT '{}',
            house_positions JSONB DEFAULT '{}',
            va_circuits JSONB DEFAULT '[]',
            total_circuits_found INTEGER DEFAULT 0,
            active_harmonics JSONB DEFAULT '[]',
            circuit_quality_score FLOAT,
            calculation_method VARCHAR(50) DEFAULT 'swiss_ephemeris',
            processing_time_ms FLOAT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (celebrity_id) REFERENCES celebrities(id)
        )
        """,
        
        # Analysis Results table (backend schema)
        """
        CREATE TABLE IF NOT EXISTS analysis_results (
            id VARCHAR(255) PRIMARY KEY,
            article_id VARCHAR(255) NOT NULL,
            celebrity_id VARCHAR(255) NOT NULL,
            chart_id VARCHAR(255),
            analysis_text TEXT NOT NULL,
            key_insights JSONB DEFAULT '[]',
            astrological_themes JSONB DEFAULT '[]',
            relevance_score FLOAT NOT NULL,
            confidence_level VARCHAR(20) DEFAULT 'unknown',
            word_count INTEGER,
            llm_model VARCHAR(100) NOT NULL,
            processing_time_ms FLOAT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            published BOOLEAN DEFAULT FALSE,
            published_at TIMESTAMP,
            FOREIGN KEY (article_id) REFERENCES articles(id),
            FOREIGN KEY (celebrity_id) REFERENCES celebrities(id),
            FOREIGN KEY (chart_id) REFERENCES charts(id)
        )
        """,
        
        # Processing Jobs table (backend schema)
        """
        CREATE TABLE IF NOT EXISTS processing_jobs (
            id VARCHAR(255) PRIMARY KEY,
            job_type VARCHAR(100) NOT NULL,
            status VARCHAR(50) DEFAULT 'pending',
            parameters JSONB DEFAULT '{}',
            priority INTEGER DEFAULT 0,
            progress_percentage FLOAT DEFAULT 0.0,
            current_step VARCHAR(200),
            total_steps INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            started_at TIMESTAMP,
            completed_at TIMESTAMP,
            result JSONB,
            error_message TEXT,
            retry_count INTEGER DEFAULT 0,
            max_retries INTEGER DEFAULT 3
        )
        """,
        
        # Generic Entities table (Isolate system)
        """
        CREATE TABLE IF NOT EXISTS generic_entities (
            id VARCHAR(255) PRIMARY KEY,
            name VARCHAR(200) NOT NULL,
            entity_type VARCHAR(100) NOT NULL,
            subtype VARCHAR(100),
            roles JSONB DEFAULT '[]',
            inception_date TIMESTAMP,
            inception_time VARCHAR(10),
            inception_location VARCHAR(200),
            estimated_inception BOOLEAN DEFAULT FALSE,
            relevance_reason TEXT,
            primary_entity BOOLEAN DEFAULT FALSE,
            confidence_score FLOAT DEFAULT 0.7,
            data_sources JSONB DEFAULT '[]',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """,
        
        # Entity Relationships table (Isolate system)
        """
        CREATE TABLE IF NOT EXISTS entity_relationships (
            id VARCHAR(255) PRIMARY KEY,
            entity1_id VARCHAR(255) NOT NULL,
            entity2_id VARCHAR(255) NOT NULL,
            entity1_name VARCHAR(200) NOT NULL,
            entity2_name VARCHAR(200) NOT NULL,
            relation_type VARCHAR(100) NOT NULL,
            both_primary BOOLEAN DEFAULT FALSE,
            inception_date TIMESTAMP,
            inception_time VARCHAR(10),
            inception_location VARCHAR(200),
            estimated_inception BOOLEAN DEFAULT FALSE,
            confidence_score FLOAT DEFAULT 0.7,
            article_id VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (entity1_id) REFERENCES generic_entities(id),
            FOREIGN KEY (entity2_id) REFERENCES generic_entities(id),
            FOREIGN KEY (article_id) REFERENCES articles(id)
        )
        """,
        
        # Entity Actions table (Isolate system)
        """
        CREATE TABLE IF NOT EXISTS entity_actions (
            id VARCHAR(255) PRIMARY KEY,
            actor_id VARCHAR(255) NOT NULL,
            target_id VARCHAR(255) NOT NULL,
            actor_name VARCHAR(200) NOT NULL,
            target_name VARCHAR(200) NOT NULL,
            action_type VARCHAR(100) NOT NULL,
            both_primary BOOLEAN DEFAULT FALSE,
            inception_date TIMESTAMP NOT NULL,
            inception_time VARCHAR(10),
            inception_location VARCHAR(200),
            estimated_inception BOOLEAN DEFAULT FALSE,
            confidence_score FLOAT DEFAULT 0.7,
            article_id VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (actor_id) REFERENCES generic_entities(id),
            FOREIGN KEY (target_id) REFERENCES generic_entities(id),
            FOREIGN KEY (article_id) REFERENCES articles(id)
        )
        """,
        
        # Entity Endeavors table (Isolate system)
        """
        CREATE TABLE IF NOT EXISTS entity_endeavors (
            id VARCHAR(255) PRIMARY KEY,
            entity_id VARCHAR(255) NOT NULL,
            entity_name VARCHAR(200) NOT NULL,
            endeavor_type VARCHAR(100) NOT NULL,
            endeavor_label VARCHAR(200) NOT NULL,
            primary_entity BOOLEAN DEFAULT FALSE,
            inception_date TIMESTAMP,
            inception_time VARCHAR(10),
            inception_location VARCHAR(200),
            estimated_inception BOOLEAN DEFAULT FALSE,
            confidence_score FLOAT DEFAULT 0.7,
            article_id VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (entity_id) REFERENCES generic_entities(id),
            FOREIGN KEY (article_id) REFERENCES articles(id)
        )
        """
    ]
    
    # Create indexes for performance
    create_index_commands = [
        "CREATE INDEX IF NOT EXISTS idx_articles_status ON articles(processing_status)",
        "CREATE INDEX IF NOT EXISTS idx_articles_source ON articles(source)",
        "CREATE INDEX IF NOT EXISTS idx_articles_published_at ON articles(published_at)",
        "CREATE INDEX IF NOT EXISTS idx_celebrities_name ON celebrities(name)",
        "CREATE INDEX IF NOT EXISTS idx_celebrities_verified ON celebrities(verified)",
        "CREATE INDEX IF NOT EXISTS idx_charts_celebrity_id ON charts(celebrity_id)",
        "CREATE INDEX IF NOT EXISTS idx_analysis_article_id ON analysis_results(article_id)",
        "CREATE INDEX IF NOT EXISTS idx_analysis_celebrity_id ON analysis_results(celebrity_id)",
        "CREATE INDEX IF NOT EXISTS idx_jobs_status ON processing_jobs(status)",
        "CREATE INDEX IF NOT EXISTS idx_jobs_type ON processing_jobs(job_type)",
        "CREATE INDEX IF NOT EXISTS idx_generic_entities_type ON generic_entities(entity_type)",
        "CREATE INDEX IF NOT EXISTS idx_generic_entities_name ON generic_entities(name)",
        "CREATE INDEX IF NOT EXISTS idx_relationships_entities ON entity_relationships(entity1_id, entity2_id)",
        "CREATE INDEX IF NOT EXISTS idx_actions_actor ON entity_actions(actor_id)",
        "CREATE INDEX IF NOT EXISTS idx_actions_target ON entity_actions(target_id)",
        "CREATE INDEX IF NOT EXISTS idx_endeavors_entity ON entity_endeavors(entity_id)"
    ]
    
    try:
        async with connection_manager.database.get_session() as session:
            # Create tables
            for i, command in enumerate(create_table_commands, 1):
                logger.info(f"Creating table {i}/{len(create_table_commands)}...")
                await session.execute(text(command))
            
            # Create indexes
            for i, command in enumerate(create_index_commands, 1):
                logger.info(f"Creating index {i}/{len(create_index_commands)}...")
                await session.execute(text(command))
            
            await session.commit()
            logger.info("Successfully created all database tables and indexes!")
            
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}")
        raise
    
    finally:
        await connection_manager.close()


async def main():
    """Main function."""
    logger.info("Starting database initialization...")
    await create_tables()
    logger.info("Database initialization completed!")


if __name__ == "__main__":
    asyncio.run(main())