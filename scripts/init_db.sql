-- Star Power Astrology Application - Database Initialization
-- This script sets up the initial database schema for development

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create schemas
CREATE SCHEMA IF NOT EXISTS astrology;
CREATE SCHEMA IF NOT EXISTS news;
CREATE SCHEMA IF NOT EXISTS llm;
CREATE SCHEMA IF NOT EXISTS cache;

-- Set search path
SET search_path TO public, astrology, news, llm, cache;

-- Create basic tables for future use
-- Note: Actual table creation will be handled by SQLAlchemy models

-- Articles table (news pipeline)
CREATE TABLE IF NOT EXISTS news.articles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    url TEXT UNIQUE NOT NULL,
    content TEXT,
    published_at TIMESTAMP WITH TIME ZONE,
    source TEXT,
    language VARCHAR(10) DEFAULT 'en',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> indexes for performance
CREATE INDEX IF NOT EXISTS idx_articles_published_at ON news.articles(published_at);
CREATE INDEX IF NOT EXISTS idx_articles_source ON news.articles(source);
CREATE INDEX IF NOT EXISTS idx_articles_title_trgm ON news.articles USING gin(title gin_trgm_ops);

-- Astrological charts table (astrology engine)
CREATE TABLE IF NOT EXISTS astrology.charts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    birth_datetime TIMESTAMP WITH TIME ZONE NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    timezone TEXT,
    chart_data JSONB,
    va_analysis JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for astrological data
CREATE INDEX IF NOT EXISTS idx_charts_birth_datetime ON astrology.charts(birth_datetime);
CREATE INDEX IF NOT EXISTS idx_charts_location ON astrology.charts(latitude, longitude);

-- LLM processing cache table
CREATE TABLE IF NOT EXISTS llm.processing_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    input_hash VARCHAR(64) UNIQUE NOT NULL,
    provider VARCHAR(50) NOT NULL,
    model VARCHAR(100) NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    input_data JSONB NOT NULL,
    output_data JSONB NOT NULL,
    quality_score DECIMAL(3, 2),
    processing_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for LLM cache
CREATE INDEX IF NOT EXISTS idx_llm_cache_hash ON llm.processing_cache(input_hash);
CREATE INDEX IF NOT EXISTS idx_llm_cache_provider ON llm.processing_cache(provider, model);
CREATE INDEX IF NOT EXISTS idx_llm_cache_expires ON llm.processing_cache(expires_at);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_articles_updated_at 
    BEFORE UPDATE ON news.articles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_charts_updated_at 
    BEFORE UPDATE ON astrology.charts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions to the starpower user
GRANT USAGE ON SCHEMA astrology TO starpower;
GRANT USAGE ON SCHEMA news TO starpower;
GRANT USAGE ON SCHEMA llm TO starpower;
GRANT USAGE ON SCHEMA cache TO starpower;

GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA astrology TO starpower;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA news TO starpower;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA llm TO starpower;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA cache TO starpower;

GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA astrology TO starpower;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA news TO starpower;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA llm TO starpower;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA cache TO starpower;

-- Insert some sample data for testing
INSERT INTO news.articles (title, url, content, published_at, source) VALUES
('Sample Celebrity News', 'https://example.com/news/1', 'Sample content for testing', NOW() - INTERVAL '1 day', 'test_source')
ON CONFLICT (url) DO NOTHING;

-- Log successful initialization
DO $$
BEGIN
    RAISE NOTICE 'Star Power database initialization completed successfully';
END $$;
