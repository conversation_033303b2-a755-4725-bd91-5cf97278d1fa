#!/usr/bin/env python3
"""
Golden Set Test Runner

Executes the Isolate golden set tests and generates a comprehensive report.
"""

import asyncio
import sys
import os
import json
from datetime import datetime
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tests.test_isolate_golden_set import IsolateGoldenSetTester, GOLDEN_SET_CASES


async def main():
    """Run the golden set tests and generate report."""
    print("=" * 60)
    print("Star Power Isolate - Golden Set Validation")
    print("=" * 60)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Initialize tester
    tester = IsolateGoldenSetTester()
    
    # Add test cases
    print(f"Loading {len(GOLDEN_SET_CASES)} golden set test cases...")
    for case in GOLDEN_SET_CASES:
        tester.add_test_case(case['name'], case['article_text'], case['expected_json'])
    print()
    
    try:
        # Run tests
        results = await tester.run_all_tests()
        
        # Print detailed results
        print("\n" + "=" * 60)
        print("DETAILED RESULTS")
        print("=" * 60)
        
        for result in results['individual_results']:
            status = "✓ PASSED" if result['passed'] else "✗ FAILED"
            print(f"{status} {result['name']}")
            print(f"  Accuracy: {result['accuracy']:.2%}")
            if result['errors']:
                for error in result['errors']:
                    print(f"  Error: {error}")
            print()
        
        # Print summary
        print("=" * 60)
        print("SUMMARY")
        print("=" * 60)
        print(f"Total Tests:      {results['total_tests']}")
        print(f"Passed Tests:     {results['passed_tests']}")
        print(f"Failed Tests:     {results['failed_tests']}")
        print(f"Overall Accuracy: {results['overall_accuracy']:.2%}")
        print(f"Target Accuracy:  {results['target_accuracy']:.2%}")
        print(f"Meets Target:     {'✓ YES' if results['meets_target'] else '✗ NO'}")
        print()
        
        # Save results to file
        results_file = project_root / "test_results" / f"golden_set_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        results_file.parent.mkdir(exist_ok=True)
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"Results saved to: {results_file}")
        
        # Exit with appropriate code
        if results['meets_target']:
            print("\n🎉 Golden set validation PASSED! Isolate feature meets accuracy target.")
            sys.exit(0)
        else:
            print(f"\n❌ Golden set validation FAILED. Accuracy {results['overall_accuracy']:.2%} below 90% target.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
