#!/usr/bin/env python3
"""
Simple test script to verify API Gateway endpoints for frontend integration.
This script tests the API Gateway endpoints without creating test data.
"""

import asyncio
import json
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.api_gateway.main import app
from fastapi.testclient import TestClient


def test_api_endpoints():
    """Test the API Gateway endpoints that frontend will use."""
    print("🔍 Testing API Gateway endpoints...")
    
    # Create test client
    client = TestClient(app)
    
    # Test 1: Get articles list
    print("\n📋 Test 1: Get articles list")
    response = client.get("/api/articles")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Articles list retrieved: {len(data['articles'])} articles")
        if data['articles']:
            first_article = data['articles'][0]
            print(f"   - First article: {first_article['title'][:50]}...")
            print(f"   - Has isolate_entities: {'isolate_entities' in first_article}")
            print(f"   - Article ID: {first_article['id']}")
            return first_article['id']
        else:
            print("   - No articles found")
            return None
    else:
        print(f"❌ Failed to get articles list: {response.status_code}")
        print(f"   Error: {response.text}")
        return None
    
    
def test_article_detail(article_id):
    """Test article detail endpoints."""
    if not article_id:
        print("\n⚠️  Skipping article detail tests - no articles available")
        return
        
    print(f"\n📄 Testing article detail for ID: {article_id}")
    
    # Create test client
    client = TestClient(app)
    
    # Test 2: Get article without entities
    print("\n📄 Test 2: Get article without entities")
    response = client.get(f"/api/articles/{article_id}")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Article retrieved: {data['title'][:50]}...")
        print(f"   - Has isolate_entities: {'isolate_entities' in data}")
        print(f"   - isolate_entities value: {data.get('isolate_entities', 'None')}")
    else:
        print(f"❌ Failed to get article: {response.status_code}")
        print(f"   Error: {response.text}")
    
    # Test 3: Get article with entities
    print("\n🎭 Test 3: Get article with entities")
    response = client.get(f"/api/articles/{article_id}?include_entities=true")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Article with entities retrieved: {data['title'][:50]}...")
        print(f"   - Has isolate_entities: {'isolate_entities' in data}")
        if data.get('isolate_entities'):
            print(f"   - Entity count: {len(data['isolate_entities'])}")
            print(f"   - First entity: {data['isolate_entities'][0]['name']}")
        else:
            print("   - No isolate entities found")
        if data.get('isolate_relationships'):
            print(f"   - Relationship count: {len(data['isolate_relationships'])}")
        else:
            print("   - No isolate relationships found")
        if data.get('isolate_actions'):
            print(f"   - Action count: {len(data['isolate_actions'])}")
        else:
            print("   - No isolate actions found")
        if data.get('isolate_endeavors'):
            print(f"   - Endeavor count: {len(data['isolate_endeavors'])}")
        else:
            print("   - No isolate endeavors found")
    else:
        print(f"❌ Failed to get article with entities: {response.status_code}")
        print(f"   Error: {response.text}")


def test_frontend_data_transformation():
    """Test the data transformation that frontend will perform."""
    print("\n🔄 Testing frontend data transformation...")
    
    # Simulate API response with Isolate data
    api_response = {
        "id": "123",
        "title": "Taylor Swift Announces New Album During Astrological Event",
        "content": "Pop superstar Taylor Swift surprised fans yesterday by announcing her new album 'Celestial Harmonies' during a rare astrological alignment.",
        "url": "https://example.com/taylor-swift-album",
        "published_at": "2024-12-21T20:00:00Z",
        "source": "Entertainment Weekly",
        "entities": ["Taylor Swift", "Susan Miller", "Jack Antonoff"],
        "celebrity_mentions": ["Taylor Swift", "Susan Miller", "Jack Antonoff", "Phoebe Bridgers"],
        "extraction_confidence": 0.95,
        "processing_status": "completed",
        "isolate_entities": [
            {
                "name": "Taylor Swift",
                "entity_type": "person",
                "primary": True,
                "birth_date": "1989-12-13",
                "birth_time": "17:30",
                "birth_location": "West Reading, PA",
                "estimated_birth": False
            }
        ],
        "isolate_relationships": [
            {
                "entity1": "Taylor Swift",
                "entity2": "Susan Miller", 
                "relationship_type": "collaboration",
                "both_primary": False,
                "inception_date": "2024-12-21"
            }
        ],
        "isolate_actions": [
            {
                "entity1": "Taylor Swift",
                "entity2": "Celestial Harmonies",
                "action_type": "announces", 
                "both_primary": False,
                "inception_date": "2024-12-21"
            }
        ],
        "isolate_endeavors": [
            {
                "entity": "Taylor Swift",
                "endeavor_type": "album_release",
                "endeavor_label": "Celestial Harmonies Album",
                "primary": True,
                "inception_date": "2024-12-21"
            }
        ],
        "created_at": "2024-12-21T20:00:00Z",
        "updated_at": "2024-12-21T20:00:00Z"
    }
    
    # Simulate frontend transformation (matching the starPowerApi.ts transformer)
    frontend_article = {
        "id": int(api_response["id"]),  # Convert to int
        "title": api_response["title"],
        "summary": api_response["content"][:200] + "...",
        "content": api_response["content"],
        "categoryId": 1,
        "publishedAt": api_response["published_at"],
        "astroAnalysis": "Analysis pending...",
        "astroGlyphs": [],
        "hashtags": api_response["entities"],
        "actorIds": [],
        "likeCount": 0,
        "shareCount": 0,
        "bookmarkCount": 0,
        "isCelebrity": len(api_response["celebrity_mentions"]) > 0,
        # Extended fields
        "url": api_response["url"],
        "source": api_response["source"],
        "entities": api_response["entities"],
        "celebrity_mentions": api_response["celebrity_mentions"],
        "extraction_confidence": api_response["extraction_confidence"],
        "processing_status": api_response["processing_status"],
        # Isolate data
        "isolate_data": {
            "entities": api_response["isolate_entities"],
            "relationships": api_response["isolate_relationships"],
            "actions": api_response["isolate_actions"],
            "endeavors": api_response["isolate_endeavors"]
        } if api_response.get("isolate_entities") else None
    }
    
    print("✅ Frontend transformation successful:")
    print(f"   - Article ID: {frontend_article['id']}")
    print(f"   - Title: {frontend_article['title'][:50]}...")
    print(f"   - Has isolate_data: {frontend_article['isolate_data'] is not None}")
    if frontend_article['isolate_data']:
        isolate_data = frontend_article['isolate_data']
        print(f"   - Entities: {len(isolate_data['entities'])}")
        print(f"   - Relationships: {len(isolate_data['relationships'])}")
        print(f"   - Actions: {len(isolate_data['actions'])}")
        print(f"   - Endeavors: {len(isolate_data['endeavors'])}")
        
        # Test component props
        print("\n🎨 Testing component props:")
        if isolate_data['entities']:
            entity = isolate_data['entities'][0]
            print(f"   - Entity component props: name='{entity['name']}', type='{entity['entity_type']}', primary={entity['primary']}")
        
        if isolate_data['relationships']:
            rel = isolate_data['relationships'][0]
            print(f"   - Relationship component props: '{rel['entity1']}' -> '{rel['entity2']}' ({rel['relationship_type']})")


def main():
    """Main test function."""
    print("🧪 Simple Frontend API Integration Test")
    print("=" * 50)
    
    try:
        # Test API endpoints
        article_id = test_api_endpoints()
        
        # Test article detail if we have an article
        test_article_detail(article_id)
        
        # Test frontend transformation
        test_frontend_data_transformation()
        
        print("\n🎉 Simple Frontend API Integration Test Completed!")
        print("=" * 50)
        print("✅ All tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
