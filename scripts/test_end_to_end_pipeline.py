#!/usr/bin/env python3
"""
End-to-End Pipeline Test

Tests the complete pipeline from article creation through Isolate extraction.
"""

import asyncio
import logging
import sys
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.database.models import ArticleData, ProcessingStatus
from services.pipeline_orchestrator import SimplePipelineOrchestrator, PipelineConfig, PipelineJob
from services.database.article_db_service import ArticleDBService
from services.database.generic_entity_db_service import GenericEntityDBService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_end_to_end_pipeline():
    """Test the complete pipeline with a sample article."""
    
    print("🚀 Starting End-to-End Pipeline Test")
    print("=" * 50)
    
    try:
        # Step 1: Create a test article
        print("\n📝 Step 1: Creating test article...")
        
        test_article = ArticleData(
            title="<PERSON> Announces New Album During Astrological Event",
            content="""
            Pop superstar <PERSON> surprised fans yesterday by announcing her upcoming album 
            "Celestial Harmonies" during a rare astrological alignment. The announcement came 
            during a live stream where <PERSON> discussed how the current planetary positions 
            influenced her creative process.
            
            "I've been working with astrologer <PERSON> <PERSON> to understand how the stars 
            align with my artistic vision," <PERSON> said during the broadcast. "This album 
            represents a new chapter in my relationship with the cosmos."
            
            The album is set to release on December 21st, coinciding with the winter solstice. 
            <PERSON>'s longtime collaborator <PERSON> <PERSON>off will be producing several tracks, 
            while she's also working with new producer Phoebe Bridgers on what she calls 
            "the most spiritually connected music I've ever made."
            
            Fans immediately began speculating about potential collaborations with other 
            artists who have shown interest in astrology, including Lana Del Rey and 
            Ariana Grande. The announcement has already generated millions of social media 
            interactions and is trending worldwide.
            """,
            url="https://example.com/taylor-swift-celestial-album",
            published_at=datetime.utcnow(),
            source="test_source",
            processing_status=ProcessingStatus.PENDING
        )
        
        print(f"✅ Created test article: {test_article.title}")
        
        # Step 2: Initialize pipeline orchestrator
        print("\n⚙️  Step 2: Initializing pipeline orchestrator...")
        
        config = PipelineConfig(
            enable_isolate_extraction=True,
            enable_detailed_logging=True
        )
        orchestrator = SimplePipelineOrchestrator(config)
        
        print("✅ Pipeline orchestrator initialized")
        
        # Step 3: Create and start pipeline job
        print("\n🔄 Step 3: Starting pipeline processing...")
        
        job = PipelineJob(article_data=test_article)
        job.started_at = datetime.utcnow()
        orchestrator.active_jobs[job.job_id] = job
        
        print(f"✅ Created pipeline job: {job.job_id}")
        
        # Step 4: Process through pipeline
        print("\n🎯 Step 4: Processing through pipeline stages...")
        
        try:
            await orchestrator._process_pipeline_job(job.job_id)
            print("✅ Pipeline processing completed successfully")
            
            # Check if job completed
            if job.job_id in orchestrator.completed_jobs or job.job_id not in orchestrator.active_jobs:
                print("✅ Job moved to completed status")
            else:
                print("⚠️  Job still in active status")
                
        except Exception as e:
            print(f"❌ Pipeline processing failed: {e}")
            return False
        
        # Step 5: Verify Isolate extraction results
        print("\n🔍 Step 5: Verifying Isolate extraction results...")
        
        if hasattr(job, 'isolate_results') and job.isolate_results:
            print("✅ Isolate extraction results found:")
            print(f"   - Entities: {job.isolate_results.get('entities_count', 0)}")
            print(f"   - Relationships: {job.isolate_results.get('relationships_count', 0)}")
            print(f"   - Actions: {job.isolate_results.get('actions_count', 0)}")
            print(f"   - Endeavors: {job.isolate_results.get('endeavors_count', 0)}")
        else:
            print("⚠️  No Isolate extraction results found")
        
        # Step 6: Check database for stored entities
        print("\n💾 Step 6: Checking database for stored entities...")
        
        try:
            entity_db = GenericEntityDBService()
            # Note: We would need the actual article ID from database to check entities
            print("✅ Entity database service accessible")
        except Exception as e:
            print(f"⚠️  Could not access entity database: {e}")
        
        print("\n🎉 End-to-End Pipeline Test Completed!")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        logger.exception("Test failed")
        return False


async def main():
    """Main test function."""
    success = await test_end_to_end_pipeline()
    
    if success:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
