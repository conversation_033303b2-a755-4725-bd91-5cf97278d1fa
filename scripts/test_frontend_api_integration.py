#!/usr/bin/env python3
"""
Test script to verify frontend API integration for Isolate entities.
This script tests the API Gateway endpoints that the frontend will use.
"""

import asyncio
import json
import sys
from datetime import datetime, timezone
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.api_gateway.main import app
from services.database.models import ArticleData, EntityRelationship, EntityAction, EntityEndeavor, GenericEntity, DatabaseConfig, RedisConfig
from services.database.article_db_service import ArticleDBService
from services.llm_processor.isolate_models import IsolateEntity, IsolateRelationship, IsolateAction, IsolateEndeavor
from services.database.entity_graph_db_service import EntityGraphDBService
from services.database.generic_entity_db_service import GenericEntityDBService
from services.database.connection_manager import initialize_connections
from fastapi.testclient import TestClient


async def create_test_article_with_isolate_data():
    """Create a test article with Isolate entity data."""
    print("📝 Creating test article with Isolate data...")
    
    # Create test article
    article_data = ArticleData(
        id="test-article-123",
        title="<PERSON> Announces New Album During Astrological Event",
        content="Pop superstar Taylor Swift surprised fans yesterday by announcing her new album 'Celestial Harmonies' during a rare astrological alignment. The announcement came during a live stream where Swift was joined by renowned astrologer Susan <PERSON> and producer Jack Antonoff. The album, set to release during the winter solstice, will feature collaborations with Phoebe Bridgers and explore themes of cosmic connection and celestial timing.",
        url="https://example.com/taylor-swift-album",
        published_at=datetime.now(timezone.utc),
        source="Entertainment Weekly",
        entities=["Taylor Swift", "Susan Miller", "Jack Antonoff"],
        celebrity_mentions=["Taylor Swift", "Susan Miller", "Jack Antonoff", "Phoebe Bridgers"],
        extraction_confidence=0.95,
        processing_status="completed"
    )
    
    # Store article in database
    db_service = ArticleDBService()
    await db_service.create_article(article_data)

    # Create test entities using GenericEntity model
    entity_service = GenericEntityDBService()
    graph_service = EntityGraphDBService()

    # Create entities
    entities = [
        GenericEntity(
            name="Taylor Swift",
            entity_type="person",
            primary=True,
            description="Pop superstar and singer-songwriter"
        ),
        GenericEntity(
            name="Susan Miller",
            entity_type="person",
            primary=False,
            description="Renowned astrologer"
        ),
        GenericEntity(
            name="Jack Antonoff",
            entity_type="person",
            primary=False,
            description="Music producer and songwriter"
        ),
        GenericEntity(
            name="Celestial Harmonies",
            entity_type="album",
            primary=False,
            description="Taylor Swift's upcoming album"
        )
    ]

    # Store entities and get their IDs
    entity_ids = {}
    for entity in entities:
        entity_id = await entity_service.create_entity(entity)
        entity_ids[entity.name] = entity_id

    # Create relationships
    relationships = [
        EntityRelationship(
            entity1_id=entity_ids["Taylor Swift"],
            entity2_id=entity_ids["Susan Miller"],
            relationship_type="collaboration",
            both_primary=False,
            inception_date="2024-12-21"
        ),
        EntityRelationship(
            entity1_id=entity_ids["Taylor Swift"],
            entity2_id=entity_ids["Jack Antonoff"],
            relationship_type="professional",
            both_primary=False,
            inception_date="2019-08-01"
        )
    ]

    # Create actions
    actions = [
        EntityAction(
            entity1_id=entity_ids["Taylor Swift"],
            entity2_id=entity_ids["Celestial Harmonies"],
            action_type="announces",
            both_primary=False,
            inception_date="2024-12-21",
            inception_time="20:00",
            inception_location="Nashville, TN"
        )
    ]

    # Create endeavors
    endeavors = [
        EntityEndeavor(
            entity_id=entity_ids["Taylor Swift"],
            endeavor_type="album_release",
            endeavor_label="Celestial Harmonies Album",
            primary=True,
            inception_date="2024-12-21"
        )
    ]

    # Store graph data
    for relationship in relationships:
        await graph_service.create_relationship(relationship)

    for action in actions:
        await graph_service.create_action(action)

    for endeavor in endeavors:
        await graph_service.create_endeavor(endeavor)
    
    print(f"✅ Created test article: {article_data.id}")
    print(f"   - Entities: {len(entities)}")
    print(f"   - Relationships: {len(relationships)}")
    print(f"   - Actions: {len(actions)}")
    print(f"   - Endeavors: {len(endeavors)}")
    print(f"   - Entity IDs: {list(entity_ids.values())}")
    
    return article_data.id


async def test_api_endpoints():
    """Test the API Gateway endpoints that frontend will use."""
    print("\n🔍 Testing API Gateway endpoints...")
    
    # Create test client
    client = TestClient(app)
    
    # Test 1: Get article without entities
    print("\n📄 Test 1: Get article without entities")
    response = client.get("/api/articles/test-article-123")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Article retrieved: {data['title'][:50]}...")
        print(f"   - Has isolate_entities: {'isolate_entities' in data}")
        print(f"   - isolate_entities value: {data.get('isolate_entities', 'None')}")
    else:
        print(f"❌ Failed to get article: {response.status_code}")
        print(f"   Error: {response.text}")
    
    # Test 2: Get article with entities
    print("\n🎭 Test 2: Get article with entities")
    response = client.get("/api/articles/test-article-123?include_entities=true")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Article with entities retrieved: {data['title'][:50]}...")
        print(f"   - Has isolate_entities: {'isolate_entities' in data}")
        if data.get('isolate_entities'):
            print(f"   - Entity count: {len(data['isolate_entities'])}")
            print(f"   - First entity: {data['isolate_entities'][0]['name']}")
        if data.get('isolate_relationships'):
            print(f"   - Relationship count: {len(data['isolate_relationships'])}")
        if data.get('isolate_actions'):
            print(f"   - Action count: {len(data['isolate_actions'])}")
        if data.get('isolate_endeavors'):
            print(f"   - Endeavor count: {len(data['isolate_endeavors'])}")
    else:
        print(f"❌ Failed to get article with entities: {response.status_code}")
        print(f"   Error: {response.text}")
    
    # Test 3: Get articles list
    print("\n📋 Test 3: Get articles list")
    response = client.get("/api/articles")
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Articles list retrieved: {len(data['articles'])} articles")
        if data['articles']:
            first_article = data['articles'][0]
            print(f"   - First article: {first_article['title'][:50]}...")
            print(f"   - Has isolate_entities: {'isolate_entities' in first_article}")
    else:
        print(f"❌ Failed to get articles list: {response.status_code}")
        print(f"   Error: {response.text}")


async def test_frontend_data_transformation():
    """Test the data transformation that frontend will perform."""
    print("\n🔄 Testing frontend data transformation...")
    
    # Simulate API response
    api_response = {
        "id": "test-article-123",
        "title": "Taylor Swift Announces New Album During Astrological Event",
        "content": "Pop superstar Taylor Swift surprised fans...",
        "url": "https://example.com/taylor-swift-album",
        "published_at": "2024-12-21T20:00:00Z",
        "source": "Entertainment Weekly",
        "entities": ["Taylor Swift", "Susan Miller", "Jack Antonoff"],
        "celebrity_mentions": ["Taylor Swift", "Susan Miller", "Jack Antonoff", "Phoebe Bridgers"],
        "extraction_confidence": 0.95,
        "processing_status": "completed",
        "isolate_entities": [
            {
                "name": "Taylor Swift",
                "entity_type": "person",
                "primary": True,
                "birth_date": "1989-12-13",
                "birth_time": "17:30",
                "birth_location": "West Reading, PA",
                "estimated_birth": False
            }
        ],
        "isolate_relationships": [
            {
                "entity1": "Taylor Swift",
                "entity2": "Susan Miller", 
                "relationship_type": "collaboration",
                "both_primary": False,
                "inception_date": "2024-12-21"
            }
        ],
        "isolate_actions": [
            {
                "entity1": "Taylor Swift",
                "entity2": "Celestial Harmonies",
                "action_type": "announces", 
                "both_primary": False,
                "inception_date": "2024-12-21"
            }
        ],
        "isolate_endeavors": [
            {
                "entity": "Taylor Swift",
                "endeavor_type": "album_release",
                "endeavor_label": "Celestial Harmonies Album",
                "primary": True,
                "inception_date": "2024-12-21"
            }
        ],
        "created_at": "2024-12-21T20:00:00Z",
        "updated_at": "2024-12-21T20:00:00Z"
    }
    
    # Simulate frontend transformation
    frontend_article = {
        "id": int(api_response["id"].split("-")[-1]),  # Convert to int
        "title": api_response["title"],
        "summary": api_response["content"][:200] + "...",
        "content": api_response["content"],
        "categoryId": 1,
        "publishedAt": api_response["published_at"],
        "astroAnalysis": "Analysis pending...",
        "astroGlyphs": [],
        "hashtags": api_response["entities"],
        "actorIds": [],
        "likeCount": 0,
        "shareCount": 0,
        "bookmarkCount": 0,
        "isCelebrity": len(api_response["celebrity_mentions"]) > 0,
        # Extended fields
        "url": api_response["url"],
        "source": api_response["source"],
        "entities": api_response["entities"],
        "celebrity_mentions": api_response["celebrity_mentions"],
        "extraction_confidence": api_response["extraction_confidence"],
        "processing_status": api_response["processing_status"],
        # Isolate data
        "isolate_data": {
            "entities": api_response["isolate_entities"],
            "relationships": api_response["isolate_relationships"],
            "actions": api_response["isolate_actions"],
            "endeavors": api_response["isolate_endeavors"]
        } if api_response.get("isolate_entities") else None
    }
    
    print("✅ Frontend transformation successful:")
    print(f"   - Article ID: {frontend_article['id']}")
    print(f"   - Title: {frontend_article['title'][:50]}...")
    print(f"   - Has isolate_data: {frontend_article['isolate_data'] is not None}")
    if frontend_article['isolate_data']:
        isolate_data = frontend_article['isolate_data']
        print(f"   - Entities: {len(isolate_data['entities'])}")
        print(f"   - Relationships: {len(isolate_data['relationships'])}")
        print(f"   - Actions: {len(isolate_data['actions'])}")
        print(f"   - Endeavors: {len(isolate_data['endeavors'])}")


async def main():
    """Main test function."""
    print("🧪 Frontend API Integration Test")
    print("=" * 50)

    try:
        # Initialize database connections
        print("🔌 Initializing database connections...")
        db_config = DatabaseConfig(url="sqlite:///test.db")
        redis_config = RedisConfig(url="redis://localhost:6379/0")
        await initialize_connections(db_config, redis_config)
        print("✅ Database connections initialized")

        # Create test data
        article_id = await create_test_article_with_isolate_data()
        
        # Test API endpoints
        await test_api_endpoints()
        
        # Test frontend transformation
        await test_frontend_data_transformation()
        
        print("\n🎉 Frontend API Integration Test Completed!")
        print("=" * 50)
        print("✅ All tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
