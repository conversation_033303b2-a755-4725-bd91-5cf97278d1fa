#!/usr/bin/env python3
"""
Isolate Pipeline Test

Tests the Isolate extraction stage specifically.
"""

import asyncio
import logging
import sys
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.database.models import ArticleData, ProcessingStatus
from services.pipeline_orchestrator import SimplePipelineOrchestrator, PipelineConfig, PipelineJob
from services.llm_processor.isolate_models import IsolateExtractionRequest

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_isolate_extraction():
    """Test the Isolate extraction stage directly."""
    
    print("🔮 Starting Isolate Extraction Test")
    print("=" * 40)
    
    try:
        # Step 1: Create a test article with celebrity content
        print("\n📝 Step 1: Creating test article...")
        
        test_article = ArticleData(
            title="<PERSON> Announces New Album During Astrological Event",
            content="""
            Pop superstar <PERSON> surprised fans yesterday by announcing her upcoming album 
            "Celestial Harmonies" during a rare astrological alignment. The announcement came 
            during a live stream where <PERSON> discussed how the current planetary positions 
            influenced her creative process.
            
            "I've been working with astrologer <PERSON> <PERSON> to understand how the stars 
            align with my artistic vision," <PERSON> said during the broadcast. "This album 
            represents a new chapter in my relationship with the cosmos."
            
            The album is set to release on December 21st, coinciding with the winter solstice. 
            <PERSON>'s longtime collaborator <PERSON> <PERSON>off will be producing several tracks, 
            while she's also working with new producer <PERSON> <PERSON>rs on what she calls 
            "the most spiritually connected music I've ever made."
            """,
            url="https://example.com/taylor-swift-celestial-album",
            published_at=datetime.utcnow(),
            source="test_source",
            processing_status=ProcessingStatus.PENDING
        )
        
        print(f"✅ Created test article: {test_article.title}")
        
        # Step 2: Initialize pipeline orchestrator
        print("\n⚙️  Step 2: Initializing pipeline orchestrator...")
        
        config = PipelineConfig(
            enable_isolate_extraction=True,
            enable_detailed_logging=True
        )
        orchestrator = SimplePipelineOrchestrator(config)
        
        print("✅ Pipeline orchestrator initialized")
        print(f"   - Isolate extraction enabled: {config.enable_isolate_extraction}")
        print(f"   - Isolate timeout: {config.isolate_extraction_timeout}s")
        print(f"   - Confidence threshold: {config.isolate_confidence_threshold}")
        
        # Step 3: Create pipeline job and mock celebrity data
        print("\n🎭 Step 3: Setting up pipeline job with mock celebrity data...")
        
        job = PipelineJob(article_data=test_article)
        job.started_at = datetime.utcnow()
        
        # Mock some celebrity data to bypass celebrity extraction
        job.celebrities = [
            {
                "name": "Taylor Swift",
                "confidence": 0.95,
                "birth_date": "1989-12-13",
                "birth_location": "West Reading, Pennsylvania"
            },
            {
                "name": "Jack Antonoff", 
                "confidence": 0.85,
                "birth_date": "1984-03-31",
                "birth_location": "Bergenfield, New Jersey"
            }
        ]
        
        orchestrator.active_jobs[job.job_id] = job
        print(f"✅ Created pipeline job: {job.job_id}")
        print(f"   - Mock celebrities: {len(job.celebrities)}")
        
        # Step 4: Test Isolate extraction directly
        print("\n🔮 Step 4: Testing Isolate extraction...")
        
        try:
            await orchestrator._process_isolate_extraction(job)
            print("✅ Isolate extraction completed successfully")
            
            # Check results
            if hasattr(job, 'isolate_results') and job.isolate_results:
                print("✅ Isolate extraction results found:")
                if hasattr(job.isolate_results, 'entities'):
                    print(f"   - Entities: {len(job.isolate_results.entities)}")
                if hasattr(job.isolate_results, 'relationships'):
                    print(f"   - Relationships: {len(job.isolate_results.relationships)}")
                if hasattr(job.isolate_results, 'actions'):
                    print(f"   - Actions: {len(job.isolate_results.actions)}")
                if hasattr(job.isolate_results, 'endeavors'):
                    print(f"   - Endeavors: {len(job.isolate_results.endeavors)}")
            else:
                print("⚠️  No Isolate extraction results found")
                
        except Exception as e:
            print(f"❌ Isolate extraction failed: {e}")
            logger.exception("Isolate extraction failed")
            return False
        
        # Step 5: Test configuration loading
        print("\n⚙️  Step 5: Testing configuration...")
        
        print(f"✅ Configuration loaded successfully:")
        print(f"   - Enable Isolate: {config.enable_isolate_extraction}")
        print(f"   - Timeout: {config.isolate_extraction_timeout}")
        print(f"   - Confidence: {config.isolate_confidence_threshold}")
        
        print("\n🎉 Isolate Extraction Test Completed!")
        print("=" * 40)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        logger.exception("Test failed")
        return False


async def main():
    """Main test function."""
    success = await test_isolate_extraction()
    
    if success:
        print("\n✅ Isolate extraction test passed!")
        sys.exit(0)
    else:
        print("\n❌ Isolate extraction test failed!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
