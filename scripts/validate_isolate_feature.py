#!/usr/bin/env python3
"""
Isolate Feature Validation Script

Comprehensive quality assurance validation for the Isolate feature implementation.
Validates all components from LLM integration through API endpoints.
"""

import asyncio
import sys
import os
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class IsolateFeatureValidator:
    """
    Comprehensive validator for the Isolate feature implementation.
    """
    
    def __init__(self):
        """Initialize the validator."""
        self.validation_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'overall_status': 'UNKNOWN',
            'errors': [],
            'warnings': []
        }
    
    async def validate_imports(self) -> bool:
        """Validate that all required modules can be imported."""
        print("🔍 Validating imports...")
        
        try:
            # Core LLM components
            from services.llm_processor.llm_orchestrator import LLMOrchestrator
            from services.llm_processor.perplexity_client import PerplexityClient
            from services.llm_processor.entity_resolver import EntityResolver
            from services.llm_processor.isolate_models import (
                IsolateEntity, IsolateRelationship, IsolateAction, IsolateEndeavor,
                IsolateResponse, IsolateExtractionRequest, IsolateExtractionResponse
            )
            
            # Database components
            from services.database.generic_entity_db_service import GenericEntityDBService
            from services.database.entity_graph_db_service import EntityGraphDBService
            from services.database.models import (
                GenericEntity, EntityRelationship, EntityAction, EntityEndeavor
            )
            
            # Pipeline components
            from services.pipeline_orchestrator import SimplePipelineOrchestrator, PipelineStage
            
            # API components
            from services.api_gateway.routes.entities import router as entities_router
            from services.api_gateway.models import ArticleResponse

            # Try to import entity response models (may not be available in all environments)
            try:
                from services.api_gateway.models import (
                    EntityResponse, RelationshipResponse, ActionResponse,
                    EndeavorResponse, EntityGraphResponse
                )
            except ImportError:
                # These models may not be available in all test environments
                pass
            
            self.validation_results['tests']['imports'] = {
                'status': 'PASSED',
                'message': 'All required modules imported successfully'
            }
            print("  ✓ All imports successful")
            return True
            
        except ImportError as e:
            self.validation_results['tests']['imports'] = {
                'status': 'FAILED',
                'message': f'Import error: {str(e)}'
            }
            self.validation_results['errors'].append(f'Import validation failed: {e}')
            print(f"  ✗ Import failed: {e}")
            return False
    
    async def validate_llm_integration(self) -> bool:
        """Validate LLM orchestrator and Perplexity integration."""
        print("🔍 Validating LLM integration...")
        
        try:
            from services.llm_processor.llm_orchestrator import LLMOrchestrator
            from services.llm_processor.models import LLMConfig, LLMProvider, LLMTaskType
            from services.llm_processor.isolate_models import IsolateExtractionRequest
            
            # Check that ISOLATE_EXTRACTION task type exists
            assert hasattr(LLMTaskType, 'ISOLATE_EXTRACTION'), "ISOLATE_EXTRACTION task type missing"
            
            # Check that PERPLEXITY provider exists
            assert hasattr(LLMProvider, 'PERPLEXITY'), "PERPLEXITY provider missing"
            
            # Initialize LLM orchestrator
            llm_config = LLMConfig(
                primary_provider=LLMProvider.PERPLEXITY,
                fallback_providers=[LLMProvider.OPENAI],
                enable_fallback=True
            )
            orchestrator = LLMOrchestrator(llm_config)
            
            # Check that extract_isolate_entities method exists
            assert hasattr(orchestrator, 'extract_isolate_entities'), "extract_isolate_entities method missing"
            
            self.validation_results['tests']['llm_integration'] = {
                'status': 'PASSED',
                'message': 'LLM integration components validated'
            }
            print("  ✓ LLM integration validated")
            return True
            
        except Exception as e:
            self.validation_results['tests']['llm_integration'] = {
                'status': 'FAILED',
                'message': f'LLM integration error: {str(e)}'
            }
            self.validation_results['errors'].append(f'LLM integration validation failed: {e}')
            print(f"  ✗ LLM integration failed: {e}")
            return False
    
    async def validate_database_models(self) -> bool:
        """Validate database models and services."""
        print("🔍 Validating database models...")
        
        try:
            from services.database.models import (
                GenericEntity, EntityRelationship, EntityAction, EntityEndeavor, ArticleData
            )
            from services.database.generic_entity_db_service import GenericEntityDBService
            from services.database.entity_graph_db_service import EntityGraphDBService
            
            # Check ArticleData has Isolate fields
            article_fields = ArticleData.__annotations__.keys()
            isolate_fields = ['isolate_processed_at', 'isolate_entity_ids', 'isolate_confidence']

            missing_fields = []
            for field in isolate_fields:
                if field not in article_fields:
                    missing_fields.append(field)

            if missing_fields:
                self.validation_results['warnings'].append(f'ArticleData missing fields: {missing_fields}')
            
            # Initialize database services
            generic_entity_db = GenericEntityDBService()
            entity_graph_db = EntityGraphDBService()
            
            # Check required methods exist
            required_methods = [
                'create_entity', 'get_entity', 'search_entities',
                'get_entities_by_article'
            ]
            
            for method in required_methods:
                assert hasattr(generic_entity_db, method), f"GenericEntityDBService missing {method}"
            
            self.validation_results['tests']['database_models'] = {
                'status': 'PASSED',
                'message': 'Database models and services validated'
            }
            print("  ✓ Database models validated")
            return True
            
        except Exception as e:
            self.validation_results['tests']['database_models'] = {
                'status': 'FAILED',
                'message': f'Database validation error: {str(e)}'
            }
            self.validation_results['errors'].append(f'Database validation failed: {e}')
            print(f"  ✗ Database validation failed: {e}")
            return False
    
    async def validate_pipeline_integration(self) -> bool:
        """Validate pipeline orchestrator integration."""
        print("🔍 Validating pipeline integration...")
        
        try:
            from services.pipeline_orchestrator import SimplePipelineOrchestrator, PipelineStage, PipelineConfig
            
            # Check ISOLATE_EXTRACTION stage exists
            assert hasattr(PipelineStage, 'ISOLATE_EXTRACTION'), "ISOLATE_EXTRACTION stage missing"
            
            # Check config has Isolate settings
            config = PipelineConfig()
            assert hasattr(config, 'isolate_extraction_timeout'), "isolate_extraction_timeout missing"
            assert hasattr(config, 'isolate_confidence_threshold'), "isolate_confidence_threshold missing"
            
            # Initialize orchestrator
            orchestrator = SimplePipelineOrchestrator(config)
            
            # Check required components exist
            assert hasattr(orchestrator, 'entity_resolver'), "entity_resolver missing"
            assert hasattr(orchestrator, 'generic_entity_db'), "generic_entity_db missing"
            assert hasattr(orchestrator, 'entity_graph_db'), "entity_graph_db missing"
            assert hasattr(orchestrator, '_process_isolate_extraction'), "_process_isolate_extraction method missing"
            
            self.validation_results['tests']['pipeline_integration'] = {
                'status': 'PASSED',
                'message': 'Pipeline integration validated'
            }
            print("  ✓ Pipeline integration validated")
            return True
            
        except Exception as e:
            self.validation_results['tests']['pipeline_integration'] = {
                'status': 'FAILED',
                'message': f'Pipeline integration error: {str(e)}'
            }
            self.validation_results['errors'].append(f'Pipeline integration validation failed: {e}')
            print(f"  ✗ Pipeline integration failed: {e}")
            return False
    
    async def validate_api_gateway(self) -> bool:
        """Validate API Gateway enhancements."""
        print("🔍 Validating API Gateway...")

        try:
            from services.api_gateway.routes import entities
            from services.api_gateway.models import ArticleResponse

            # Check entities router exists
            assert hasattr(entities, 'router'), "entities router missing"

            # Check ArticleResponse has Isolate fields
            article_response_fields = ArticleResponse.__annotations__.keys()
            isolate_fields = [
                'isolate_entities', 'isolate_relationships',
                'isolate_actions', 'isolate_endeavors'
            ]

            missing_fields = []
            for field in isolate_fields:
                if field not in article_response_fields:
                    missing_fields.append(field)

            if missing_fields:
                self.validation_results['warnings'].append(f"ArticleResponse missing fields: {missing_fields}")

            # Check if entity response models exist in the models file
            import services.api_gateway.models as models_module
            entity_models = ['EntityResponse', 'RelationshipResponse', 'ActionResponse', 'EndeavorResponse']
            missing_models = []

            for model_name in entity_models:
                if not hasattr(models_module, model_name):
                    missing_models.append(model_name)

            if missing_models:
                self.validation_results['warnings'].append(f"Missing entity response models: {missing_models}")
            else:
                print("  ✓ Entity response models found")

            self.validation_results['tests']['api_gateway'] = {
                'status': 'PASSED',
                'message': 'API Gateway enhancements validated'
            }
            print("  ✓ API Gateway validated")
            return True

        except Exception as e:
            self.validation_results['tests']['api_gateway'] = {
                'status': 'FAILED',
                'message': f'API Gateway validation error: {str(e)}'
            }
            self.validation_results['errors'].append(f'API Gateway validation failed: {e}')
            print(f"  ✗ API Gateway validation failed: {e}")
            return False
    
    async def validate_test_suite(self) -> bool:
        """Validate test suite completeness."""
        print("🔍 Validating test suite...")
        
        try:
            # Check test files exist
            test_files = [
                'tests/test_isolate_golden_set.py',
                'tests/test_isolate_integration.py'
            ]
            
            missing_files = []
            for test_file in test_files:
                if not (project_root / test_file).exists():
                    missing_files.append(test_file)
            
            if missing_files:
                raise FileNotFoundError(f"Missing test files: {missing_files}")
            
            # Check script files exist
            script_files = [
                'scripts/run_golden_set_tests.py',
                'scripts/validate_isolate_feature.py'
            ]
            
            missing_scripts = []
            for script_file in script_files:
                if not (project_root / script_file).exists():
                    missing_scripts.append(script_file)
            
            if missing_scripts:
                self.validation_results['warnings'].append(f"Missing script files: {missing_scripts}")
            
            self.validation_results['tests']['test_suite'] = {
                'status': 'PASSED',
                'message': 'Test suite files validated'
            }
            print("  ✓ Test suite validated")
            return True
            
        except Exception as e:
            self.validation_results['tests']['test_suite'] = {
                'status': 'FAILED',
                'message': f'Test suite validation error: {str(e)}'
            }
            self.validation_results['errors'].append(f'Test suite validation failed: {e}')
            print(f"  ✗ Test suite validation failed: {e}")
            return False
    
    async def run_validation(self) -> Dict[str, Any]:
        """Run complete validation suite."""
        print("🚀 Starting Isolate Feature Validation")
        print("=" * 60)
        
        validation_steps = [
            ('imports', self.validate_imports),
            ('llm_integration', self.validate_llm_integration),
            ('database_models', self.validate_database_models),
            ('pipeline_integration', self.validate_pipeline_integration),
            ('api_gateway', self.validate_api_gateway),
            ('test_suite', self.validate_test_suite)
        ]
        
        passed_tests = 0
        total_tests = len(validation_steps)
        
        for test_name, test_func in validation_steps:
            try:
                result = await test_func()
                if result:
                    passed_tests += 1
            except Exception as e:
                self.validation_results['tests'][test_name] = {
                    'status': 'ERROR',
                    'message': f'Validation error: {str(e)}'
                }
                self.validation_results['errors'].append(f'{test_name} validation error: {e}')
                print(f"  ⚠️  {test_name} validation error: {e}")
        
        # Determine overall status
        if passed_tests == total_tests:
            self.validation_results['overall_status'] = 'PASSED'
        elif passed_tests >= total_tests * 0.8:  # 80% pass rate
            self.validation_results['overall_status'] = 'PASSED_WITH_WARNINGS'
        else:
            self.validation_results['overall_status'] = 'FAILED'
        
        # Print summary
        print("\n" + "=" * 60)
        print("VALIDATION SUMMARY")
        print("=" * 60)
        print(f"Tests Passed: {passed_tests}/{total_tests}")
        print(f"Overall Status: {self.validation_results['overall_status']}")
        
        if self.validation_results['errors']:
            print(f"Errors: {len(self.validation_results['errors'])}")
            for error in self.validation_results['errors']:
                print(f"  - {error}")
        
        if self.validation_results['warnings']:
            print(f"Warnings: {len(self.validation_results['warnings'])}")
            for warning in self.validation_results['warnings']:
                print(f"  - {warning}")
        
        return self.validation_results


async def main():
    """Main validation entry point."""
    validator = IsolateFeatureValidator()
    
    try:
        results = await validator.run_validation()
        
        # Save results
        results_file = project_root / "validation_results.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\nValidation results saved to: {results_file}")
        
        # Exit with appropriate code
        if results['overall_status'] == 'PASSED':
            print("\n🎉 Isolate feature validation PASSED!")
            sys.exit(0)
        elif results['overall_status'] == 'PASSED_WITH_WARNINGS':
            print("\n⚠️  Isolate feature validation PASSED with warnings.")
            sys.exit(0)
        else:
            print("\n❌ Isolate feature validation FAILED.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Validation failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
