#!/bin/bash

# Frontend Verification Script
# Automates testing of frontend functionality without manual browser interaction

set -e

echo "🔍 Star Power Frontend Verification"
echo "=================================="

# Test 1: Check if frontend server is responding
echo "1. Testing frontend server..."
if curl -s -f http://localhost:3000 > /dev/null; then
    echo "   ✅ Frontend server is accessible (dev server on port 3000)"
else
    echo "   ❌ Frontend dev server is not responding on port 3000"
    echo "   💡 Try: cd frontend-integration && npm run dev"
    exit 1
fi

# Test 2: Check if API server is responding
echo "2. Testing API server..."
if curl -s -f http://localhost:8000/health/ > /dev/null; then
    echo "   ✅ API server is healthy"
else
    echo "   ❌ API server is not responding"
    exit 1
fi

# Test 3: Check API articles endpoint
echo "3. Testing articles API endpoint..."
ARTICLES_RESPONSE=$(curl -s http://localhost:8000/api/articles/)
ARTICLE_COUNT=$(echo "$ARTICLES_RESPONSE" | jq -r '.total // 0' 2>/dev/null || echo "0")

if [ "$ARTICLE_COUNT" -gt 0 ]; then
    echo "   ✅ Articles API returns $ARTICLE_COUNT articles"
else
    echo "   ❌ Articles API not returning data"
    exit 1
fi

# Test 4: Check frontend for error messages
echo "4. Checking frontend for error messages..."
FRONTEND_HTML=$(curl -s http://localhost:3000)

if echo "$FRONTEND_HTML" | grep -q "API Connection Error"; then
    echo "   ❌ Frontend shows 'API Connection Error'"
    exit 1
elif echo "$FRONTEND_HTML" | grep -q "undefined is not an object"; then
    echo "   ❌ Frontend has JavaScript errors"
    exit 1
else
    echo "   ✅ Frontend loads without obvious errors"
fi

# Test 5: Test health endpoint that frontend uses
echo "5. Testing health endpoint..."
HEALTH_STATUS=$(curl -s http://localhost:8000/health/ | jq -r '.status // "unknown"' 2>/dev/null)

if [ "$HEALTH_STATUS" = "healthy" ]; then
    echo "   ✅ Health check passes"
else
    echo "   ❌ Health check fails (status: $HEALTH_STATUS)"
    exit 1
fi

# Test 6: Verify URL construction is correct
echo "6. Testing URL construction..."
# Test that the old problematic URL returns 404
if curl -s http://localhost:8000/api/api/articles | grep -q "Not Found"; then
    echo "   ✅ Duplicate /api path correctly returns 404"
else
    echo "   ⚠️  Unexpected response from duplicate path"
fi

echo ""
echo "🎉 All frontend verification tests passed!"
echo "   Frontend Dev Server: http://localhost:3000 (development)"
echo "   Frontend Static Build: http://localhost:5173 (production preview)"  
echo "   API: http://localhost:8000"
echo "   Articles available: $ARTICLE_COUNT"
echo ""
echo "🌟 Use http://localhost:3000 for development (live updates)"