# Star Power API Gateway

The Star Power API Gateway provides a unified REST API interface for the Star Power astrology application. It coordinates between news processing, celebrity identification, and astrological analysis services.

## Features

- **FastAPI Framework**: Modern, fast web framework with automatic OpenAPI documentation
- **Service Coordination**: Unified interface to all Star Power services
- **Error Handling**: Comprehensive error handling with consistent JSON responses
- **Health Monitoring**: Service health checks and status monitoring
- **Auto-Generated Docs**: Interactive API documentation via Swagger UI and ReDoc
- **Async/Await**: Full async support for high performance
- **CORS Support**: Cross-origin resource sharing for frontend integration

## API Endpoints

### Health & Status
- `GET /health` - Service health check
- `GET /health/detailed` - Detailed service status

### News Articles
- `GET /api/articles` - List articles with pagination and filtering
- `GET /api/articles/{id}` - Get specific article
- `POST /api/articles/process` - Process new article from URL or content

### Celebrity Data
- `GET /api/celebrities` - List celebrities with search
- `GET /api/celebrities/{id}` - Get specific celebrity
- `POST /api/celebrities/identify` - Identify celebrities in text

### Astrology Analysis
- `POST /api/astrology/analyze` - Perform astrological analysis
- `GET /api/astrology/charts/{celebrity_id}` - Get celebrity's chart

### Pipeline Orchestration
- `POST /api/pipeline/process` - Trigger full pipeline processing
- `GET /api/pipeline/status/{job_id}` - Check processing status

## Quick Start

### 1. Install Dependencies

```bash
pip install fastapi uvicorn
```

### 2. Start the Server

```bash
# Using the startup script
python run_api_gateway.py

# Or directly with uvicorn
uvicorn services.api_gateway.main:app --reload --host 0.0.0.0 --port 8000
```

### 3. Access Documentation

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## Configuration

The API Gateway can be configured via environment variables:

```bash
# Server configuration
export API_HOST=0.0.0.0
export API_PORT=8000
export API_RELOAD=true
export API_LOG_LEVEL=info

# Database configuration (handled by connection manager)
export DATABASE_URL=postgresql://user:pass@localhost/starpower
export REDIS_URL=redis://localhost:6379

# Service configuration
export GNEWS_API_KEY=your_gnews_key
export OPENAI_API_KEY=your_openai_key
```

## Example Usage

### Process an Article

```bash
curl -X POST "http://localhost:8000/api/articles/process" \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://example.com/celebrity-news-article",
    "source": "example_news"
  }'
```

### Identify Celebrities

```bash
curl -X POST "http://localhost:8000/api/celebrities/identify" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Brad Pitt and Angelina Jolie were spotted at the premiere.",
    "context": "entertainment news"
  }'
```

### Trigger Pipeline Processing

```bash
curl -X POST "http://localhost:8000/api/pipeline/process" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "celebrity news",
    "max_articles": 10
  }'
```

## Architecture

The API Gateway follows a service-oriented architecture:

```
API Gateway
├── Routes (FastAPI routers)
│   ├── Health
│   ├── Articles
│   ├── Celebrities
│   ├── Astrology
│   └── Pipeline
├── Models (Pydantic schemas)
├── Middleware (CORS, timing, etc.)
└── Service Dependencies
    ├── Database Services
    ├── News Pipeline
    ├── LLM Processor
    ├── Astrology Engine
    └── Pipeline Orchestrator
```

## Error Handling

The API Gateway provides consistent error responses:

```json
{
  "error": "validation_error",
  "message": "Invalid input data",
  "details": {
    "field": "url",
    "issue": "Invalid URL format"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Testing

Run the integration test to verify everything is working:

```bash
python test_api_gateway.py
```

## Development

### Adding New Endpoints

1. Create route handler in appropriate module under `routes/`
2. Add Pydantic models to `models.py`
3. Register router in `main.py`
4. Update this documentation

### Service Integration

Services are injected via FastAPI's dependency injection system:

```python
async def get_service():
    return ServiceClass()

@router.get("/endpoint")
async def endpoint(service: ServiceClass = Depends(get_service)):
    return await service.do_something()
```

## Production Deployment

For production deployment:

1. Set `API_RELOAD=false`
2. Use a production ASGI server like Gunicorn with Uvicorn workers
3. Configure proper logging and monitoring
4. Set up load balancing if needed
5. Configure SSL/TLS termination

```bash
gunicorn services.api_gateway.main:app -w 4 -k uvicorn.workers.UvicornWorker
```
