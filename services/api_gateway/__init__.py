# API Gateway Service Package
"""
API Gateway Service - API Routing and Service Coordination

This package handles API routing and service coordination:
- main.py: FastAPI application entry point
- routes/: API route definitions
- middleware/: Request/response middleware
- auth/: Authentication and authorization
"""

from .main import app, create_app
from .models import (
    ArticleProcessRequest, ArticleResponse, ArticleListResponse,
    CelebrityResponse, CelebrityListResponse, CelebrityIdentifyRequest,
    AstrologyAnalyzeRequest, AstrologyResponse, ChartResponse,
    PipelineProcessRequest, PipelineStatusResponse,
    ErrorResponse, HealthResponse
)

__version__ = "1.0.0"
__all__ = [
    "app", "create_app",
    "ArticleProcessRequest", "ArticleResponse", "ArticleListResponse",
    "CelebrityResponse", "CelebrityListResponse", "CelebrityIdentifyRequest",
    "AstrologyAnalyzeRequest", "AstrologyResponse", "ChartResponse",
    "PipelineProcessRequest", "PipelineStatusResponse",
    "ErrorResponse", "HealthResponse"
]
