"""
FastAPI Application - Star Power API Gateway

Main FastAPI application with service integration and routing.
"""

import logging
import time
from contextlib import asynccontextmanager
from typing import Dict, Any
from datetime import datetime

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse

from .routes import articles, celebrities, astrology, pipeline, health, entities
from .models import ErrorResponse
from ..database.connection_manager import initialize_connections, close_connections
from ..database.models import DatabaseConfig, RedisConfig
from ..database.config import get_database_config, get_redis_config

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup/shutdown."""
    # Startup
    logger.info("Starting Star Power API Gateway...")
    try:
        # Get configurations for development environment
        db_config = get_database_config("development")
        redis_config = get_redis_config("development")

        await initialize_connections(db_config, redis_config)
        logger.info("Database connections initialized")
    except Exception as e:
        logger.error(f"Failed to initialize connections: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down Star Power API Gateway...")
    try:
        await close_connections()
        logger.info("Database connections closed")
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    
    app = FastAPI(
        title="Star Power API",
        description="Astrological analysis of celebrity news and events",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=lifespan
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Add request timing middleware
    @app.middleware("http")
    async def add_process_time_header(request, call_next):
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        return response
    
    # Include routers
    app.include_router(health.router, prefix="/health", tags=["Health"])
    app.include_router(articles.router, prefix="/api/articles", tags=["Articles"])
    app.include_router(celebrities.router, prefix="/api/celebrities", tags=["Celebrities"])
    app.include_router(entities.router, prefix="/api/entities", tags=["Entities"])
    app.include_router(astrology.router, prefix="/api/astrology", tags=["Astrology"])
    app.include_router(pipeline.router, prefix="/api/pipeline", tags=["Pipeline"])
    
    # Global exception handler
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request, exc):
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": exc.detail,
                "detail": getattr(exc, 'detail', None),
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request, exc):
        logger.error(f"Unhandled exception: {exc}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error",
                "detail": "An unexpected error occurred",
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }
        )
    
    return app


# Create the app instance
app = create_app()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "services.api_gateway.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
