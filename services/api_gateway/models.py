"""
API Gateway Models - Request/Response models for FastAPI endpoints
"""

from __future__ import annotations
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum

from ..database.models import (
    ArticleData, CelebrityData, ProcessingStatus,
    GenericEntity, EntityRelationship, EntityAction, EntityEndeavor
)


class ArticleProcessRequest(BaseModel):
    """Request to process a news article."""
    url: Optional[str] = Field(None, description="Article URL to process")
    content: Optional[str] = Field(None, description="Article content (if URL not provided)")
    title: Optional[str] = Field(None, description="Article title")
    source: Optional[str] = Field(None, description="Article source")
    
    class Config:
        json_schema_extra = {
            "example": {
                "url": "https://example.com/celebrity-news",
                "title": "Celebrity News Article",
                "source": "Entertainment Weekly"
            }
        }


class ArticleResponse(BaseModel):
    """Response containing article data."""
    id: str
    title: str
    content: str
    url: str
    published_at: datetime
    source: str
    entities: List[str]
    processing_status: ProcessingStatus
    celebrity_mentions: List[str]
    extraction_confidence: Optional[float]
    # Isolate entity data (optional, populated when include_entities=true)
    isolate_entities: Optional[List["EntityResponse"]] = None
    isolate_relationships: Optional[List["RelationshipResponse"]] = None
    isolate_actions: Optional[List["ActionResponse"]] = None
    isolate_endeavors: Optional[List["EndeavorResponse"]] = None
    isolate_processing_status: Optional[str] = None
    isolate_confidence_score: Optional[float] = None
    created_at: datetime
    updated_at: datetime
    
    @classmethod
    def from_article_data(
        cls,
        article: ArticleData,
        isolate_entities: Optional[List[EntityResponse]] = None,
        isolate_relationships: Optional[List[RelationshipResponse]] = None,
        isolate_actions: Optional[List[ActionResponse]] = None,
        isolate_endeavors: Optional[List[EndeavorResponse]] = None
    ) -> "ArticleResponse":
        """Create response from ArticleData model with optional Isolate data."""
        return cls(
            id=article.id,
            title=article.title,
            content=article.content,
            url=article.url,
            published_at=article.published_at,
            source=article.source,
            entities=article.entities,
            processing_status=article.processing_status,
            celebrity_mentions=article.celebrity_mentions,
            extraction_confidence=article.extraction_confidence,
            isolate_entities=isolate_entities,
            isolate_relationships=isolate_relationships,
            isolate_actions=isolate_actions,
            isolate_endeavors=isolate_endeavors,
            isolate_processing_status=getattr(article, 'isolate_processing_status', None),
            isolate_confidence_score=getattr(article, 'isolate_confidence_score', None),
            created_at=article.created_at,
            updated_at=article.updated_at
        )


class ArticleListResponse(BaseModel):
    """Response containing list of articles with pagination."""
    articles: List[ArticleResponse]
    total: int
    page: int
    per_page: int
    pages: int
    has_next: bool
    has_prev: bool


# Isolate Entity Models

class EntityResponse(BaseModel):
    """Response containing entity data."""
    id: str
    name: str
    entity_type: str
    confidence_score: float
    inception_date: Optional[datetime]
    inception_time: Optional[str]
    inception_location: Optional[str]
    created_at: datetime
    updated_at: datetime

    @classmethod
    def from_generic_entity(cls, entity: GenericEntity) -> "EntityResponse":
        """Create response from GenericEntity model."""
        return cls(
            id=entity.id,
            name=entity.name,
            entity_type=entity.entity_type,
            confidence_score=entity.confidence_score,
            inception_date=entity.inception_date,
            inception_time=entity.inception_time,
            inception_location=entity.inception_location,
            created_at=entity.created_at,
            updated_at=entity.updated_at
        )


class RelationshipResponse(BaseModel):
    """Response containing relationship data."""
    id: str
    entity1_id: str
    entity1_name: str
    entity2_id: str
    entity2_name: str
    relation_type: str
    confidence_score: float
    inception_date: Optional[datetime]
    inception_time: Optional[str]
    inception_location: Optional[str]
    created_at: datetime

    @classmethod
    def from_entity_relationship(cls, relationship: EntityRelationship) -> "RelationshipResponse":
        """Create response from EntityRelationship model."""
        return cls(
            id=relationship.id,
            entity1_id=relationship.entity1_id,
            entity1_name=relationship.entity1_name,
            entity2_id=relationship.entity2_id,
            entity2_name=relationship.entity2_name,
            relation_type=relationship.relation_type,
            confidence_score=relationship.confidence_score,
            inception_date=relationship.inception_date,
            inception_time=relationship.inception_time,
            inception_location=relationship.inception_location,
            created_at=relationship.created_at
        )


class ActionResponse(BaseModel):
    """Response containing action data."""
    id: str
    actor_id: str
    actor_name: str
    target_id: str
    target_name: str
    action_type: str
    confidence_score: float
    inception_date: Optional[datetime]
    inception_time: Optional[str]
    inception_location: Optional[str]
    created_at: datetime

    @classmethod
    def from_entity_action(cls, action: EntityAction) -> "ActionResponse":
        """Create response from EntityAction model."""
        return cls(
            id=action.id,
            actor_id=action.actor_id,
            actor_name=action.actor_name,
            target_id=action.target_id,
            target_name=action.target_name,
            action_type=action.action_type,
            confidence_score=action.confidence_score,
            inception_date=action.inception_date,
            inception_time=action.inception_time,
            inception_location=action.inception_location,
            created_at=action.created_at
        )


class EndeavorResponse(BaseModel):
    """Response containing endeavor data."""
    id: str
    entity_id: str
    entity_name: str
    endeavor_type: str
    confidence_score: float
    inception_date: Optional[datetime]
    inception_time: Optional[str]
    inception_location: Optional[str]
    created_at: datetime

    @classmethod
    def from_entity_endeavor(cls, endeavor: EntityEndeavor) -> "EndeavorResponse":
        """Create response from EntityEndeavor model."""
        return cls(
            id=endeavor.id,
            entity_id=endeavor.entity_id,
            entity_name=endeavor.entity_name,
            endeavor_type=endeavor.endeavor_type,
            confidence_score=endeavor.confidence_score,
            inception_date=endeavor.inception_date,
            inception_time=endeavor.inception_time,
            inception_location=endeavor.inception_location,
            created_at=endeavor.created_at
        )


class EntityGraphResponse(BaseModel):
    """Response containing entity graph data."""
    entity_id: str
    entities: List[EntityResponse]
    relationships: List[RelationshipResponse]
    actions: List[ActionResponse]
    endeavors: List[EndeavorResponse]
    total_entities: int
    total_relationships: int
    total_actions: int
    total_endeavors: int


class CelebrityResponse(BaseModel):
    """Response containing celebrity data."""
    id: str
    name: str
    birth_date: Optional[datetime]
    birth_time: Optional[str]
    birth_location: Optional[str]
    birth_latitude: Optional[float]
    birth_longitude: Optional[float]
    birth_timezone: Optional[str]
    confidence_score: float
    enhanced: bool
    verified: bool
    aliases: List[str]
    professional_name: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    @classmethod
    def from_celebrity_data(cls, celebrity: CelebrityData) -> "CelebrityResponse":
        """Create response from CelebrityData model."""
        return cls(
            id=celebrity.id,
            name=celebrity.name,
            birth_date=celebrity.birth_date,
            birth_time=celebrity.birth_time,
            birth_location=celebrity.birth_location,
            birth_latitude=celebrity.birth_latitude,
            birth_longitude=celebrity.birth_longitude,
            birth_timezone=celebrity.birth_timezone,
            confidence_score=celebrity.confidence_score,
            enhanced=celebrity.enhanced,
            verified=celebrity.verified,
            aliases=celebrity.aliases,
            professional_name=celebrity.professional_name,
            created_at=celebrity.created_at,
            updated_at=celebrity.updated_at
        )


class CelebrityListResponse(BaseModel):
    """Response containing list of celebrities with pagination."""
    celebrities: List[CelebrityResponse]
    total: int
    page: int
    per_page: int
    pages: int
    has_next: bool
    has_prev: bool


class CelebrityIdentifyRequest(BaseModel):
    """Request to identify celebrities in text."""
    text: str = Field(..., description="Text content to analyze for celebrities")
    context: Optional[str] = Field(None, description="Additional context")
    
    class Config:
        json_schema_extra = {
            "example": {
                "text": "Brad Pitt and Angelina Jolie were spotted at the premiere.",
                "context": "Entertainment news article"
            }
        }


class AstrologyAnalyzeRequest(BaseModel):
    """Request for astrological analysis."""
    celebrity_id: Optional[str] = Field(None, description="Celebrity ID to analyze")
    birth_date: Optional[datetime] = Field(None, description="Birth date")
    birth_time: Optional[str] = Field(None, description="Birth time (HH:MM format)")
    birth_latitude: Optional[float] = Field(None, description="Birth latitude")
    birth_longitude: Optional[float] = Field(None, description="Birth longitude")
    
    class Config:
        json_schema_extra = {
            "example": {
                "celebrity_id": "celebrity-123",
                "birth_date": "1990-01-01T00:00:00Z",
                "birth_time": "12:00",
                "birth_latitude": 40.7128,
                "birth_longitude": -74.0060
            }
        }


class AstrologyResponse(BaseModel):
    """Response containing astrological analysis."""
    celebrity_name: Optional[str]
    birth_date: Optional[datetime]
    chart_data: Dict[str, Any]
    aspects: List[Dict[str, Any]]
    va_circuits: List[Dict[str, Any]]
    analysis_summary: Optional[str]
    processing_time_ms: Optional[float]
    created_at: datetime


class ChartResponse(BaseModel):
    """Response containing chart data for a celebrity."""
    celebrity_id: str
    celebrity_name: str
    chart_data: Dict[str, Any]
    va_circuits: List[Dict[str, Any]]
    aspects: List[Dict[str, Any]]
    created_at: datetime
    updated_at: datetime


class PipelineProcessRequest(BaseModel):
    """Request to trigger pipeline processing."""
    query: str = Field(default="celebrity news", description="News search query")
    max_articles: int = Field(default=20, ge=1, le=100, description="Maximum articles to process")
    
    class Config:
        json_schema_extra = {
            "example": {
                "query": "celebrity astrology news",
                "max_articles": 10
            }
        }


class PipelineStatusResponse(BaseModel):
    """Response containing pipeline job status."""
    job_id: str
    stage: str
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    retry_count: int
    last_error: Optional[str]
    celebrities_found: int
    astrology_completed: bool
    stage_timings: Dict[str, float]
    total_processing_time: Optional[float]


class ErrorResponse(BaseModel):
    """Standard error response."""
    error: str
    detail: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    model_config = {
        "json_encoders": {
            datetime: lambda v: v.isoformat()
        },
        "json_schema_extra": {
            "example": {
                "error": "Resource not found",
                "detail": "Article with ID 'abc123' does not exist",
                "timestamp": "2025-08-02T12:00:00Z"
            }
        }
    }


class HealthResponse(BaseModel):
    """Health check response."""
    status: str
    timestamp: str
    services: Dict[str, Dict[str, Any]]
    version: str = "1.0.0"
    
    class Config:
        json_schema_extra = {
            "example": {
                "status": "healthy",
                "timestamp": "2025-08-02T12:00:00Z",
                "services": {
                    "database": {"status": "healthy", "response_time_ms": 5.2},
                    "redis": {"status": "healthy", "response_time_ms": 1.1},
                    "llm_processor": {"status": "healthy", "response_time_ms": 150.0}
                },
                "version": "1.0.0"
            }
        }
