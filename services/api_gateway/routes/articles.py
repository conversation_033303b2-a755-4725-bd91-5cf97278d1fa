"""
Article Routes

Handles news article processing and retrieval endpoints.
"""

import logging
from typing import Optional, List
from datetime import datetime

from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import JSONResponse

from ..models import (
    ArticleProcessRequest, ArticleResponse, ArticleListResponse, ErrorResponse
)
from ...database.article_db_service import ArticleDBService
from ...database.generic_entity_db_service import GenericEntityDBService
from ...database.entity_graph_db_service import EntityGraphDBService
from ...database.models import ArticleData, ProcessingStatus
from ...news_pipeline.news_ingestion_service import NewsIngestionService, NewsIngestionConfig
from ...pipeline_orchestrator import SimplePipelineOrchestrator, PipelineConfig
from ..models import EntityResponse, RelationshipResponse, ActionResponse, EndeavorResponse

logger = logging.getLogger(__name__)

router = APIRouter()

# Service dependencies
async def get_article_db_service():
    """Get article database service instance."""
    return ArticleDBService()

async def get_news_ingestion_service():
    """Get news ingestion service instance."""
    config = NewsIngestionConfig()
    return NewsIngestionService(config)


async def get_pipeline_orchestrator():
    """Get pipeline orchestrator service instance."""
    config = PipelineConfig()
    return SimplePipelineOrchestrator(config)

async def get_generic_entity_db_service():
    """Get generic entity database service instance."""
    return GenericEntityDBService()

async def get_entity_graph_db_service():
    """Get entity graph database service instance."""
    return EntityGraphDBService()


@router.get("", response_model=ArticleListResponse)
@router.get("/", response_model=ArticleListResponse)
async def list_articles(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    status: Optional[ProcessingStatus] = Query(None, description="Filter by processing status"),
    source: Optional[str] = Query(None, description="Filter by source"),
    article_db: ArticleDBService = Depends(get_article_db_service)
):
    """
    List articles with pagination and filtering.
    
    Returns paginated list of articles with optional filtering by status and source.
    """
    try:
        # Build filters
        filters = {}
        if status:
            filters['processing_status'] = status
        if source:
            filters['source'] = source
        
        # Get paginated articles using search method
        paginated_result = await article_db.search_articles(
            source=source,
            status=status,
            page=page,
            per_page=per_page
        )
        
        # Convert to response models
        article_responses = [
            ArticleResponse.from_article_data(article) 
            for article in paginated_result.items
        ]
        
        return ArticleListResponse(
            articles=article_responses,
            total=paginated_result.total,
            page=paginated_result.page,
            per_page=paginated_result.per_page,
            pages=paginated_result.pages,
            has_next=paginated_result.has_next,
            has_prev=paginated_result.has_prev
        )
        
    except Exception as e:
        logger.error(f"Failed to list articles: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve articles")


@router.get("/{article_id}", response_model=ArticleResponse)
async def get_article(
    article_id: str,
    include_entities: bool = Query(False, description="Include Isolate entity data"),
    article_db: ArticleDBService = Depends(get_article_db_service),
    generic_entity_db: GenericEntityDBService = Depends(get_generic_entity_db_service),
    entity_graph_db: EntityGraphDBService = Depends(get_entity_graph_db_service)
):
    """
    Get article by ID.

    Returns detailed article information including processing status and metadata.
    Optionally includes Isolate entity data when include_entities=true.
    """
    try:
        article = await article_db.get_article_by_id(article_id)

        if not article:
            raise HTTPException(status_code=404, detail="Article not found")

        # Optionally include entity data
        isolate_entities = None
        isolate_relationships = None
        isolate_actions = None
        isolate_endeavors = None

        if include_entities:
            try:
                # Get entities for this article
                entities = await generic_entity_db.get_entities_for_article(article_id)
                isolate_entities = [EntityResponse.from_generic_entity(entity) for entity in entities]

                # Get relationships for this article
                relationships = await entity_graph_db.get_relationships_for_article(article_id)
                isolate_relationships = [RelationshipResponse.from_entity_relationship(rel) for rel in relationships]

                # Get actions for this article
                actions = await entity_graph_db.get_actions_for_article(article_id)
                isolate_actions = [ActionResponse.from_entity_action(action) for action in actions]

                # Get endeavors for this article
                endeavors = await entity_graph_db.get_endeavors_for_article(article_id)
                isolate_endeavors = [EndeavorResponse.from_entity_endeavor(endeavor) for endeavor in endeavors]

            except Exception as e:
                logger.warning(f"Failed to load entity data for article {article_id}: {e}")
                # Continue without entity data rather than failing the whole request

        return ArticleResponse.from_article_data(
            article,
            isolate_entities=isolate_entities,
            isolate_relationships=isolate_relationships,
            isolate_actions=isolate_actions,
            isolate_endeavors=isolate_endeavors
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get article {article_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve article")


@router.post("/process", response_model=ArticleResponse)
async def process_article(
    request: ArticleProcessRequest,
    news_service: NewsIngestionService = Depends(get_news_ingestion_service),
    article_db: ArticleDBService = Depends(get_article_db_service),
    pipeline_orchestrator: SimplePipelineOrchestrator = Depends(get_pipeline_orchestrator)
):
    """
    Process a news article.
    
    Accepts either a URL to fetch and process, or direct content.
    Returns the processed article with extracted entities and metadata.
    """
    try:
        if not request.url and not request.content:
            raise HTTPException(
                status_code=400, 
                detail="Either 'url' or 'content' must be provided"
            )
        
        # Process the article
        if request.url:
            # Fetch and process from URL
            articles = await news_service.process_article_url(request.url)
            if not articles:
                raise HTTPException(status_code=400, detail="Failed to process article from URL")
            article_data = articles[0]
        else:
            # Process provided content
            article_data = ArticleData(
                title=request.title or "Untitled",
                content=request.content,
                url=request.url or "",
                published_at=datetime.utcnow(),
                source=request.source or "direct_input",
                processing_status=ProcessingStatus.PENDING
            )
        
        # Save to database
        saved_article = await article_db.create_article(article_data)

        # Automatically trigger pipeline processing for the new article
        try:
            from ...pipeline_orchestrator import PipelineJob
            import asyncio

            # Create a pipeline job for this article
            job = PipelineJob(article_data=saved_article)
            job.started_at = datetime.utcnow()
            pipeline_orchestrator.active_jobs[job.job_id] = job

            # Start pipeline processing in background
            asyncio.create_task(pipeline_orchestrator._process_pipeline_job(job.job_id))

            logger.info(f"Started pipeline processing for article {saved_article.id} with job {job.job_id}")

        except Exception as e:
            logger.warning(f"Failed to trigger pipeline processing for article {saved_article.id}: {e}")
            # Don't fail the article creation if pipeline fails to start

        return ArticleResponse.from_article_data(saved_article)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to process article: {e}")
        raise HTTPException(status_code=500, detail="Failed to process article")


@router.put("/{article_id}/status")
async def update_article_status(
    article_id: str,
    status: ProcessingStatus,
    article_db: ArticleDBService = Depends(get_article_db_service)
):
    """
    Update article processing status.
    
    Allows manual status updates for article processing workflow.
    """
    try:
        # Get existing article
        article = await article_db.get_article_by_id(article_id)
        if not article:
            raise HTTPException(status_code=404, detail="Article not found")
        
        # Update status
        article.processing_status = status
        article.updated_at = datetime.utcnow()
        
        updated_article = await article_db.update_article(article)
        
        return ArticleResponse.from_article_data(updated_article)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update article status: {e}")
        raise HTTPException(status_code=500, detail="Failed to update article status")


@router.delete("/{article_id}")
async def delete_article(
    article_id: str,
    article_db: ArticleDBService = Depends(get_article_db_service)
):
    """
    Delete an article.
    
    Permanently removes article from the database.
    """
    try:
        # Check if article exists
        article = await article_db.get_article_by_id(article_id)
        if not article:
            raise HTTPException(status_code=404, detail="Article not found")
        
        # Delete article
        await article_db.delete_article(article_id)
        
        return {"message": "Article deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete article: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete article")
