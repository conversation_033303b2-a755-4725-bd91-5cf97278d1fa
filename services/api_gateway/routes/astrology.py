"""
Astrology Routes

Handles astrological analysis and chart generation endpoints.
"""

import logging
import asyncio
from datetime import datetime
from typing import Optional, Dict, Any

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse

from ..models import AstrologyAnalyzeRequest, AstrologyResponse, ChartResponse, ErrorResponse
from ...database.celebrity_db_service import CelebrityDBService
from ...astrology_engine.ephemeris_calculator import EphemerisCalculator
from ...astrology_engine.aspect_engine import AspectEngine
from ...astrology_engine.va_circuit_detector import VACircuitDetector

logger = logging.getLogger(__name__)

router = APIRouter()

# Service dependencies
async def get_celebrity_db_service():
    """Get celebrity database service instance."""
    return CelebrityDBService()

async def get_ephemeris_calculator():
    """Get ephemeris calculator service instance."""
    return EphemerisCalculator()

async def get_aspect_engine():
    """Get aspect engine service instance."""
    return AspectEngine()

async def get_va_circuit_detector():
    """Get VA circuit detector service instance."""
    return VACircuitDetector()


@router.post("/analyze", response_model=AstrologyResponse)
async def analyze_chart(
    request: AstrologyAnalyzeRequest,
    celebrity_db: CelebrityDBService = Depends(get_celebrity_db_service),
    ephemeris_calc: EphemerisCalculator = Depends(get_ephemeris_calculator),
    aspect_engine: AspectEngine = Depends(get_aspect_engine),
    va_detector: VACircuitDetector = Depends(get_va_circuit_detector)
):
    """
    Perform astrological analysis.
    
    Analyzes birth chart data and returns comprehensive astrological information
    including planetary positions, aspects, and VA circuits.
    """
    try:
        start_time = datetime.utcnow()
        
        # Get celebrity data if ID provided
        celebrity_name = None
        birth_date = request.birth_date
        birth_time = request.birth_time
        birth_latitude = request.birth_latitude
        birth_longitude = request.birth_longitude
        
        if request.celebrity_id:
            celebrity = await celebrity_db.get_celebrity_by_id(request.celebrity_id)
            if not celebrity:
                raise HTTPException(status_code=404, detail="Celebrity not found")
            
            celebrity_name = celebrity.name
            birth_date = celebrity.birth_date or birth_date
            birth_time = celebrity.birth_time or birth_time
            birth_latitude = celebrity.birth_latitude or birth_latitude
            birth_longitude = celebrity.birth_longitude or birth_longitude
        
        # Validate required data
        if not birth_date:
            raise HTTPException(status_code=400, detail="Birth date is required")
        if birth_latitude is None or birth_longitude is None:
            raise HTTPException(status_code=400, detail="Birth coordinates are required")
        
        # Calculate chart data
        chart_data = await ephemeris_calc.calculate_chart(
            birth_date=birth_date,
            birth_time=birth_time,
            latitude=birth_latitude,
            longitude=birth_longitude
        )
        
        # Calculate aspects
        aspects = aspect_engine.calculate_aspects(chart_data)
        
        # Detect VA circuits (crown jewel algorithm)
        va_circuits = await asyncio.wait_for(
            va_detector.detect_circuits(chart_data),
            timeout=2.0  # 2-second timeout as per requirements
        )
        
        # Calculate processing time
        processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
        
        return AstrologyResponse(
            celebrity_name=celebrity_name,
            birth_date=birth_date,
            chart_data=chart_data,
            aspects=aspects,
            va_circuits=va_circuits,
            analysis_summary=f"Analysis completed for {celebrity_name or 'chart'} with {len(va_circuits)} VA circuits detected",
            processing_time_ms=processing_time,
            created_at=datetime.utcnow()
        )
        
    except asyncio.TimeoutError:
        logger.error("VA circuit detection timed out")
        raise HTTPException(status_code=408, detail="Analysis timed out")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to analyze chart: {e}")
        raise HTTPException(status_code=500, detail="Failed to perform astrological analysis")


@router.get("/charts/{celebrity_id}", response_model=ChartResponse)
async def get_celebrity_chart(
    celebrity_id: str,
    celebrity_db: CelebrityDBService = Depends(get_celebrity_db_service),
    ephemeris_calc: EphemerisCalculator = Depends(get_ephemeris_calculator),
    aspect_engine: AspectEngine = Depends(get_aspect_engine),
    va_detector: VACircuitDetector = Depends(get_va_circuit_detector)
):
    """
    Get cached or generate chart data for a celebrity.
    
    Returns comprehensive chart data including planetary positions, aspects, and VA circuits.
    """
    try:
        # Get celebrity data
        celebrity = await celebrity_db.get_celebrity_by_id(celebrity_id)
        if not celebrity:
            raise HTTPException(status_code=404, detail="Celebrity not found")
        
        # Check if celebrity has birth data
        if not celebrity.birth_date:
            raise HTTPException(status_code=400, detail="Celebrity has no birth date")
        if celebrity.birth_latitude is None or celebrity.birth_longitude is None:
            raise HTTPException(status_code=400, detail="Celebrity has no birth coordinates")
        
        # Calculate chart data
        chart_data = await ephemeris_calc.calculate_chart(
            birth_date=celebrity.birth_date,
            birth_time=celebrity.birth_time,
            latitude=celebrity.birth_latitude,
            longitude=celebrity.birth_longitude
        )
        
        # Calculate aspects
        aspects = aspect_engine.calculate_aspects(chart_data)
        
        # Detect VA circuits
        va_circuits = await asyncio.wait_for(
            va_detector.detect_circuits(chart_data),
            timeout=2.0
        )
        
        return ChartResponse(
            celebrity_id=celebrity.id,
            celebrity_name=celebrity.name,
            chart_data=chart_data,
            va_circuits=va_circuits,
            aspects=aspects,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
    except asyncio.TimeoutError:
        logger.error(f"VA circuit detection timed out for celebrity {celebrity_id}")
        raise HTTPException(status_code=408, detail="Chart analysis timed out")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get celebrity chart: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve celebrity chart")


@router.get("/aspects/{celebrity_id}")
async def get_celebrity_aspects(
    celebrity_id: str,
    celebrity_db: CelebrityDBService = Depends(get_celebrity_db_service),
    ephemeris_calc: EphemerisCalculator = Depends(get_ephemeris_calculator),
    aspect_engine: AspectEngine = Depends(get_aspect_engine)
):
    """
    Get astrological aspects for a celebrity.
    
    Returns detailed aspect information including planetary relationships and orbs.
    """
    try:
        # Get celebrity data
        celebrity = await celebrity_db.get_celebrity_by_id(celebrity_id)
        if not celebrity:
            raise HTTPException(status_code=404, detail="Celebrity not found")
        
        # Check birth data
        if not celebrity.birth_date:
            raise HTTPException(status_code=400, detail="Celebrity has no birth date")
        if celebrity.birth_latitude is None or celebrity.birth_longitude is None:
            raise HTTPException(status_code=400, detail="Celebrity has no birth coordinates")
        
        # Calculate chart data
        chart_data = await ephemeris_calc.calculate_chart(
            birth_date=celebrity.birth_date,
            birth_time=celebrity.birth_time,
            latitude=celebrity.birth_latitude,
            longitude=celebrity.birth_longitude
        )
        
        # Calculate aspects
        aspects = aspect_engine.calculate_aspects(chart_data)
        
        return {
            "celebrity_id": celebrity.id,
            "celebrity_name": celebrity.name,
            "aspects": aspects,
            "total_aspects": len(aspects),
            "calculated_at": datetime.utcnow().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get celebrity aspects: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve celebrity aspects")


@router.get("/va-circuits/{celebrity_id}")
async def get_celebrity_va_circuits(
    celebrity_id: str,
    celebrity_db: CelebrityDBService = Depends(get_celebrity_db_service),
    ephemeris_calc: EphemerisCalculator = Depends(get_ephemeris_calculator),
    va_detector: VACircuitDetector = Depends(get_va_circuit_detector)
):
    """
    Get VA circuits for a celebrity using the crown jewel algorithm.
    
    Returns detailed VA circuit analysis with harmonic information.
    """
    try:
        # Get celebrity data
        celebrity = await celebrity_db.get_celebrity_by_id(celebrity_id)
        if not celebrity:
            raise HTTPException(status_code=404, detail="Celebrity not found")
        
        # Check birth data
        if not celebrity.birth_date:
            raise HTTPException(status_code=400, detail="Celebrity has no birth date")
        if celebrity.birth_latitude is None or celebrity.birth_longitude is None:
            raise HTTPException(status_code=400, detail="Celebrity has no birth coordinates")
        
        # Calculate chart data
        chart_data = await ephemeris_calc.calculate_chart(
            birth_date=celebrity.birth_date,
            birth_time=celebrity.birth_time,
            latitude=celebrity.birth_latitude,
            longitude=celebrity.birth_longitude
        )
        
        # Detect VA circuits with timeout
        va_circuits = await asyncio.wait_for(
            va_detector.detect_circuits(chart_data),
            timeout=2.0
        )
        
        return {
            "celebrity_id": celebrity.id,
            "celebrity_name": celebrity.name,
            "va_circuits": va_circuits,
            "total_circuits": len(va_circuits),
            "calculated_at": datetime.utcnow().isoformat()
        }
        
    except asyncio.TimeoutError:
        logger.error(f"VA circuit detection timed out for celebrity {celebrity_id}")
        raise HTTPException(status_code=408, detail="VA circuit analysis timed out")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get celebrity VA circuits: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve celebrity VA circuits")
