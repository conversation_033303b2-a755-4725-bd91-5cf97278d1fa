"""
Celebrity Routes

Handles celebrity data and identification endpoints.
"""

import logging
from typing import Op<PERSON>, <PERSON>, Tuple

from fastapi import APIRouter, HTTPException, Query, Depends
from fastapi.responses import JSONResponse

from ..models import (
    CelebrityResponse, CelebrityListResponse, CelebrityIdentifyRequest, ErrorResponse
)
from ...database.celebrity_db_service import CelebrityDBService
from ...database.models import CelebrityData
from ...llm_processor.llm_orchestrator import LLMOrchestrator
from ...llm_processor.models import LLMConfig, LLMProvider

logger = logging.getLogger(__name__)

router = APIRouter()

# Service dependencies
async def get_celebrity_db_service():
    """Get celebrity database service instance."""
    return CelebrityDBService()

async def get_llm_orchestrator():
    """Get LLM orchestrator service instance."""
    config = LLMConfig(
        primary_provider=LLMProvider.OLLAMA,
        fallback_provider=LLMProvider.OPENAI
    )
    return LLMOrchestrator(config)


@router.get("/", response_model=CelebrityListResponse)
async def list_celebrities(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    verified: Optional[bool] = Query(None, description="Filter by verification status"),
    enhanced: Optional[bool] = Query(None, description="Filter by enhancement status"),
    celebrity_db: CelebrityDBService = Depends(get_celebrity_db_service)
):
    """
    List celebrities with pagination and filtering.
    
    Returns paginated list of celebrities with optional filtering by verification and enhancement status.
    """
    try:
        # Build filters
        filters = {}
        if verified is not None:
            filters['verified'] = verified
        if enhanced is not None:
            filters['enhanced'] = enhanced
        
        # Get paginated celebrities using search method
        paginated_result = await celebrity_db.search_celebrities(
            verified_only=verified,
            enhanced_only=enhanced,
            page=page,
            per_page=per_page
        )
        
        # Convert to response models
        celebrity_responses = [
            CelebrityResponse.from_celebrity_data(celebrity) 
            for celebrity in paginated_result.items
        ]
        
        return CelebrityListResponse(
            celebrities=celebrity_responses,
            total=paginated_result.total,
            page=paginated_result.page,
            per_page=paginated_result.per_page,
            pages=paginated_result.pages,
            has_next=paginated_result.has_next,
            has_prev=paginated_result.has_prev
        )
        
    except Exception as e:
        logger.error(f"Failed to list celebrities: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve celebrities")


@router.get("/{celebrity_id}", response_model=CelebrityResponse)
async def get_celebrity(
    celebrity_id: str,
    celebrity_db: CelebrityDBService = Depends(get_celebrity_db_service)
):
    """
    Get celebrity by ID.
    
    Returns detailed celebrity information including birth data and metadata.
    """
    try:
        celebrity = await celebrity_db.get_celebrity_by_id(celebrity_id)
        
        if not celebrity:
            raise HTTPException(status_code=404, detail="Celebrity not found")
        
        return CelebrityResponse.from_celebrity_data(celebrity)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get celebrity {celebrity_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve celebrity")


@router.get("/search/{name}")
async def search_celebrities(
    name: str,
    fuzzy_match: bool = Query(True, description="Enable fuzzy name matching"),
    celebrity_db: CelebrityDBService = Depends(get_celebrity_db_service)
):
    """
    Search celebrities by name.
    
    Returns list of celebrities matching the search name with confidence scores.
    """
    try:
        # Search for celebrities
        matches: List[Tuple[CelebrityData, float]] = await celebrity_db.find_celebrity_by_name(
            name=name,
            fuzzy_match=fuzzy_match
        )
        
        # Convert to response format
        results = []
        for celebrity, confidence in matches:
            celebrity_response = CelebrityResponse.from_celebrity_data(celebrity)
            results.append({
                "celebrity": celebrity_response,
                "match_confidence": confidence
            })
        
        return {
            "query": name,
            "matches": results,
            "total_matches": len(results)
        }
        
    except Exception as e:
        logger.error(f"Failed to search celebrities for '{name}': {e}")
        raise HTTPException(status_code=500, detail="Failed to search celebrities")


@router.post("/identify")
async def identify_celebrities(
    request: CelebrityIdentifyRequest,
    llm_orchestrator: LLMOrchestrator = Depends(get_llm_orchestrator)
):
    """
    Identify celebrities in text content.
    
    Uses LLM processing to extract and identify celebrity mentions from text.
    Returns list of identified celebrities with confidence scores.
    """
    try:
        # Process text with LLM orchestrator
        response = await llm_orchestrator.identify_celebrities(
            article_title=request.context or "Text Analysis",
            article_content=request.text
        )
        
        # Convert to response format
        identified_celebrities = []
        for celebrity_match in response.celebrities:
            identified_celebrities.append({
                "name": celebrity_match.name,
                "confidence": celebrity_match.confidence,
                "context": celebrity_match.context,
                "aliases": celebrity_match.aliases or []
            })
        
        return {
            "text": request.text,
            "celebrities": identified_celebrities,
            "total_identified": len(identified_celebrities),
            "processing_time_ms": response.processing_time_ms,
            "provider_used": response.provider_used.value if response.provider_used else None
        }
        
    except Exception as e:
        logger.error(f"Failed to identify celebrities: {e}")
        raise HTTPException(status_code=500, detail="Failed to identify celebrities")


@router.post("/", response_model=CelebrityResponse)
async def create_celebrity(
    celebrity_data: dict,
    celebrity_db: CelebrityDBService = Depends(get_celebrity_db_service)
):
    """
    Create a new celebrity record.
    
    Accepts celebrity data and creates a new record in the database.
    """
    try:
        # Convert dict to CelebrityData model
        celebrity = CelebrityData(**celebrity_data)
        
        # Save to database
        saved_celebrity = await celebrity_db.create_celebrity(celebrity)
        
        return CelebrityResponse.from_celebrity_data(saved_celebrity)
        
    except Exception as e:
        logger.error(f"Failed to create celebrity: {e}")
        raise HTTPException(status_code=500, detail="Failed to create celebrity")


@router.put("/{celebrity_id}", response_model=CelebrityResponse)
async def update_celebrity(
    celebrity_id: str,
    celebrity_data: dict,
    celebrity_db: CelebrityDBService = Depends(get_celebrity_db_service)
):
    """
    Update celebrity information.
    
    Updates existing celebrity record with new data.
    """
    try:
        # Get existing celebrity
        existing_celebrity = await celebrity_db.get_celebrity_by_id(celebrity_id)
        if not existing_celebrity:
            raise HTTPException(status_code=404, detail="Celebrity not found")
        
        # Update fields
        for field, value in celebrity_data.items():
            if hasattr(existing_celebrity, field):
                setattr(existing_celebrity, field, value)
        
        # Save updated celebrity
        updated_celebrity = await celebrity_db.update_celebrity(existing_celebrity)
        
        return CelebrityResponse.from_celebrity_data(updated_celebrity)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update celebrity: {e}")
        raise HTTPException(status_code=500, detail="Failed to update celebrity")


@router.delete("/{celebrity_id}")
async def delete_celebrity(
    celebrity_id: str,
    celebrity_db: CelebrityDBService = Depends(get_celebrity_db_service)
):
    """
    Delete a celebrity record.
    
    Permanently removes celebrity from the database.
    """
    try:
        # Check if celebrity exists
        celebrity = await celebrity_db.get_celebrity_by_id(celebrity_id)
        if not celebrity:
            raise HTTPException(status_code=404, detail="Celebrity not found")
        
        # Delete celebrity
        await celebrity_db.delete_celebrity(celebrity_id)
        
        return {"message": "Celebrity deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete celebrity: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete celebrity")
