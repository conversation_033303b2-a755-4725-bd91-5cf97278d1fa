"""
API Routes for Entity Management

Handles entity graph data from Isolate processing including entities,
relationships, actions, and endeavors.
"""

import logging
from typing import Optional, List
from fastapi import APIRouter, HTTPException, Depends, Query

from ..models import (
    EntityResponse, RelationshipResponse, ActionResponse, EndeavorResponse,
    EntityGraphResponse, ErrorResponse
)
from ...database.generic_entity_db_service import GenericEntityDBService
from ...database.entity_graph_db_service import EntityGraphDBService

logger = logging.getLogger(__name__)

router = APIRouter()

# Service dependencies
async def get_generic_entity_db_service():
    """Get generic entity database service instance."""
    return GenericEntityDBService()

async def get_entity_graph_db_service():
    """Get entity graph database service instance."""
    return EntityGraphDBService()


@router.get("/", response_model=List[EntityResponse])
async def list_entities(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(20, ge=1, le=100, description="Items per page"),
    entity_type: Optional[str] = Query(None, description="Filter by entity type"),
    search: Optional[str] = Query(None, description="Search entity names"),
    generic_entity_db: GenericEntityDBService = Depends(get_generic_entity_db_service)
):
    """
    List entities with pagination and filtering.
    
    Returns paginated list of entities extracted from Isolate processing.
    """
    try:
        # Get paginated entities
        paginated_result = await generic_entity_db.search_entities(
            entity_type=entity_type,
            search_term=search,
            page=page,
            per_page=per_page
        )
        
        # Convert to response models
        entity_responses = [
            EntityResponse.from_generic_entity(entity) 
            for entity in paginated_result.items
        ]
        
        return entity_responses
        
    except Exception as e:
        logger.error(f"Failed to list entities: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve entities")


@router.get("/{entity_id}", response_model=EntityResponse)
async def get_entity(
    entity_id: str,
    generic_entity_db: GenericEntityDBService = Depends(get_generic_entity_db_service)
):
    """
    Get entity by ID.
    
    Returns detailed entity information.
    """
    try:
        entity = await generic_entity_db.get_entity_by_id(entity_id)
        
        if not entity:
            raise HTTPException(status_code=404, detail="Entity not found")
        
        return EntityResponse.from_generic_entity(entity)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get entity {entity_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve entity")


@router.get("/{entity_id}/graph", response_model=EntityGraphResponse)
async def get_entity_graph(
    entity_id: str,
    depth: int = Query(1, ge=1, le=3, description="Graph traversal depth"),
    generic_entity_db: GenericEntityDBService = Depends(get_generic_entity_db_service),
    entity_graph_db: EntityGraphDBService = Depends(get_entity_graph_db_service)
):
    """
    Get entity graph including relationships, actions, and endeavors.
    
    Returns the complete entity graph up to specified depth.
    """
    try:
        # Verify entity exists
        entity = await generic_entity_db.get_entity_by_id(entity_id)
        if not entity:
            raise HTTPException(status_code=404, detail="Entity not found")
        
        # Get entity graph
        graph_data = await entity_graph_db.get_entity_graph(entity_id, depth=depth)
        
        # Convert to response models
        entities = [EntityResponse.from_generic_entity(entity)]
        relationships = [
            RelationshipResponse.from_entity_relationship(rel) 
            for rel in graph_data.get('relationships', [])
        ]
        actions = [
            ActionResponse.from_entity_action(action) 
            for action in graph_data.get('actions_as_actor', []) + graph_data.get('actions_as_target', [])
        ]
        endeavors = [
            EndeavorResponse.from_entity_endeavor(endeavor) 
            for endeavor in graph_data.get('endeavors', [])
        ]
        
        return EntityGraphResponse(
            entity_id=entity_id,
            entities=entities,
            relationships=relationships,
            actions=actions,
            endeavors=endeavors,
            total_entities=len(entities),
            total_relationships=len(relationships),
            total_actions=len(actions),
            total_endeavors=len(endeavors)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get entity graph for {entity_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve entity graph")


@router.get("/{entity_id}/relationships", response_model=List[RelationshipResponse])
async def get_entity_relationships(
    entity_id: str,
    entity_graph_db: EntityGraphDBService = Depends(get_entity_graph_db_service)
):
    """
    Get all relationships for an entity.
    
    Returns relationships where the entity is either entity1 or entity2.
    """
    try:
        relationships = await entity_graph_db.get_relationships_for_entity(entity_id)
        
        return [
            RelationshipResponse.from_entity_relationship(rel) 
            for rel in relationships
        ]
        
    except Exception as e:
        logger.error(f"Failed to get relationships for entity {entity_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve relationships")


@router.get("/{entity_id}/actions", response_model=List[ActionResponse])
async def get_entity_actions(
    entity_id: str,
    as_actor: Optional[bool] = Query(None, description="Filter by actor role (true) or target role (false)"),
    entity_graph_db: EntityGraphDBService = Depends(get_entity_graph_db_service)
):
    """
    Get all actions for an entity.
    
    Returns actions where the entity is either the actor or target.
    """
    try:
        if as_actor is True:
            actions = await entity_graph_db.get_actions_for_entity(entity_id, as_actor=True)
        elif as_actor is False:
            actions = await entity_graph_db.get_actions_for_entity(entity_id, as_actor=False)
        else:
            # Get both actor and target actions
            actor_actions = await entity_graph_db.get_actions_for_entity(entity_id, as_actor=True)
            target_actions = await entity_graph_db.get_actions_for_entity(entity_id, as_actor=False)
            actions = actor_actions + target_actions
        
        return [
            ActionResponse.from_entity_action(action) 
            for action in actions
        ]
        
    except Exception as e:
        logger.error(f"Failed to get actions for entity {entity_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve actions")


@router.get("/{entity_id}/endeavors", response_model=List[EndeavorResponse])
async def get_entity_endeavors(
    entity_id: str,
    entity_graph_db: EntityGraphDBService = Depends(get_entity_graph_db_service)
):
    """
    Get all endeavors for an entity.
    
    Returns endeavors associated with the entity.
    """
    try:
        endeavors = await entity_graph_db.get_endeavors_for_entity(entity_id)
        
        return [
            EndeavorResponse.from_entity_endeavor(endeavor) 
            for endeavor in endeavors
        ]
        
    except Exception as e:
        logger.error(f"Failed to get endeavors for entity {entity_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve endeavors")
