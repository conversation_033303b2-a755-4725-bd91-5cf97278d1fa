"""
Health Check Routes

Provides system health monitoring endpoints.
"""

import logging
import time
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

from ..models import HealthResponse
from ...database.connection_manager import get_connection_manager
from ...pipeline_orchestrator import SimplePipelineOrchestrator

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=HealthResponse)
async def health_check():
    """
    Get overall system health status.
    
    Returns comprehensive health information for all services.
    """
    try:
        services = {}
        overall_status = "healthy"
        
        # Check database connection
        try:
            start_time = time.time()
            connection_manager = await get_connection_manager()
            
            # Test database connection
            async with connection_manager.database.get_session() as session:
                from sqlalchemy import text
                await session.execute(text("SELECT 1"))
            
            db_response_time = (time.time() - start_time) * 1000
            services["database"] = {
                "status": "healthy",
                "response_time_ms": round(db_response_time, 2)
            }
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            services["database"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            overall_status = "degraded"
        
        # Check Redis connection
        try:
            start_time = time.time()
            connection_manager = await get_connection_manager()
            
            # Test Redis connection
            await connection_manager.cache.client.ping()
            
            redis_response_time = (time.time() - start_time) * 1000
            services["redis"] = {
                "status": "healthy",
                "response_time_ms": round(redis_response_time, 2)
            }
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            services["redis"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            overall_status = "degraded"
        
        # Check pipeline orchestrator
        try:
            orchestrator = SimplePipelineOrchestrator()
            pipeline_health = orchestrator.get_health_status()
            services["pipeline_orchestrator"] = pipeline_health
        except Exception as e:
            logger.error(f"Pipeline orchestrator health check failed: {e}")
            services["pipeline_orchestrator"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            overall_status = "degraded"
        
        return HealthResponse(
            status=overall_status,
            timestamp=datetime.utcnow().isoformat() + "Z",
            services=services
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")


@router.get("/database")
async def database_health():
    """Check database connection health."""
    try:
        start_time = time.time()
        connection_manager = await get_connection_manager()
        
        async with connection_manager.database.get_session() as session:
            from sqlalchemy import text
            result = await session.execute(text("SELECT version()"))
            version = result.scalar()
        
        response_time = (time.time() - start_time) * 1000
        
        return {
            "status": "healthy",
            "response_time_ms": round(response_time, 2),
            "version": version
        }
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Database unhealthy: {e}")


@router.get("/redis")
async def redis_health():
    """Check Redis connection health."""
    try:
        start_time = time.time()
        connection_manager = await get_connection_manager()
        
        # Test Redis with ping and info
        await connection_manager.cache.client.ping()
        info = await connection_manager.cache.client.info()
        
        response_time = (time.time() - start_time) * 1000
        
        return {
            "status": "healthy",
            "response_time_ms": round(response_time, 2),
            "version": info.get("redis_version", "unknown"),
            "memory_usage": info.get("used_memory_human", "unknown")
        }
    except Exception as e:
        logger.error(f"Redis health check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Redis unhealthy: {e}")


@router.get("/pipeline")
async def pipeline_health():
    """Check pipeline orchestrator health."""
    try:
        orchestrator = SimplePipelineOrchestrator()
        health_status = orchestrator.get_health_status()
        stats = orchestrator.get_pipeline_stats()
        
        return {
            "health": health_status,
            "stats": stats
        }
    except Exception as e:
        logger.error(f"Pipeline health check failed: {e}")
        raise HTTPException(status_code=503, detail=f"Pipeline unhealthy: {e}")
