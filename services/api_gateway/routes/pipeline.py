"""
Pipeline Routes

Handles pipeline orchestration and job management endpoints.
"""

import logging
from typing import Optional, List

from fastapi import APIRouter, HTTPException, Query, Depends, BackgroundTasks
from fastapi.responses import JSONResponse

from ..models import PipelineProcessRequest, PipelineStatusResponse, ErrorResponse
from ...pipeline_orchestrator import SimplePipelineOrchestrator, PipelineConfig

logger = logging.getLogger(__name__)

router = APIRouter()

# Service dependencies
async def get_pipeline_orchestrator():
    """Get pipeline orchestrator service instance."""
    config = PipelineConfig(
        enable_detailed_logging=True,
        enable_performance_tracking=True
    )
    return SimplePipelineOrchestrator(config)


@router.post("/process")
async def trigger_pipeline_processing(
    request: PipelineProcessRequest,
    background_tasks: BackgroundTasks,
    orchestrator: SimplePipelineOrchestrator = Depends(get_pipeline_orchestrator)
):
    """
    Trigger pipeline processing for news articles.
    
    Starts background processing of news articles through the complete pipeline:
    news ingestion → celebrity extraction → astrology analysis.
    
    Returns job IDs for tracking progress.
    """
    try:
        # Start pipeline processing in background
        job_ids = await orchestrator.process_news_batch(
            query=request.query,
            max_articles=request.max_articles
        )
        
        if not job_ids:
            raise HTTPException(status_code=400, detail="No articles found for processing")
        
        return {
            "message": "Pipeline processing started",
            "job_ids": job_ids,
            "total_jobs": len(job_ids),
            "query": request.query,
            "max_articles": request.max_articles
        }
        
    except Exception as e:
        logger.error(f"Failed to trigger pipeline processing: {e}")
        raise HTTPException(status_code=500, detail="Failed to start pipeline processing")


@router.get("/status/{job_id}", response_model=PipelineStatusResponse)
async def get_job_status(
    job_id: str,
    orchestrator: SimplePipelineOrchestrator = Depends(get_pipeline_orchestrator)
):
    """
    Get status of a specific pipeline job.
    
    Returns detailed status information including current stage, timing, and results.
    """
    try:
        job_status = orchestrator.get_job_status(job_id)
        
        if not job_status:
            raise HTTPException(status_code=404, detail="Job not found")
        
        return PipelineStatusResponse(**job_status)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get job status: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve job status")


@router.get("/stats")
async def get_pipeline_stats(
    orchestrator: SimplePipelineOrchestrator = Depends(get_pipeline_orchestrator)
):
    """
    Get comprehensive pipeline statistics.
    
    Returns performance metrics, success rates, and processing statistics.
    """
    try:
        stats = orchestrator.get_pipeline_stats()
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get pipeline stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve pipeline statistics")


@router.get("/health")
async def get_pipeline_health(
    orchestrator: SimplePipelineOrchestrator = Depends(get_pipeline_orchestrator)
):
    """
    Get pipeline health status.
    
    Returns health information for all pipeline services and components.
    """
    try:
        health_status = orchestrator.get_health_status()
        return health_status
        
    except Exception as e:
        logger.error(f"Failed to get pipeline health: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve pipeline health")


@router.get("/jobs")
async def list_active_jobs(
    orchestrator: SimplePipelineOrchestrator = Depends(get_pipeline_orchestrator)
):
    """
    List all active pipeline jobs.
    
    Returns information about currently running jobs.
    """
    try:
        active_jobs = []
        for job_id, job in orchestrator.active_jobs.items():
            job_info = {
                "job_id": job.job_id,
                "stage": job.stage.value,
                "created_at": job.created_at.isoformat(),
                "started_at": job.started_at.isoformat() if job.started_at else None,
                "retry_count": job.retry_count,
                "celebrities_found": len(job.celebrities),
                "article_title": job.article_data.title if job.article_data else None
            }
            active_jobs.append(job_info)
        
        return {
            "active_jobs": active_jobs,
            "total_active": len(active_jobs)
        }
        
    except Exception as e:
        logger.error(f"Failed to list active jobs: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve active jobs")


@router.get("/jobs/completed")
async def list_completed_jobs(
    limit: int = Query(50, ge=1, le=200, description="Maximum number of jobs to return"),
    orchestrator: SimplePipelineOrchestrator = Depends(get_pipeline_orchestrator)
):
    """
    List completed pipeline jobs.
    
    Returns information about recently completed jobs with results.
    """
    try:
        completed_jobs = []
        
        # Get the most recent completed jobs
        recent_jobs = orchestrator.completed_jobs[-limit:] if orchestrator.completed_jobs else []
        
        for job in recent_jobs:
            job_info = {
                "job_id": job.job_id,
                "stage": job.stage.value,
                "created_at": job.created_at.isoformat(),
                "started_at": job.started_at.isoformat() if job.started_at else None,
                "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                "total_processing_time": job.total_processing_time,
                "retry_count": job.retry_count,
                "celebrities_found": len(job.celebrities),
                "astrology_completed": job.astrology_results is not None,
                "article_title": job.article_data.title if job.article_data else None,
                "last_error": job.last_error
            }
            completed_jobs.append(job_info)
        
        return {
            "completed_jobs": completed_jobs,
            "total_returned": len(completed_jobs),
            "total_completed": len(orchestrator.completed_jobs)
        }
        
    except Exception as e:
        logger.error(f"Failed to list completed jobs: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve completed jobs")


@router.delete("/jobs/{job_id}")
async def cancel_job(
    job_id: str,
    orchestrator: SimplePipelineOrchestrator = Depends(get_pipeline_orchestrator)
):
    """
    Cancel an active pipeline job.
    
    Stops processing for the specified job if it's currently active.
    """
    try:
        if job_id not in orchestrator.active_jobs:
            raise HTTPException(status_code=404, detail="Job not found or already completed")
        
        # Move job to completed with failed status
        job = orchestrator.active_jobs[job_id]
        job.stage = job.stage.__class__.FAILED  # Set to failed stage
        job.last_error = "Job cancelled by user"
        
        # Move to completed jobs
        orchestrator.completed_jobs.append(job)
        del orchestrator.active_jobs[job_id]
        
        return {"message": f"Job {job_id} cancelled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel job: {e}")
        raise HTTPException(status_code=500, detail="Failed to cancel job")


@router.post("/jobs/{job_id}/retry")
async def retry_job(
    job_id: str,
    orchestrator: SimplePipelineOrchestrator = Depends(get_pipeline_orchestrator)
):
    """
    Retry a failed pipeline job.
    
    Restarts processing for a failed job from the beginning.
    """
    try:
        # Look for job in completed jobs
        job_to_retry = None
        for job in orchestrator.completed_jobs:
            if job.job_id == job_id:
                job_to_retry = job
                break
        
        if not job_to_retry:
            raise HTTPException(status_code=404, detail="Job not found")
        
        if job_to_retry.stage.value != "failed":
            raise HTTPException(status_code=400, detail="Job is not in failed state")
        
        # Reset job state and move back to active
        job_to_retry.stage = job_to_retry.stage.__class__.NEWS_INGESTION
        job_to_retry.retry_count = 0
        job_to_retry.last_error = None
        job_to_retry.completed_at = None
        job_to_retry.celebrities = []
        job_to_retry.astrology_results = None
        
        # Move back to active jobs
        orchestrator.active_jobs[job_id] = job_to_retry
        orchestrator.completed_jobs.remove(job_to_retry)
        
        # Start processing again
        import asyncio
        asyncio.create_task(orchestrator._process_pipeline_job(job_id))
        
        return {"message": f"Job {job_id} restarted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to retry job: {e}")
        raise HTTPException(status_code=500, detail="Failed to retry job")
