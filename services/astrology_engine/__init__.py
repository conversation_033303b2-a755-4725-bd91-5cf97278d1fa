# Astrology Engine Service Package
"""
Astrology Engine Service - Crown Jewel Preservation

This package contains the core astrological calculation engine including:
- va_circuit_detector.py: CROWN JEWEL 694-line VA algorithm (PRESERVE EXACTLY)
- ephemeris_calculator.py: Swiss Ephemeris integration with test mode fallback
- ephemeris_data_manager.py: Intelligent ephemeris data discovery and validation
- data_models.py: Core astrological data structures
- natal_analyzer.py: Natal chart analysis (future)
- mundane_analyzer.py: World event astrology (future)
- aspect_engine.py: Planetary aspect detection with multi-tradition support
- chart_generator.py: Chart data structure creation (future)

CRITICAL: The VA algorithm is the crown jewel technology and must be preserved exactly.
"""

from .va_circuit_detector import VACircuitDetector
from .ephemeris_calculator import EphemerisCalculator
from .ephemeris_data_manager import EphemerisDataManager, EphemerisConfig
from .aspect_engine import AspectEngine, AspectDefinition, DetectedAspect
from .data_models import (
    EphemerisData, CelestialBodyPosition, Location, ChartCalculationRequest,
    HouseSystem, CoordinateSystem, EphemerisStatus,
    create_test_ephemeris, create_circuit_test_ephemeris,
    VA_DEFAULT_BODIES, ALL_CELESTIAL_BODIES
)

__version__ = "1.0.0"
__all__ = [
    "VACircuitDetector",
    "EphemerisCalculator",
    "EphemerisDataManager",
    "EphemerisConfig",
    "AspectEngine",
    "AspectDefinition",
    "DetectedAspect",
    "EphemerisData",
    "CelestialBodyPosition",
    "Location",
    "ChartCalculationRequest",
    "HouseSystem",
    "CoordinateSystem",
    "EphemerisStatus",
    "create_test_ephemeris",
    "create_circuit_test_ephemeris",
    "VA_DEFAULT_BODIES",
    "ALL_CELESTIAL_BODIES"
]

# Crown jewel protection notice
CROWN_JEWEL_NOTICE = """
WARNING: This package contains the crown jewel VA algorithm.
Any modifications to va_circuit_detector.py require explicit authorization.
Algorithm integrity is monitored and validated.
"""
