"""
Planetary Aspect Detection Engine

This module provides configurable aspect detection for multiple astrological traditions
including Placidus, Vibrational, and Vedic systems. It supports custom aspect definitions
and orb tolerances for different schools of astrology.

Key Features:
- Multi-tradition aspect detection (Placidus, Vibrational, Vedic)
- Configurable aspect definitions and orb tolerances
- JSON-based configuration system
- Comprehensive aspect analysis with orb calculations
- Integration with existing astrology engine components
"""

import json
import math
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from pathlib import Path

from .data_models import CelestialBodyPosition


@dataclass
class AspectDefinition:
    """Definition of an astrological aspect."""
    name: str
    angle: float
    orb: float
    nature: str  # 'harmonious', 'challenging', 'neutral'
    symbol: str
    description: str


@dataclass
class DetectedAspect:
    """A detected aspect between two celestial bodies."""
    planet1: str
    planet2: str
    aspect_name: str
    exact_angle: float
    orb_difference: float
    is_applying: bool
    strength: float  # 0.0 to 1.0 based on orb tightness
    nature: str


class AspectEngine:
    """
    Configurable planetary aspect detection engine.
    
    Supports multiple astrological traditions through JSON configuration files.
    Provides comprehensive aspect analysis with orb calculations and strength ratings.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the aspect engine with configuration.
        
        Args:
            config_path: Path to aspect configuration JSON file.
                        If None, uses default Placidus configuration.
        """
        self.config_path = config_path
        self.aspect_definitions: Dict[str, AspectDefinition] = {}
        self.default_orb = 6.0
        self._load_configuration()
    
    def _load_configuration(self):
        """Load aspect configuration from JSON file or use defaults."""
        if self.config_path and Path(self.config_path).exists():
            with open(self.config_path, 'r') as f:
                config = json.load(f)
            self._parse_configuration(config)
        else:
            self._load_default_configuration()
    
    def _parse_configuration(self, config: Dict[str, Any]):
        """Parse aspect configuration from JSON data."""
        self.default_orb = config.get('default_orb', 6.0)
        
        for aspect_data in config.get('aspects', []):
            aspect_def = AspectDefinition(
                name=aspect_data['name'],
                angle=aspect_data['angle'],
                orb=aspect_data.get('orb', self.default_orb),
                nature=aspect_data.get('nature', 'neutral'),
                symbol=aspect_data.get('symbol', ''),
                description=aspect_data.get('description', '')
            )
            self.aspect_definitions[aspect_def.name] = aspect_def
    
    def _load_default_configuration(self):
        """Load default Placidus aspect configuration."""
        default_aspects = [
            {'name': 'conjunction', 'angle': 0, 'orb': 8, 'nature': 'neutral', 'symbol': '☌'},
            {'name': 'opposition', 'angle': 180, 'orb': 8, 'nature': 'challenging', 'symbol': '☍'},
            {'name': 'trine', 'angle': 120, 'orb': 8, 'nature': 'harmonious', 'symbol': '△'},
            {'name': 'square', 'angle': 90, 'orb': 8, 'nature': 'challenging', 'symbol': '□'},
            {'name': 'sextile', 'angle': 60, 'orb': 6, 'nature': 'harmonious', 'symbol': '⚹'},
            {'name': 'quincunx', 'angle': 150, 'orb': 3, 'nature': 'challenging', 'symbol': '⚻'},
            {'name': 'semisquare', 'angle': 45, 'orb': 2, 'nature': 'challenging', 'symbol': '∠'},
            {'name': 'sesquiquadrate', 'angle': 135, 'orb': 2, 'nature': 'challenging', 'symbol': '⚼'},
            {'name': 'semisextile', 'angle': 30, 'orb': 2, 'nature': 'neutral', 'symbol': '⚺'}
        ]
        
        for aspect_data in default_aspects:
            aspect_def = AspectDefinition(
                name=aspect_data['name'],
                angle=aspect_data['angle'],
                orb=aspect_data['orb'],
                nature=aspect_data['nature'],
                symbol=aspect_data['symbol'],
                description=f"{aspect_data['name'].title()} aspect ({aspect_data['angle']}°)"
            )
            self.aspect_definitions[aspect_def.name] = aspect_def
    
    def calculate_aspects(self, chart_data: Dict[str, Any]) -> List[DetectedAspect]:
        """
        Calculate all aspects between planets in the chart.
        
        Args:
            chart_data: Dictionary containing planet positions and configuration
                       Expected format: {
                           'planet_positions': {'sun': 134.0, 'moon': 220.5, ...},
                           'aspect_definitions': {...} (optional),
                           'orb': 6 (optional)
                       }
        
        Returns:
            List of DetectedAspect objects
        """
        planet_positions = chart_data.get('planet_positions', {})
        
        # Override configuration if provided in chart_data
        if 'aspect_definitions' in chart_data:
            self._override_aspects_from_chart_data(chart_data)
        
        aspects = []
        planet_names = list(planet_positions.keys())
        
        # Check all planet pairs
        for i, planet1 in enumerate(planet_names):
            for planet2 in planet_names[i+1:]:
                lon1 = planet_positions[planet1]
                lon2 = planet_positions[planet2]
                
                # Calculate angular separation
                angle_diff = self._calculate_angular_separation(lon1, lon2)
                
                # Check for aspects
                detected_aspect = self._check_for_aspect(planet1, planet2, angle_diff, lon1, lon2)
                if detected_aspect:
                    aspects.append(detected_aspect)
        
        return aspects
    
    def _override_aspects_from_chart_data(self, chart_data: Dict[str, Any]):
        """Override aspect definitions from chart data."""
        aspect_defs = chart_data.get('aspect_definitions', {})
        default_orb = chart_data.get('orb', self.default_orb)
        
        # Clear existing definitions and rebuild
        self.aspect_definitions.clear()
        
        for name, angle in aspect_defs.items():
            aspect_def = AspectDefinition(
                name=name,
                angle=angle,
                orb=default_orb,
                nature='neutral',
                symbol='',
                description=f"{name.title()} aspect ({angle}°)"
            )
            self.aspect_definitions[name] = aspect_def
    
    def _calculate_angular_separation(self, lon1: float, lon2: float) -> float:
        """Calculate the angular separation between two longitudes."""
        diff = abs(lon1 - lon2)
        return min(diff, 360 - diff)
    
    def _check_for_aspect(self, planet1: str, planet2: str, angle_diff: float, 
                         lon1: float, lon2: float) -> Optional[DetectedAspect]:
        """Check if the angular difference matches any aspect definition."""
        for aspect_name, aspect_def in self.aspect_definitions.items():
            orb_difference = abs(angle_diff - aspect_def.angle)
            
            if orb_difference <= aspect_def.orb:
                # Calculate strength (1.0 = exact, 0.0 = at orb limit)
                strength = 1.0 - (orb_difference / aspect_def.orb)
                
                # Determine if aspect is applying (planets moving closer)
                is_applying = self._is_aspect_applying(lon1, lon2, aspect_def.angle)
                
                return DetectedAspect(
                    planet1=planet1,
                    planet2=planet2,
                    aspect_name=aspect_name,
                    exact_angle=angle_diff,
                    orb_difference=orb_difference,
                    is_applying=is_applying,
                    strength=strength,
                    nature=aspect_def.nature
                )
        
        return None
    
    def _is_aspect_applying(self, lon1: float, lon2: float, target_angle: float) -> bool:
        """
        Determine if an aspect is applying (planets moving closer to exact aspect).
        
        Note: This is a simplified implementation. In practice, you would need
        planetary speeds to determine true applying/separating aspects.
        """
        # Simplified: assume slower planet is applying to faster planet
        # This would need actual planetary speeds for accurate calculation
        return True  # Placeholder implementation
    
    def is_aspect(self, angle: float, aspect_type: str, orb_tolerance: Optional[float] = None) -> bool:
        """
        Check if a given angle matches a specific aspect type.
        
        Args:
            angle: Angular separation in degrees
            aspect_type: Name of the aspect to check
            orb_tolerance: Custom orb tolerance (uses aspect default if None)
        
        Returns:
            True if the angle is within orb of the aspect
        """
        if aspect_type not in self.aspect_definitions:
            return False
        
        aspect_def = self.aspect_definitions[aspect_type]
        orb = orb_tolerance if orb_tolerance is not None else aspect_def.orb
        
        return abs(angle - aspect_def.angle) <= orb
    
    def configure_aspects(self, custom_config: Dict[str, Any]):
        """
        Load custom aspect rules for different traditions.

        Args:
            custom_config: Dictionary containing aspect configuration
        """
        # Clear existing definitions first
        self.aspect_definitions.clear()
        self._parse_configuration(custom_config)
    
    def get_aspect_definitions(self) -> Dict[str, AspectDefinition]:
        """Get current aspect definitions."""
        return self.aspect_definitions.copy()
    
    def to_json(self, aspects: List[DetectedAspect]) -> List[Dict[str, Any]]:
        """
        Convert detected aspects to JSON format.
        
        Args:
            aspects: List of DetectedAspect objects
        
        Returns:
            List of dictionaries in JSON format
        """
        return [
            {
                'pair': [aspect.planet1, aspect.planet2],
                'type': aspect.aspect_name,
                'angle': round(aspect.exact_angle, 2),
                'orb': round(aspect.orb_difference, 2),
                'strength': round(aspect.strength, 3),
                'nature': aspect.nature,
                'applying': aspect.is_applying
            }
            for aspect in aspects
        ]
