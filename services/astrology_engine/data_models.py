"""
Astrological Data Models

Core data structures for astrological calculations and analysis.
These models are used throughout the astrology engine services.
"""

from typing import Dict, List, Optional, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum


class HouseSystem(Enum):
    """Supported astrological house systems."""
    PLACIDUS = "placidus"
    KOCH = "koch"
    WHOLE_SIGN = "whole_sign"
    EQUAL_HOUSE = "equal_house"


class CoordinateSystem(Enum):
    """Supported coordinate systems."""
    TROPICAL = "tropical"
    SIDEREAL = "sidereal"


@dataclass
class Location:
    """Geographic location for chart calculations."""
    latitude: float
    longitude: float
    timezone: str
    name: Optional[str] = None
    
    def __post_init__(self):
        """Validate location coordinates."""
        if not -90 <= self.latitude <= 90:
            raise ValueError(f"Invalid latitude: {self.latitude}")
        if not -180 <= self.longitude <= 180:
            raise ValueError(f"Invalid longitude: {self.longitude}")


@dataclass
class CelestialBodyPosition:
    """Position of a celestial body at a specific time."""
    lon: float  # Longitude in degrees (0-360)
    lat: float  # Latitude in degrees
    speed: float  # Daily motion in degrees
    sign: Optional[str] = None  # Zodiac sign name
    sign_degree: Optional[float] = None  # Degree within sign (0-30)
    house: Optional[int] = None  # House number (1-12)
    
    def __post_init__(self):
        """Validate and normalize position data."""
        # Normalize longitude to 0-360 range
        self.lon = self.lon % 360.0
        
        # Calculate sign and degree if not provided
        if self.sign is None or self.sign_degree is None:
            self._calculate_sign_position()
    
    def _calculate_sign_position(self):
        """Calculate zodiac sign and degree within sign."""
        signs = [
            "Aries", "Taurus", "Gemini", "Cancer", "Leo", "Virgo",
            "Libra", "Scorpio", "Sagittarius", "Capricorn", "Aquarius", "Pisces"
        ]
        
        sign_index = int(self.lon // 30)
        self.sign = signs[sign_index]
        self.sign_degree = self.lon % 30


@dataclass
class EphemerisData:
    """Complete ephemeris data for a specific time and location."""
    calculation_time: Optional[datetime] = None
    location: Optional[Location] = None
    house_system: HouseSystem = HouseSystem.PLACIDUS
    coordinate_system: CoordinateSystem = CoordinateSystem.TROPICAL
    celestial_bodies: Optional[Dict[str, CelestialBodyPosition]] = None
    
    def __post_init__(self):
        """Initialize celestial bodies dictionary if not provided."""
        if self.celestial_bodies is None:
            self.celestial_bodies = {}
    
    def add_celestial_body(self, name: str, position: CelestialBodyPosition):
        """Add a celestial body position to the ephemeris."""
        self.celestial_bodies[name] = position
    
    def get_celestial_body(self, name: str) -> Optional[CelestialBodyPosition]:
        """Get position of a specific celestial body."""
        return self.celestial_bodies.get(name)
    
    def get_planet_names(self) -> List[str]:
        """Get list of all celestial body names in this ephemeris."""
        return list(self.celestial_bodies.keys())
    
    def validate_completeness(self, required_bodies: List[str]) -> bool:
        """Check if all required celestial bodies are present."""
        return all(body in self.celestial_bodies for body in required_bodies)


@dataclass
class EphemerisStatus:
    """Status of ephemeris data availability."""
    status: str  # 'production', 'test_mode', 'error'
    path: Optional[str] = None
    message: Optional[str] = None
    data_files_found: Optional[List[str]] = None
    missing_files: Optional[List[str]] = None


@dataclass
class ChartCalculationRequest:
    """Request for chart calculation."""
    datetime: datetime
    location: Location
    house_system: HouseSystem = HouseSystem.PLACIDUS
    coordinate_system: CoordinateSystem = CoordinateSystem.TROPICAL
    celestial_bodies: Optional[List[str]] = None  # If None, use default set
    
    def __post_init__(self):
        """Set default celestial bodies if not specified."""
        if self.celestial_bodies is None:
            self.celestial_bodies = [
                "Sun", "Moon", "Mercury", "Venus", "Mars", "Jupiter", "Saturn",
                "Uranus", "Neptune", "Pluto", "North Node", "South Node",
                "Chiron", "Lilith", "Ceres"
            ]


# Standard celestial body sets for different calculation types
MAJOR_PLANETS = ["Sun", "Moon", "Mercury", "Venus", "Mars", "Jupiter", "Saturn"]
OUTER_PLANETS = ["Uranus", "Neptune", "Pluto"]
LUNAR_NODES = ["North Node", "South Node"]
ASTEROIDS = ["Chiron", "Ceres", "Pallas", "Juno", "Vesta"]
CALCULATED_POINTS = ["Lilith", "Part of Fortune", "Vertex", "Midheaven", "Ascendant"]

# Default set for VA circuit analysis
VA_DEFAULT_BODIES = MAJOR_PLANETS + OUTER_PLANETS + ["North Node", "Chiron", "Lilith"]

# All supported celestial bodies
ALL_CELESTIAL_BODIES = (
    MAJOR_PLANETS + OUTER_PLANETS + LUNAR_NODES + 
    ASTEROIDS + CALCULATED_POINTS
)


def create_test_ephemeris(planet_count: int = 10, base_offset: float = 0.0) -> EphemerisData:
    """
    Create test ephemeris data for development and testing.
    
    Args:
        planet_count: Number of planets to include (max 15)
        base_offset: Base offset in degrees for all positions
        
    Returns:
        EphemerisData with evenly distributed test positions
    """
    ephemeris = EphemerisData()
    ephemeris.calculation_time = datetime.now()
    ephemeris.location = Location(
        latitude=40.7128,  # New York City
        longitude=-74.0060,
        timezone="America/New_York",
        name="Test Location"
    )
    
    planet_names = [
        "Sun", "Moon", "Mercury", "Venus", "Mars", "Jupiter", "Saturn",
        "Uranus", "Neptune", "Pluto", "Chiron", "North Node", "South Node",
        "Lilith", "Ceres"
    ]
    
    # Create evenly distributed positions around the zodiac
    for i in range(min(planet_count, len(planet_names))):
        name = planet_names[i]
        # Distribute planets evenly with some variation
        longitude = (base_offset + (i * 360.0 / planet_count)) % 360.0
        
        # Add some realistic speed variations
        speed_map = {
            "Sun": 1.0, "Moon": 13.0, "Mercury": 1.5, "Venus": 1.2,
            "Mars": 0.5, "Jupiter": 0.08, "Saturn": 0.03, "Uranus": 0.01,
            "Neptune": 0.006, "Pluto": 0.004, "Chiron": 0.02,
            "North Node": -0.05, "South Node": -0.05, "Lilith": 0.11, "Ceres": 0.21
        }
        
        speed = speed_map.get(name, 1.0)
        
        position = CelestialBodyPosition(
            lon=longitude,
            lat=0.0,  # Simplified for testing
            speed=speed
        )
        
        ephemeris.add_celestial_body(name, position)
    
    return ephemeris


def create_circuit_test_ephemeris() -> EphemerisData:
    """
    Create ephemeris designed to produce VA circuits for testing.
    
    Returns:
        EphemerisData with positions that form astrological circuits
    """
    ephemeris = EphemerisData()
    ephemeris.calculation_time = datetime.now()
    ephemeris.location = Location(
        latitude=40.7128,
        longitude=-74.0060,
        timezone="America/New_York",
        name="Circuit Test Location"
    )
    
    # Create grand trine configuration (120-degree aspects)
    circuit_positions = {
        "Sun": CelestialBodyPosition(lon=0.0, lat=0.0, speed=1.0),      # 0° Aries
        "Moon": CelestialBodyPosition(lon=120.0, lat=0.0, speed=13.0),  # 0° Leo
        "Mercury": CelestialBodyPosition(lon=240.0, lat=0.0, speed=1.5), # 0° Sagittarius
        
        # Add supporting planets for more complex circuits
        "Venus": CelestialBodyPosition(lon=60.0, lat=0.0, speed=1.2),   # 0° Gemini
        "Mars": CelestialBodyPosition(lon=180.0, lat=0.0, speed=0.5),   # 0° Libra
        "Jupiter": CelestialBodyPosition(lon=300.0, lat=0.0, speed=0.08), # 0° Aquarius
    }
    
    for name, position in circuit_positions.items():
        ephemeris.add_celestial_body(name, position)
    
    return ephemeris
