"""
Ephemeris Calculator Service

Swiss Ephemeris wrapper service with test mode fallback for development.
Provides accurate planetary position calculations with graceful degradation.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import time

from .data_models import (
    EphemerisData, CelestialBodyPosition, Location, ChartCalculationRequest,
    HouseSystem, CoordinateSystem, create_test_ephemeris, VA_DEFAULT_BODIES
)
from .ephemeris_data_manager import EphemerisDataManager, EphemerisConfig

logger = logging.getLogger(__name__)


class SwissEphemerisClient:
    """
    Swiss Ephemeris client wrapper.
    
    Handles the actual Swiss Ephemeris library integration with proper
    error handling and fallback mechanisms.
    """
    
    def __init__(self, ephemeris_path: Optional[str] = None):
        """
        Initialize Swiss Ephemeris client.
        
        Args:
            ephemeris_path: Path to ephemeris data files
        """
        self.ephemeris_path = ephemeris_path
        self._swisseph = None
        self._initialized = False
        
        self._planet_mapping = {
            'Sun': 0, 'Moon': 1, 'Mercury': 2, 'Venus': 3, 'Mars': 4,
            'Jupiter': 5, 'Saturn': 6, 'Uranus': 7, 'Neptune': 8, 'Pluto': 9,
            'North Node': 11, 'South Node': -11,  # South Node is negative North Node
            'Chiron': 15, 'Lilith': 12, 'Ceres': 17
        }
    
    def initialize(self) -> bool:
        """
        Initialize Swiss Ephemeris library.
        
        Returns:
            True if initialization successful, False otherwise
        """
        if self._initialized:
            return True
        
        try:
            import swisseph as swe
            self._swisseph = swe
            
            # Set ephemeris path if provided
            if self.ephemeris_path:
                swe.set_ephe_path(self.ephemeris_path)
            
            # Test calculation to verify functionality
            test_jd = swe.julday(2000, 1, 1, 12.0)  # J2000.0
            test_calc = swe.calc_ut(test_jd, swe.SUN)
            
            if test_calc and len(test_calc) >= 2:
                self._initialized = True
                logger.info("Swiss Ephemeris initialized successfully")
                return True
            else:
                logger.error("Swiss Ephemeris test calculation failed")
                return False
                
        except ImportError:
            logger.error("Swiss Ephemeris library not available (swisseph not installed)")
            return False
        except Exception as e:
            logger.error(f"Swiss Ephemeris initialization failed: {e}")
            return False
    
    def calculate_position(self, julian_day: float, planet_id: int) -> Optional[Tuple[float, float, float]]:
        """
        Calculate planetary position for given Julian day.
        
        Args:
            julian_day: Julian day number
            planet_id: Swiss Ephemeris planet ID
            
        Returns:
            Tuple of (longitude, latitude, speed) or None if calculation fails
        """
        if not self._initialized or not self._swisseph:
            return None
        
        try:
            result = self._swisseph.calc_ut(julian_day, planet_id)
            if result and len(result) >= 2:
                # result[0] contains [longitude, latitude, distance, speed_lon, speed_lat, speed_dist]
                position_data = result[0]
                return (position_data[0], position_data[1], position_data[3])  # lon, lat, speed
            return None
            
        except Exception as e:
            logger.warning(f"Swiss Ephemeris calculation failed for planet {planet_id}: {e}")
            return None
    
    def datetime_to_julian_day(self, dt: datetime) -> float:
        """
        Convert datetime to Julian day number.
        
        Args:
            dt: Datetime to convert
            
        Returns:
            Julian day number
        """
        if not self._swisseph:
            # Fallback calculation (approximate)
            return 2440587.5 + (dt.timestamp() / 86400.0)
        
        return self._swisseph.julday(dt.year, dt.month, dt.day, 
                                   dt.hour + dt.minute/60.0 + dt.second/3600.0)


class TestModeEphemeris:
    """
    Test mode ephemeris for development environments.
    
    Provides realistic but simulated planetary positions when Swiss Ephemeris
    data is not available.
    """
    
    def __init__(self):
        """Initialize test mode ephemeris."""
        # Base positions for J2000.0 (approximate)
        self._base_positions = {
            'Sun': 280.0, 'Moon': 120.0, 'Mercury': 290.0, 'Venus': 310.0,
            'Mars': 330.0, 'Jupiter': 45.0, 'Saturn': 75.0, 'Uranus': 315.0,
            'Neptune': 300.0, 'Pluto': 250.0, 'North Node': 125.0,
            'South Node': 305.0, 'Chiron': 95.0, 'Lilith': 200.0, 'Ceres': 160.0
        }
        
        # Approximate daily motions (degrees per day)
        self._daily_motions = {
            'Sun': 1.0, 'Moon': 13.0, 'Mercury': 1.5, 'Venus': 1.2,
            'Mars': 0.5, 'Jupiter': 0.08, 'Saturn': 0.03, 'Uranus': 0.01,
            'Neptune': 0.006, 'Pluto': 0.004, 'North Node': -0.05,
            'South Node': -0.05, 'Chiron': 0.02, 'Lilith': 0.11, 'Ceres': 0.21
        }
    
    def calculate_position(self, dt: datetime, planet_name: str) -> CelestialBodyPosition:
        """
        Calculate test position for given planet and time.
        
        Args:
            dt: Datetime for calculation
            planet_name: Name of celestial body
            
        Returns:
            CelestialBodyPosition with simulated data
        """
        if planet_name not in self._base_positions:
            # Default position for unknown bodies
            base_pos = 0.0
            daily_motion = 1.0
        else:
            base_pos = self._base_positions[planet_name]
            daily_motion = self._daily_motions[planet_name]
        
        # Calculate days since J2000.0
        j2000 = datetime(2000, 1, 1, 12, 0, 0)
        days_since_j2000 = (dt - j2000).total_seconds() / 86400.0
        
        # Calculate current position
        longitude = (base_pos + (daily_motion * days_since_j2000)) % 360.0
        
        # Add some realistic variation
        import math
        variation = math.sin(days_since_j2000 * 0.01) * 2.0  # Small orbital variation
        longitude = (longitude + variation) % 360.0
        
        return CelestialBodyPosition(
            lon=longitude,
            lat=0.0,  # Simplified for test mode
            speed=daily_motion
        )


class EphemerisCalculator:
    """
    Main ephemeris calculation service.
    
    Provides planetary position calculations with automatic fallback
    from Swiss Ephemeris to test mode when needed.
    """
    
    def __init__(self, config: Optional[EphemerisConfig] = None):
        """
        Initialize ephemeris calculator.
        
        Args:
            config: Optional configuration, uses defaults if not provided
        """
        self.config = config or EphemerisConfig()
        self.data_manager = EphemerisDataManager(self.config)
        self.swiss_client = None
        self.test_mode = TestModeEphemeris()
        self._performance_stats = {'calculations': 0, 'total_time': 0.0}
        
        # Initialize Swiss Ephemeris if available
        self._initialize_swiss_ephemeris()
    
    def _initialize_swiss_ephemeris(self):
        """Initialize Swiss Ephemeris client if data is available."""
        status = self.data_manager.ensure_ephemeris_availability()
        
        if status.status == 'production' and status.path:
            self.swiss_client = SwissEphemerisClient(status.path)
            if not self.swiss_client.initialize():
                logger.warning("Swiss Ephemeris initialization failed, falling back to test mode")
                self.swiss_client = None
        else:
            logger.info(f"Using test mode: {status.message}")
    
    def calculate_chart(self, request: ChartCalculationRequest) -> EphemerisData:
        """
        Calculate complete chart for given request.
        
        Args:
            request: Chart calculation request
            
        Returns:
            EphemerisData with calculated positions
        """
        start_time = time.time()
        
        ephemeris = EphemerisData(
            calculation_time=request.datetime,
            location=request.location,
            house_system=request.house_system,
            coordinate_system=request.coordinate_system
        )
        
        # Calculate positions for all requested celestial bodies
        for body_name in request.celestial_bodies:
            position = self._calculate_single_position(request.datetime, body_name)
            if position:
                ephemeris.add_celestial_body(body_name, position)
        
        # Update performance statistics
        calculation_time = time.time() - start_time
        self._performance_stats['calculations'] += 1
        self._performance_stats['total_time'] += calculation_time
        
        logger.debug(f"Chart calculation completed in {calculation_time:.3f}s for {len(request.celestial_bodies)} bodies")
        
        return ephemeris
    
    def _calculate_single_position(self, dt: datetime, body_name: str) -> Optional[CelestialBodyPosition]:
        """
        Calculate position for a single celestial body.
        
        Args:
            dt: Datetime for calculation
            body_name: Name of celestial body
            
        Returns:
            CelestialBodyPosition or None if calculation fails
        """
        # Try Swiss Ephemeris first if available
        if self.swiss_client and self.swiss_client._initialized:
            planet_id = self.swiss_client._planet_mapping.get(body_name)
            if planet_id is not None:
                julian_day = self.swiss_client.datetime_to_julian_day(dt)
                result = self.swiss_client.calculate_position(julian_day, planet_id)
                
                if result:
                    lon, lat, speed = result
                    return CelestialBodyPosition(lon=lon, lat=lat, speed=speed)
        
        # Fallback to test mode
        return self.test_mode.calculate_position(dt, body_name)
    
    def is_test_mode(self) -> bool:
        """
        Check if calculator is running in test mode.
        
        Returns:
            True if using test mode, False if using Swiss Ephemeris
        """
        return self.swiss_client is None or not self.swiss_client._initialized
    
    def get_performance_stats(self) -> Dict[str, float]:
        """
        Get performance statistics.
        
        Returns:
            Dictionary with performance metrics
        """
        stats = self._performance_stats.copy()
        if stats['calculations'] > 0:
            stats['average_time'] = stats['total_time'] / stats['calculations']
        else:
            stats['average_time'] = 0.0
        
        return stats
    
    def validate_accuracy(self) -> Dict[str, bool]:
        """
        Validate calculation accuracy against known test cases.
        
        Returns:
            Dictionary with validation results
        """
        # Test calculation for J2000.0 (known reference point)
        test_date = datetime(2000, 1, 1, 12, 0, 0)
        test_location = Location(latitude=0.0, longitude=0.0, timezone="UTC")
        
        request = ChartCalculationRequest(
            datetime=test_date,
            location=test_location,
            celestial_bodies=['Sun', 'Moon', 'Mercury']
        )
        
        try:
            result = self.calculate_chart(request)
            
            # Basic validation: check that we got positions
            validation_results = {
                'calculation_successful': len(result.celestial_bodies) > 0,
                'sun_position_reasonable': False,
                'moon_position_reasonable': False,
                'performance_acceptable': True
            }
            
            # Check Sun position (should be around 280° for J2000.0)
            if 'Sun' in result.celestial_bodies:
                sun_pos = result.celestial_bodies['Sun'].lon
                validation_results['sun_position_reasonable'] = 270.0 <= sun_pos <= 290.0
            
            # Check Moon position (varies widely, just check it's valid)
            if 'Moon' in result.celestial_bodies:
                moon_pos = result.celestial_bodies['Moon'].lon
                validation_results['moon_position_reasonable'] = 0.0 <= moon_pos <= 360.0
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Accuracy validation failed: {e}")
            return {
                'calculation_successful': False,
                'sun_position_reasonable': False,
                'moon_position_reasonable': False,
                'performance_acceptable': False,
                'error': str(e)
            }
    
    def get_status_info(self) -> Dict[str, any]:
        """
        Get comprehensive status information.
        
        Returns:
            Dictionary with status information
        """
        ephemeris_status = self.data_manager.ensure_ephemeris_availability()
        performance_stats = self.get_performance_stats()
        
        return {
            'ephemeris_status': ephemeris_status.status,
            'ephemeris_path': ephemeris_status.path,
            'test_mode': self.is_test_mode(),
            'swiss_ephemeris_available': self.swiss_client is not None and self.swiss_client._initialized,
            'calculations_performed': performance_stats['calculations'],
            'average_calculation_time': performance_stats.get('average_time', 0.0),
            'data_files_found': ephemeris_status.data_files_found or [],
            'missing_files': ephemeris_status.missing_files or []
        }
