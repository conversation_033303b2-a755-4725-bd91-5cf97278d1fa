"""
Ephemeris Data Manager

Intelligent ephemeris data discovery, validation, and management system.
Handles Swiss Ephemeris data files with fallback to test mode for development.
"""

import os
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from .data_models import EphemerisStatus

logger = logging.getLogger(__name__)


@dataclass
class EphemerisConfig:
    """Configuration for ephemeris data management."""
    test_mode: bool = False
    data_path: Optional[str] = None
    fallback_enabled: bool = True
    validation_enabled: bool = True


class EphemerisDataManager:
    """
    Intelligent ephemeris data discovery and validation.
    
    Manages Swiss Ephemeris data files with automatic path detection,
    validation, and graceful fallback to test mode when needed.
    """
    
    def __init__(self, config: Optional[EphemerisConfig] = None):
        """
        Initialize ephemeris data manager.
        
        Args:
            config: Optional configuration, uses defaults if not provided
        """
        self.config = config or EphemerisConfig()
        
        # Standard ephemeris data paths (in order of preference)
        self.data_paths = [
            '/usr/share/swisseph',           # Standard Linux installation
            '/opt/swisseph',                 # Custom Linux installation
            '/usr/local/share/swisseph',     # Homebrew on macOS
            '/usr/local/lib/swisseph',       # Alternative macOS location
            './ephemeris_data',              # Local development directory
            './data/ephemeris',              # Alternative local directory
            os.path.expanduser('~/swisseph'), # User home directory
        ]
        
        # Add environment variable path if set
        env_path = os.environ.get('SWISSEPH_PATH')
        if env_path:
            self.data_paths.insert(0, env_path)
        
        # Add config path if specified
        if self.config.data_path:
            self.data_paths.insert(0, self.config.data_path)
        
        # Required ephemeris data files for basic functionality
        self.required_files = [
            'sepl_18.se1',    # Planetary ephemeris (main planets)
            'semo_18.se1',    # Moon ephemeris
            'seas_18.se1',    # Asteroid ephemeris
        ]
        
        # Optional files for extended functionality
        self.optional_files = [
            'seplm48.se1',    # Long-term planetary ephemeris
            'seorbel.se1',    # Orbital elements
            'fixstars.cat',   # Fixed stars catalog
        ]
        
        self._status_cache = None
        self._validated_path = None
    
    def ensure_ephemeris_availability(self) -> EphemerisStatus:
        """
        Ensure ephemeris data is available with fallback to test mode.
        
        Returns:
            EphemerisStatus indicating data availability and location
        """
        # Return cached status if available and validation not forced
        if self._status_cache and not self.config.validation_enabled:
            return self._status_cache
        
        # If test mode is explicitly enabled, skip validation
        if self.config.test_mode:
            logger.info("Test mode enabled, skipping ephemeris data validation")
            self._status_cache = EphemerisStatus(
                status='test_mode',
                message='Test mode explicitly enabled'
            )
            return self._status_cache
        
        # Try to find valid ephemeris installation
        for path in self.data_paths:
            if not path:  # Skip None/empty paths
                continue
                
            logger.debug(f"Checking ephemeris path: {path}")
            validation_result = self._validate_ephemeris_installation(path)
            
            if validation_result['valid']:
                logger.info(f"Valid ephemeris data found at: {path}")
                self._validated_path = path
                self._status_cache = EphemerisStatus(
                    status='production',
                    path=path,
                    message=f'Valid ephemeris data found',
                    data_files_found=validation_result['found_files'],
                    missing_files=validation_result['missing_files']
                )
                return self._status_cache
        
        # No valid installation found
        if self.config.fallback_enabled:
            logger.warning("No valid ephemeris data found, enabling test mode fallback")
            self._status_cache = EphemerisStatus(
                status='test_mode',
                message='No valid ephemeris data found, using test mode fallback',
                missing_files=self.required_files
            )
        else:
            logger.error("No valid ephemeris data found and fallback disabled")
            self._status_cache = EphemerisStatus(
                status='error',
                message='No valid ephemeris data found and fallback disabled',
                missing_files=self.required_files
            )
        
        return self._status_cache
    
    def _validate_ephemeris_installation(self, path: str) -> Dict[str, Any]:
        """
        Validate ephemeris installation at given path.
        
        Args:
            path: Path to check for ephemeris data
            
        Returns:
            Dictionary with validation results
        """
        if not os.path.exists(path):
            return {
                'valid': False,
                'reason': 'Path does not exist',
                'found_files': [],
                'missing_files': self.required_files
            }
        
        if not os.path.isdir(path):
            return {
                'valid': False,
                'reason': 'Path is not a directory',
                'found_files': [],
                'missing_files': self.required_files
            }
        
        # Check for required files
        found_files = []
        missing_files = []
        
        for filename in self.required_files:
            file_path = os.path.join(path, filename)
            if os.path.exists(file_path) and os.path.isfile(file_path):
                # Additional validation: check file size (ephemeris files should be substantial)
                file_size = os.path.getsize(file_path)
                if file_size > 1024:  # At least 1KB
                    found_files.append(filename)
                else:
                    missing_files.append(f"{filename} (too small: {file_size} bytes)")
            else:
                missing_files.append(filename)
        
        # Consider installation valid if we have the core planetary ephemeris
        core_files_present = any(f in found_files for f in ['sepl_18.se1', 'seplm48.se1'])
        
        return {
            'valid': core_files_present and len(missing_files) <= 1,  # Allow 1 missing optional file
            'reason': 'Sufficient ephemeris files found' if core_files_present else 'Missing core ephemeris files',
            'found_files': found_files,
            'missing_files': missing_files
        }
    
    def get_ephemeris_path(self) -> Optional[str]:
        """
        Get the validated ephemeris data path.
        
        Returns:
            Path to ephemeris data or None if using test mode
        """
        status = self.ensure_ephemeris_availability()
        return status.path if status.status == 'production' else None
    
    def is_test_mode(self) -> bool:
        """
        Check if ephemeris is running in test mode.
        
        Returns:
            True if using test mode, False if using real ephemeris data
        """
        status = self.ensure_ephemeris_availability()
        return status.status == 'test_mode'
    
    def get_installation_instructions(self) -> str:
        """
        Get installation instructions for Swiss Ephemeris data.
        
        Returns:
            Formatted installation instructions
        """
        instructions = """
Swiss Ephemeris Data Installation Instructions:

1. Download ephemeris data files from: https://www.astro.com/swisseph/

2. Required files for basic functionality:
   - sepl_18.se1 (Planetary ephemeris)
   - semo_18.se1 (Moon ephemeris)
   - seas_18.se1 (Asteroid ephemeris)

3. Install to one of these locations:
   - Linux: /usr/share/swisseph/ or /opt/swisseph/
   - macOS: /usr/local/share/swisseph/ (Homebrew) or /usr/local/lib/swisseph/
   - Development: ./ephemeris_data/ or ./data/ephemeris/
   - Custom: Set SWISSEPH_PATH environment variable

4. Verify installation:
   - Files should be readable and > 1KB in size
   - Directory should be accessible to the application

5. Alternative: Use test mode for development (set test_mode=True in config)

Current search paths:
"""
        for i, path in enumerate(self.data_paths, 1):
            instructions += f"   {i}. {path}\n"
        
        return instructions
    
    def create_local_ephemeris_directory(self) -> bool:
        """
        Create local ephemeris directory for development.
        
        Returns:
            True if directory created successfully, False otherwise
        """
        local_path = './ephemeris_data'
        try:
            os.makedirs(local_path, exist_ok=True)
            
            # Create a README file with instructions
            readme_path = os.path.join(local_path, 'README.md')
            with open(readme_path, 'w') as f:
                f.write(self.get_installation_instructions())
            
            logger.info(f"Created local ephemeris directory: {local_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create local ephemeris directory: {e}")
            return False
    
    def validate_file_integrity(self, path: str) -> Dict[str, bool]:
        """
        Validate integrity of ephemeris files at given path.
        
        Args:
            path: Path to ephemeris data directory
            
        Returns:
            Dictionary mapping filenames to validation status
        """
        if not os.path.exists(path):
            return {}
        
        integrity_results = {}
        
        for filename in self.required_files + self.optional_files:
            file_path = os.path.join(path, filename)
            
            if os.path.exists(file_path):
                try:
                    # Basic integrity checks
                    file_size = os.path.getsize(file_path)
                    is_readable = os.access(file_path, os.R_OK)
                    
                    # Ephemeris files should be substantial in size
                    size_ok = file_size > 1024  # At least 1KB
                    
                    integrity_results[filename] = is_readable and size_ok
                    
                except Exception as e:
                    logger.warning(f"Error validating {filename}: {e}")
                    integrity_results[filename] = False
            else:
                integrity_results[filename] = False
        
        return integrity_results
