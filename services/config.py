"""
Global Configuration Management

Loads environment variables from .env files and provides centralized configuration.
"""

import os
import logging
from pathlib import Path
from typing import Optional

try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False

logger = logging.getLogger(__name__)

# Global flag to track if environment has been loaded
_env_loaded = False


def load_environment(env_file: Optional[str] = None) -> bool:
    """
    Load environment variables from .env file.
    
    Args:
        env_file: Path to .env file (defaults to .env in project root)
        
    Returns:
        True if environment was loaded successfully
    """
    global _env_loaded
    
    if _env_loaded:
        return True
    
    if not DOTENV_AVAILABLE:
        logger.warning("python-dotenv not available, skipping .env file loading")
        return False
    
    # Find project root (directory containing this file's parent)
    project_root = Path(__file__).parent.parent
    
    if env_file is None:
        env_file = project_root / ".env"
    else:
        env_file = Path(env_file)
    
    if env_file.exists():
        try:
            load_dotenv(env_file)
            logger.info(f"Loaded environment variables from {env_file}")
            _env_loaded = True
            return True
        except Exception as e:
            logger.error(f"Failed to load environment file {env_file}: {e}")
            return False
    else:
        logger.warning(f"Environment file {env_file} not found")
        return False


def get_env_var(key: str, default: Optional[str] = None, required: bool = False) -> Optional[str]:
    """
    Get environment variable with optional default and validation.
    
    Args:
        key: Environment variable name
        default: Default value if not found
        required: Whether the variable is required
        
    Returns:
        Environment variable value or default
        
    Raises:
        ValueError: If required variable is missing
    """
    # Ensure environment is loaded
    load_environment()
    
    value = os.getenv(key, default)
    
    if required and value is None:
        raise ValueError(f"Required environment variable {key} is not set")
    
    return value


def get_bool_env_var(key: str, default: bool = False) -> bool:
    """
    Get boolean environment variable.
    
    Args:
        key: Environment variable name
        default: Default value if not found
        
    Returns:
        Boolean value
    """
    value = get_env_var(key)
    if value is None:
        return default
    
    return value.lower() in ('true', '1', 'yes', 'on')


def get_int_env_var(key: str, default: int = 0) -> int:
    """
    Get integer environment variable.
    
    Args:
        key: Environment variable name
        default: Default value if not found
        
    Returns:
        Integer value
    """
    value = get_env_var(key)
    if value is None:
        return default
    
    try:
        return int(value)
    except ValueError:
        logger.warning(f"Invalid integer value for {key}: {value}, using default {default}")
        return default


def get_float_env_var(key: str, default: float = 0.0) -> float:
    """
    Get float environment variable.
    
    Args:
        key: Environment variable name
        default: Default value if not found
        
    Returns:
        Float value
    """
    value = get_env_var(key)
    if value is None:
        return default
    
    try:
        return float(value)
    except ValueError:
        logger.warning(f"Invalid float value for {key}: {value}, using default {default}")
        return default


# Auto-load environment on import
load_environment()
