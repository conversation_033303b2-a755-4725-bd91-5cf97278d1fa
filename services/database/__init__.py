"""
Database Service Package - Data Persistence and Caching

This package handles data persistence and caching with PostgreSQL and Redis:
- models.py: Pydantic data models for all entities
- connection_manager.py: Database and Redis connection management
- migrations.py: Database schema migrations
- article_db_service.py: News article data management
- celebrity_db_service.py: Celebrity data management with fuzzy matching
- cache_manager.py: Advanced Redis cache coordination
"""

from .models import (
    ArticleData, CelebrityData, ChartData, AnalysisResult, ProcessingJob,
    ProcessingStatus, ConfidenceLevel, DataSource, PaginatedResponse,
    DatabaseConfig, RedisConfig, HealthCheck,
    GenericEntity, EntityRelationship, EntityAction, EntityEndeavor
)

from .connection_manager import (
    ConnectionManager, DatabaseConnectionManager, RedisConnectionManager,
    get_connection_manager, initialize_connections, close_connections
)

from .migrations import (
    MigrationManager, Migration, run_all_migrations, get_all_migrations
)

from .article_db_service import ArticleDBService
from .celebrity_db_service import CelebrityDBService
from .generic_entity_db_service import GenericEntityDBService
from .entity_graph_db_service import EntityGraphDBService
from .cache_manager import CacheManager, get_cache_manager

__version__ = "1.0.0"
__all__ = [
    # Models
    "ArticleData", "CelebrityData", "ChartData", "AnalysisResult", "ProcessingJob",
    "ProcessingStatus", "ConfidenceLevel", "DataSource", "PaginatedResponse",
    "DatabaseConfig", "RedisConfig", "HealthCheck",
    "GenericEntity", "EntityRelationship", "EntityAction", "EntityEndeavor",

    # Connection Management
    "ConnectionManager", "DatabaseConnectionManager", "RedisConnectionManager",
    "get_connection_manager", "initialize_connections", "close_connections",

    # Migrations
    "MigrationManager", "Migration", "run_all_migrations", "get_all_migrations",

    # Services
    "ArticleDBService", "CelebrityDBService", "GenericEntityDBService",
    "EntityGraphDBService", "CacheManager", "get_cache_manager"
]
