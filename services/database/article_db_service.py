"""
Article Database Service

Handles news article data persistence with caching and search capabilities.
Provides CRUD operations and advanced querying for article management.
"""

import logging
import json
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from uuid import UUID

from sqlalchemy import text, and_, or_, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession

from .models import ArticleData, ProcessingStatus, PaginatedResponse, create_pagination_info
from .connection_manager import get_connection_manager

logger = logging.getLogger(__name__)


class ArticleDBService:
    """Service for managing news article data in the database."""
    
    def __init__(self):
        """Initialize article database service."""
        self.cache_ttl = 3600  # 1 hour cache TTL
        self.cache_prefix = "article:"
    
    async def create_article(self, article: ArticleData) -> ArticleData:
        """
        Create a new article in the database.
        
        Args:
            article: Article data to create
            
        Returns:
            Created article with generated ID
        """
        connection_manager = await get_connection_manager()
        
        # Ensure timestamps are set
        now = datetime.utcnow()
        article.created_at = now
        article.updated_at = now
        
        insert_sql = """
        INSERT INTO articles (
            id, title, content, url, published_at, source, entities,
            processing_status, created_at, updated_at, celebrity_mentions,
            extraction_confidence, language, word_count
        ) VALUES (
            :id, :title, :content, :url, :published_at, :source, :entities,
            :processing_status, :created_at, :updated_at, :celebrity_mentions,
            :extraction_confidence, :language, :word_count
        )
        """
        
        async with connection_manager.database.get_session() as session:
            await session.execute(text(insert_sql), {
                "id": article.id,
                "title": article.title,
                "content": article.content,
                "url": article.url,
                "published_at": article.published_at,
                "source": article.source,
                "entities": json.dumps(article.entities),
                "processing_status": article.processing_status.value,
                "created_at": article.created_at,
                "updated_at": article.updated_at,
                "celebrity_mentions": json.dumps(article.celebrity_mentions),
                "extraction_confidence": article.extraction_confidence,
                "language": article.language,
                "word_count": article.word_count
            })
            await session.commit()
        
        # Cache the article
        await self._cache_article(article)
        
        logger.info(f"Created article {article.id}: {article.title[:50]}...")
        return article
    
    async def get_article_by_id(self, article_id: str) -> Optional[ArticleData]:
        """
        Get article by ID with caching.
        
        Args:
            article_id: Article ID
            
        Returns:
            Article data if found, None otherwise
        """
        # Try cache first
        cached_article = await self._get_cached_article(article_id)
        if cached_article:
            return cached_article
        
        connection_manager = await get_connection_manager()
        
        query = """
        SELECT * FROM articles WHERE id = :article_id
        """
        
        async with connection_manager.database.get_session() as session:
            result = await session.execute(text(query), {"article_id": article_id})
            row = result.fetchone()
            
            if not row:
                return None
            
            article = self._row_to_article(row)
            
            # Cache the result
            await self._cache_article(article)
            
            return article
    
    async def get_articles_by_status(self, status: ProcessingStatus, limit: int = 100) -> List[ArticleData]:
        """
        Get articles by processing status.
        
        Args:
            status: Processing status to filter by
            limit: Maximum number of articles to return
            
        Returns:
            List of articles with the specified status
        """
        connection_manager = await get_connection_manager()
        
        query = """
        SELECT * FROM articles 
        WHERE processing_status = :status 
        ORDER BY created_at DESC 
        LIMIT :limit
        """
        
        async with connection_manager.database.get_session() as session:
            result = await session.execute(text(query), {
                "status": status.value,
                "limit": limit
            })
            
            return [self._row_to_article(row) for row in result.fetchall()]
    
    async def search_articles(
        self,
        query: Optional[str] = None,
        source: Optional[str] = None,
        status: Optional[ProcessingStatus] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None,
        page: int = 1,
        per_page: int = 20
    ) -> PaginatedResponse:
        """
        Search articles with filters and pagination.
        
        Args:
            query: Text search query
            source: Filter by source
            status: Filter by processing status
            date_from: Filter articles from this date
            date_to: Filter articles to this date
            page: Page number (1-based)
            per_page: Items per page
            
        Returns:
            Paginated response with articles
        """
        connection_manager = await get_connection_manager()
        
        # Build WHERE conditions
        conditions = []
        params = {}
        
        if query:
            conditions.append("(title ILIKE :query OR content ILIKE :query)")
            params["query"] = f"%{query}%"
        
        if source:
            conditions.append("source = :source")
            params["source"] = source
        
        if status:
            conditions.append("processing_status = :status")
            params["status"] = status.value
        
        if date_from:
            conditions.append("published_at >= :date_from")
            params["date_from"] = date_from
        
        if date_to:
            conditions.append("published_at <= :date_to")
            params["date_to"] = date_to
        
        where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
        
        # Count total results
        count_sql = f"SELECT COUNT(*) FROM articles {where_clause}"
        
        # Get paginated results
        offset = (page - 1) * per_page
        params.update({"limit": per_page, "offset": offset})
        
        results_sql = f"""
        SELECT * FROM articles {where_clause}
        ORDER BY published_at DESC
        LIMIT :limit OFFSET :offset
        """
        
        async with connection_manager.database.get_session() as session:
            # Get total count
            count_result = await session.execute(text(count_sql), params)
            total = count_result.scalar()
            
            # Get results
            results = await session.execute(text(results_sql), params)
            articles = [self._row_to_article(row) for row in results.fetchall()]
        
        # Create pagination info
        pagination = create_pagination_info(total, page, per_page)
        
        return PaginatedResponse(
            items=articles,
            **pagination
        )
    
    async def update_processing_status(self, article_id: str, status: ProcessingStatus) -> bool:
        """
        Update article processing status.

        Args:
            article_id: Article ID
            status: New processing status

        Returns:
            True if updated successfully
        """
        connection_manager = await get_connection_manager()

        update_sql = """
        UPDATE articles
        SET processing_status = :status, updated_at = :updated_at
        WHERE id = :article_id
        """

        async with connection_manager.database.get_session() as session:
            result = await session.execute(text(update_sql), {
                "status": status.value,
                "updated_at": datetime.utcnow(),
                "article_id": article_id
            })
            await session.commit()
            return result.rowcount > 0

    async def update_article(self, article: ArticleData) -> Optional[ArticleData]:
        """
        Update an existing article.

        Args:
            article: Article data to update

        Returns:
            Updated article data
        """
        connection_manager = await get_connection_manager()

        article.updated_at = datetime.utcnow()

        update_sql = """
        UPDATE articles SET
            title = :title,
            content = :content,
            url = :url,
            published_at = :published_at,
            source = :source,
            entities = :entities,
            processing_status = :processing_status,
            updated_at = :updated_at,
            celebrity_mentions = :celebrity_mentions,
            extraction_confidence = :extraction_confidence,
            language = :language,
            word_count = :word_count
        WHERE id = :id
        """

        async with connection_manager.database.get_session() as session:
            result = await session.execute(text(update_sql), {
                "id": article.id,
                "title": article.title,
                "content": article.content,
                "url": article.url,
                "published_at": article.published_at,
                "source": article.source,
                "entities": json.dumps(article.entities),
                "processing_status": article.processing_status.value,
                "updated_at": article.updated_at,
                "celebrity_mentions": json.dumps(article.celebrity_mentions),
                "extraction_confidence": article.extraction_confidence,
                "language": article.language,
                "word_count": article.word_count
            })
            await session.commit()

            if result.rowcount > 0:
                # Invalidate cache
                await self._invalidate_cache(article.id)
                return article
            return None

    async def delete_article(self, article_id: str) -> bool:
        """
        Delete an article.

        Args:
            article_id: Article ID to delete

        Returns:
            True if deleted successfully
        """
        connection_manager = await get_connection_manager()

        delete_sql = "DELETE FROM articles WHERE id = :article_id"

        async with connection_manager.database.get_session() as session:
            result = await session.execute(text(delete_sql), {"article_id": article_id})
            await session.commit()

            if result.rowcount > 0:
                # Invalidate cache
                await self._invalidate_cache(article_id)
                return True
            return False
    
    def _row_to_article(self, row) -> ArticleData:
        """Convert database row to ArticleData model."""
        # Handle JSON fields - they might come as lists/dicts directly from PostgreSQL
        def safe_json_parse(value):
            if value is None:
                return []
            if isinstance(value, (list, dict)):
                return value
            if isinstance(value, str):
                return json.loads(value)
            return []
            
        return ArticleData(
            id=str(row.id),
            title=row.title,
            content=row.content,
            url=row.url,
            published_at=row.published_at,
            source=row.source,
            entities=safe_json_parse(row.entities),
            processing_status=ProcessingStatus(row.processing_status),
            created_at=row.created_at,
            updated_at=row.updated_at,
            celebrity_mentions=safe_json_parse(row.celebrity_mentions),
            extraction_confidence=float(row.extraction_confidence) if row.extraction_confidence else None,
            language=row.language,
            word_count=row.word_count
        )
    
    async def _cache_article(self, article: ArticleData):
        """Cache article data in Redis."""
        try:
            connection_manager = await get_connection_manager()
            cache_key = f"{self.cache_prefix}{article.id}"
            cache_value = article.model_dump_json()
            await connection_manager.cache.set(cache_key, cache_value, self.cache_ttl)
        except Exception as e:
            logger.warning(f"Failed to cache article {article.id}: {e}")

    async def _invalidate_cache(self, article_id: str):
        """Invalidate cached article data."""
        try:
            connection_manager = await get_connection_manager()
            cache_key = f"{self.cache_prefix}{article_id}"
            await connection_manager.cache.delete(cache_key)
        except Exception as e:
            logger.warning(f"Failed to invalidate cache for article {article_id}: {e}")
    
    async def _get_cached_article(self, article_id: str) -> Optional[ArticleData]:
        """Get article from cache."""
        try:
            connection_manager = await get_connection_manager()
            cache_key = f"{self.cache_prefix}{article_id}"
            cached_data = await connection_manager.cache.get(cache_key)
            
            if cached_data:
                return ArticleData.model_validate_json(cached_data)
        except Exception as e:
            logger.warning(f"Failed to get cached article {article_id}: {e}")
        
        return None
    
    async def _clear_article_cache(self, article_id: str):
        """Clear article from cache."""
        try:
            connection_manager = await get_connection_manager()
            cache_key = f"{self.cache_prefix}{article_id}"
            await connection_manager.cache.delete(cache_key)
        except Exception as e:
            logger.warning(f"Failed to clear cache for article {article_id}: {e}")
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get article statistics."""
        connection_manager = await get_connection_manager()
        
        stats_sql = """
        SELECT 
            COUNT(*) as total_articles,
            COUNT(CASE WHEN processing_status = 'completed' THEN 1 END) as completed,
            COUNT(CASE WHEN processing_status = 'pending' THEN 1 END) as pending,
            COUNT(CASE WHEN processing_status = 'processing' THEN 1 END) as processing,
            COUNT(CASE WHEN processing_status = 'failed' THEN 1 END) as failed,
            AVG(word_count) as avg_word_count,
            COUNT(DISTINCT source) as unique_sources
        FROM articles
        """
        
        async with connection_manager.database.get_session() as session:
            result = await session.execute(text(stats_sql))
            row = result.fetchone()
            
            return {
                "total_articles": row.total_articles,
                "completed": row.completed,
                "pending": row.pending,
                "processing": row.processing,
                "failed": row.failed,
                "avg_word_count": float(row.avg_word_count) if row.avg_word_count else 0,
                "unique_sources": row.unique_sources
            }
