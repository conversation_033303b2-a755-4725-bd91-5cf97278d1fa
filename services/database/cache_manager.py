"""
Cache Manager

Centralized Redis cache management with intelligent caching strategies,
cache warming, and performance monitoring.
"""

import logging
import json
import asyncio
from typing import Any, Optional, Dict, List, Callable, Union
from datetime import datetime, timedelta
from functools import wraps
from dataclasses import dataclass

from .connection_manager import get_connection_manager

logger = logging.getLogger(__name__)


@dataclass
class CacheStats:
    """Cache performance statistics."""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    errors: int = 0
    
    @property
    def hit_rate(self) -> float:
        """Calculate cache hit rate."""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0


class CacheManager:
    """Advanced Redis cache manager with performance monitoring."""
    
    def __init__(self):
        """Initialize cache manager."""
        self.stats = CacheStats()
        self.default_ttl = 3600  # 1 hour
        self.key_prefix = "starpower:"
        
        # Cache warming configuration
        self.warm_cache_enabled = True
        self.warm_cache_interval = 300  # 5 minutes
        
        # Performance monitoring
        self.slow_operation_threshold = 100  # milliseconds
    
    async def get(self, key: str, default: Any = None) -> Any:
        """
        Get value from cache with automatic deserialization.
        
        Args:
            key: Cache key
            default: Default value if key not found
            
        Returns:
            Cached value or default
        """
        start_time = datetime.utcnow()
        
        try:
            connection_manager = await get_connection_manager()
            full_key = f"{self.key_prefix}{key}"
            
            cached_data = await connection_manager.cache.get(full_key)
            
            if cached_data is not None:
                self.stats.hits += 1
                
                # Try to deserialize JSON
                try:
                    return json.loads(cached_data)
                except (json.JSONDecodeError, TypeError):
                    # Return as string if not JSON
                    return cached_data
            else:
                self.stats.misses += 1
                return default
                
        except Exception as e:
            self.stats.errors += 1
            logger.error(f"Cache GET error for key {key}: {e}")
            return default
        
        finally:
            # Monitor performance
            duration = (datetime.utcnow() - start_time).total_seconds() * 1000
            if duration > self.slow_operation_threshold:
                logger.warning(f"Slow cache GET operation: {duration:.2f}ms for key {key}")
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        Set value in cache with automatic serialization.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            
        Returns:
            True if successful, False otherwise
        """
        start_time = datetime.utcnow()
        
        try:
            connection_manager = await get_connection_manager()
            full_key = f"{self.key_prefix}{key}"
            ttl = ttl or self.default_ttl
            
            # Serialize value
            if isinstance(value, (dict, list)):
                cache_value = json.dumps(value)
            elif hasattr(value, 'json'):  # Pydantic models
                cache_value = value.json()
            else:
                cache_value = str(value)
            
            success = await connection_manager.cache.set(full_key, cache_value, ttl)
            
            if success:
                self.stats.sets += 1
            else:
                self.stats.errors += 1
            
            return success
            
        except Exception as e:
            self.stats.errors += 1
            logger.error(f"Cache SET error for key {key}: {e}")
            return False
        
        finally:
            # Monitor performance
            duration = (datetime.utcnow() - start_time).total_seconds() * 1000
            if duration > self.slow_operation_threshold:
                logger.warning(f"Slow cache SET operation: {duration:.2f}ms for key {key}")
    
    async def delete(self, key: str) -> bool:
        """
        Delete key from cache.
        
        Args:
            key: Cache key to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            connection_manager = await get_connection_manager()
            full_key = f"{self.key_prefix}{key}"
            
            success = await connection_manager.cache.delete(full_key)
            
            if success:
                self.stats.deletes += 1
            
            return success
            
        except Exception as e:
            self.stats.errors += 1
            logger.error(f"Cache DELETE error for key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """
        Check if key exists in cache.
        
        Args:
            key: Cache key to check
            
        Returns:
            True if key exists, False otherwise
        """
        try:
            connection_manager = await get_connection_manager()
            full_key = f"{self.key_prefix}{key}"
            
            return await connection_manager.cache.exists(full_key)
            
        except Exception as e:
            self.stats.errors += 1
            logger.error(f"Cache EXISTS error for key {key}: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """
        Clear all keys matching a pattern.
        
        Args:
            pattern: Key pattern (supports wildcards)
            
        Returns:
            Number of keys deleted
        """
        try:
            connection_manager = await get_connection_manager()
            full_pattern = f"{self.key_prefix}{pattern}"
            
            # Get Redis client directly for pattern operations
            redis_client = connection_manager.cache.client
            
            # Find matching keys
            keys = await redis_client.keys(full_pattern)
            
            if keys:
                deleted = await redis_client.delete(*keys)
                self.stats.deletes += deleted
                logger.info(f"Cleared {deleted} keys matching pattern: {pattern}")
                return deleted
            
            return 0
            
        except Exception as e:
            self.stats.errors += 1
            logger.error(f"Cache CLEAR_PATTERN error for pattern {pattern}: {e}")
            return 0
    
    async def get_or_set(
        self, 
        key: str, 
        factory: Callable, 
        ttl: Optional[int] = None,
        *args, 
        **kwargs
    ) -> Any:
        """
        Get value from cache or set it using factory function.
        
        Args:
            key: Cache key
            factory: Function to generate value if not cached
            ttl: Time to live in seconds
            *args: Arguments for factory function
            **kwargs: Keyword arguments for factory function
            
        Returns:
            Cached or generated value
        """
        # Try to get from cache first
        cached_value = await self.get(key)
        
        if cached_value is not None:
            return cached_value
        
        # Generate value using factory
        try:
            if asyncio.iscoroutinefunction(factory):
                value = await factory(*args, **kwargs)
            else:
                value = factory(*args, **kwargs)
            
            # Cache the generated value
            await self.set(key, value, ttl)
            
            return value
            
        except Exception as e:
            logger.error(f"Factory function error for key {key}: {e}")
            raise
    
    def cache_result(self, key_template: str, ttl: Optional[int] = None):
        """
        Decorator to cache function results.
        
        Args:
            key_template: Template for cache key (can use function args)
            ttl: Time to live in seconds
            
        Returns:
            Decorated function
        """
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                # Generate cache key from template and arguments
                try:
                    cache_key = key_template.format(*args, **kwargs)
                except (IndexError, KeyError):
                    # Fallback to function name and hash of arguments
                    import hashlib
                    args_hash = hashlib.md5(str(args + tuple(kwargs.items())).encode()).hexdigest()[:8]
                    cache_key = f"{func.__name__}:{args_hash}"
                
                return await self.get_or_set(cache_key, func, ttl, *args, **kwargs)
            
            return wrapper
        return decorator
    
    async def warm_cache(self, warming_functions: List[Callable]):
        """
        Warm cache with frequently accessed data.
        
        Args:
            warming_functions: List of functions to execute for cache warming
        """
        if not self.warm_cache_enabled:
            return
        
        logger.info("Starting cache warming process")
        
        for func in warming_functions:
            try:
                if asyncio.iscoroutinefunction(func):
                    await func()
                else:
                    func()
                    
            except Exception as e:
                logger.error(f"Cache warming error for function {func.__name__}: {e}")
        
        logger.info("Cache warming completed")
    
    async def get_stats(self) -> Dict[str, Any]:
        """
        Get cache performance statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        try:
            connection_manager = await get_connection_manager()
            redis_info = await connection_manager.cache.client.info()
            
            return {
                "hit_rate": self.stats.hit_rate,
                "hits": self.stats.hits,
                "misses": self.stats.misses,
                "sets": self.stats.sets,
                "deletes": self.stats.deletes,
                "errors": self.stats.errors,
                "redis_memory_used": redis_info.get("used_memory_human", "unknown"),
                "redis_connected_clients": redis_info.get("connected_clients", 0),
                "redis_total_commands": redis_info.get("total_commands_processed", 0)
            }
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {
                "hit_rate": self.stats.hit_rate,
                "hits": self.stats.hits,
                "misses": self.stats.misses,
                "sets": self.stats.sets,
                "deletes": self.stats.deletes,
                "errors": self.stats.errors,
                "redis_memory_used": "unknown",
                "redis_connected_clients": 0,
                "redis_total_commands": 0
            }
    
    def reset_stats(self):
        """Reset cache statistics."""
        self.stats = CacheStats()
        logger.info("Cache statistics reset")


# Global cache manager instance
_cache_manager: Optional[CacheManager] = None


def get_cache_manager() -> CacheManager:
    """Get global cache manager instance."""
    global _cache_manager
    if not _cache_manager:
        _cache_manager = CacheManager()
    return _cache_manager


# Common cache warming functions

async def warm_celebrity_cache():
    """Warm cache with frequently accessed celebrities."""
    try:
        from .celebrity_db_service import CelebrityDBService
        
        celebrity_service = CelebrityDBService()
        
        # Cache top verified celebrities
        verified_celebrities = await celebrity_service.search_celebrities(
            verified_only=True,
            per_page=50
        )
        
        logger.info(f"Warmed cache with {len(verified_celebrities.items)} verified celebrities")
        
    except Exception as e:
        logger.error(f"Error warming celebrity cache: {e}")


async def warm_article_cache():
    """Warm cache with recent articles."""
    try:
        from .article_db_service import ArticleDBService
        from .models import ProcessingStatus
        
        article_service = ArticleDBService()
        
        # Cache recent completed articles
        recent_articles = await article_service.get_articles_by_status(
            ProcessingStatus.COMPLETED,
            limit=100
        )
        
        logger.info(f"Warmed cache with {len(recent_articles)} recent articles")
        
    except Exception as e:
        logger.error(f"Error warming article cache: {e}")


# Default cache warming functions
DEFAULT_WARMING_FUNCTIONS = [
    warm_celebrity_cache,
    warm_article_cache
]
