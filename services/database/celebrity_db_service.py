"""
Celebrity Database Service

Enhanced celebrity data persistence with advanced matching, validation, and performance optimization.
Provides CRUD operations, sophisticated search capabilities, and historical accuracy tracking
for celebrity management with API interface support.
"""

import logging
import json
import hashlib
from typing import List, Optional, Dict, Any, Tuple, Set
from datetime import datetime, timed<PERSON><PERSON>
from difflib import SequenceMatcher
from dataclasses import dataclass

from sqlalchemy import text, and_, or_, desc, asc, func
from sqlalchemy.ext.asyncio import AsyncSession

from .models import CelebrityData, DataSource, ConfidenceLevel, PaginatedResponse, create_pagination_info
from .connection_manager import get_connection_manager

logger = logging.getLogger(__name__)


@dataclass
class SearchMetrics:
    """Metrics for search performance and accuracy tracking."""
    query: str
    results_count: int
    search_time_ms: float
    accuracy_score: Optional[float] = None
    user_feedback: Optional[bool] = None
    timestamp: datetime = None

    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()


@dataclass
class CelebrityMatchResult:
    """Enhanced celebrity match result with metadata."""
    celebrity: CelebrityData
    similarity_score: float
    match_type: str  # 'exact', 'fuzzy', 'alias', 'professional'
    confidence_factors: List[str]
    search_metadata: Dict[str, Any]


class CelebrityDBService:
    """
    Enhanced service for managing celebrity data with advanced matching capabilities,
    performance optimization, and historical accuracy tracking.
    """

    def __init__(self):
        """Initialize enhanced celebrity database service."""
        self.cache_ttl = 7200  # 2 hour cache TTL for celebrities
        self.cache_prefix = "celebrity:"
        self.search_metrics_prefix = "search_metrics:"
        self.name_similarity_threshold = 0.8
        self.fuzzy_search_limit = 200  # Increased for better matching
        self.search_metrics_retention_days = 30

        # Performance optimization settings
        self.enable_search_caching = True
        self.search_cache_ttl = 1800  # 30 minutes for search results
        self.batch_size = 100

        # Accuracy tracking
        self.accuracy_tracking_enabled = True
        self.min_confidence_for_tracking = 0.7
    
    async def create_celebrity(self, celebrity: CelebrityData) -> CelebrityData:
        """
        Create a new celebrity in the database.
        
        Args:
            celebrity: Celebrity data to create
            
        Returns:
            Created celebrity with generated ID
        """
        connection_manager = await get_connection_manager()
        
        # Ensure timestamps are set
        now = datetime.utcnow()
        celebrity.created_at = now
        celebrity.updated_at = now
        
        insert_sql = """
        INSERT INTO celebrities (
            id, name, birth_date, birth_time, birth_location, birth_latitude,
            birth_longitude, birth_timezone, confidence_score, data_sources,
            enhanced, verified, created_at, updated_at, last_verified_at,
            aliases, professional_name
        ) VALUES (
            :id, :name, :birth_date, :birth_time, :birth_location, :birth_latitude,
            :birth_longitude, :birth_timezone, :confidence_score, :data_sources,
            :enhanced, :verified, :created_at, :updated_at, :last_verified_at,
            :aliases, :professional_name
        )
        """
        
        async with connection_manager.database.get_session() as session:
            await session.execute(text(insert_sql), {
                "id": celebrity.id,
                "name": celebrity.name,
                "birth_date": celebrity.birth_date,
                "birth_time": celebrity.birth_time,
                "birth_location": celebrity.birth_location,
                "birth_latitude": celebrity.birth_latitude,
                "birth_longitude": celebrity.birth_longitude,
                "birth_timezone": celebrity.birth_timezone,
                "confidence_score": celebrity.confidence_score,
                "data_sources": json.dumps([ds.value for ds in celebrity.data_sources]),
                "enhanced": celebrity.enhanced,
                "verified": celebrity.verified,
                "created_at": celebrity.created_at,
                "updated_at": celebrity.updated_at,
                "last_verified_at": celebrity.last_verified_at,
                "aliases": json.dumps(celebrity.aliases),
                "professional_name": celebrity.professional_name
            })
            await session.commit()
        
        # Cache the celebrity
        await self._cache_celebrity(celebrity)
        
        logger.info(f"Created celebrity {celebrity.id}: {celebrity.name}")
        return celebrity
    
    async def get_celebrity_by_id(self, celebrity_id: str) -> Optional[CelebrityData]:
        """
        Get celebrity by ID with caching.
        
        Args:
            celebrity_id: Celebrity ID
            
        Returns:
            Celebrity data if found, None otherwise
        """
        # Try cache first
        cached_celebrity = await self._get_cached_celebrity(celebrity_id)
        if cached_celebrity:
            return cached_celebrity
        
        connection_manager = await get_connection_manager()
        
        query = """
        SELECT * FROM celebrities WHERE id = :celebrity_id
        """
        
        async with connection_manager.database.get_session() as session:
            result = await session.execute(text(query), {"celebrity_id": celebrity_id})
            row = result.fetchone()
            
            if not row:
                return None
            
            celebrity = self._row_to_celebrity(row)
            
            # Cache the result
            await self._cache_celebrity(celebrity)
            
            return celebrity
    
    async def find_celebrity_by_name(self, name: str, fuzzy_match: bool = True) -> List[Tuple[CelebrityData, float]]:
        """
        Find celebrities by name with optional fuzzy matching.
        
        Args:
            name: Celebrity name to search for
            fuzzy_match: Enable fuzzy matching for similar names
            
        Returns:
            List of (celebrity, similarity_score) tuples, sorted by similarity
        """
        connection_manager = await get_connection_manager()
        
        # First try exact match
        exact_query = """
        SELECT * FROM celebrities 
        WHERE LOWER(name) = LOWER(:name) 
           OR LOWER(professional_name) = LOWER(:name)
           OR :name = ANY(SELECT LOWER(alias) FROM jsonb_array_elements_text(aliases) AS alias)
        ORDER BY confidence_score DESC
        """
        
        async with connection_manager.database.get_session() as session:
            result = await session.execute(text(exact_query), {"name": name})
            exact_matches = [self._row_to_celebrity(row) for row in result.fetchall()]
        
        if exact_matches:
            return [(celebrity, 1.0) for celebrity in exact_matches]
        
        if not fuzzy_match:
            return []
        
        # Fuzzy matching
        fuzzy_query = """
        SELECT * FROM celebrities 
        ORDER BY confidence_score DESC
        LIMIT 100
        """
        
        async with connection_manager.database.get_session() as session:
            result = await session.execute(text(fuzzy_query))
            all_celebrities = [self._row_to_celebrity(row) for row in result.fetchall()]
        
        # Calculate similarity scores
        matches = []
        name_lower = name.lower()
        
        for celebrity in all_celebrities:
            # Check main name
            main_similarity = SequenceMatcher(None, name_lower, celebrity.name.lower()).ratio()
            
            # Check professional name
            prof_similarity = 0.0
            if celebrity.professional_name:
                prof_similarity = SequenceMatcher(None, name_lower, celebrity.professional_name.lower()).ratio()
            
            # Check aliases
            alias_similarity = 0.0
            for alias in celebrity.aliases:
                alias_sim = SequenceMatcher(None, name_lower, alias.lower()).ratio()
                alias_similarity = max(alias_similarity, alias_sim)
            
            # Take the best similarity score
            best_similarity = max(main_similarity, prof_similarity, alias_similarity)
            
            if best_similarity >= self.name_similarity_threshold:
                matches.append((celebrity, best_similarity))
        
        # Sort by similarity score (descending)
        matches.sort(key=lambda x: x[1], reverse=True)
        
        return matches

    async def advanced_celebrity_search(
        self,
        name: str,
        fuzzy_match: bool = True,
        include_metadata: bool = True
    ) -> List[CelebrityMatchResult]:
        """
        Advanced celebrity search with enhanced matching and metadata.

        Args:
            name: Celebrity name to search for
            fuzzy_match: Enable fuzzy matching for similar names
            include_metadata: Include search metadata and confidence factors

        Returns:
            List of CelebrityMatchResult objects with enhanced matching info
        """
        start_time = datetime.utcnow()

        # Check search cache first
        if self.enable_search_caching:
            cached_results = await self._get_cached_search_results(name)
            if cached_results:
                return cached_results

        connection_manager = await get_connection_manager()
        results = []

        # Enhanced exact match with multiple strategies
        exact_matches = await self._perform_exact_search(name, connection_manager)
        for celebrity in exact_matches:
            match_result = CelebrityMatchResult(
                celebrity=celebrity,
                similarity_score=1.0,
                match_type='exact',
                confidence_factors=['exact_name_match'],
                search_metadata={'search_strategy': 'exact', 'query': name}
            )
            results.append(match_result)

        # If exact matches found and high confidence, return early
        if exact_matches and any(c.confidence_score > 0.9 for c in exact_matches):
            await self._cache_search_results(name, results)
            await self._track_search_metrics(name, len(results), start_time)
            return results

        # Fuzzy matching with enhanced algorithms
        if fuzzy_match:
            fuzzy_matches = await self._perform_fuzzy_search(name, connection_manager)
            results.extend(fuzzy_matches)

        # Sort by combined score (similarity * confidence)
        results.sort(key=lambda x: x.similarity_score * x.celebrity.confidence_score, reverse=True)

        # Cache results and track metrics
        if self.enable_search_caching:
            await self._cache_search_results(name, results)

        search_time = (datetime.utcnow() - start_time).total_seconds() * 1000
        await self._track_search_metrics(name, len(results), search_time)

        return results

    async def _perform_exact_search(self, name: str, connection_manager) -> List[CelebrityData]:
        """Perform exact search with multiple matching strategies."""
        exact_query = """
        SELECT * FROM celebrities
        WHERE LOWER(name) = LOWER(:name)
           OR LOWER(professional_name) = LOWER(:name)
           OR :name = ANY(SELECT LOWER(alias) FROM jsonb_array_elements_text(aliases) AS alias)
        ORDER BY confidence_score DESC, verified DESC
        """

        async with connection_manager.database.get_session() as session:
            result = await session.execute(text(exact_query), {"name": name})
            return [self._row_to_celebrity(row) for row in result.fetchall()]

    async def _perform_fuzzy_search(self, name: str, connection_manager) -> List[CelebrityMatchResult]:
        """Perform enhanced fuzzy search with multiple algorithms."""
        # Get candidates with optimized query
        fuzzy_query = """
        SELECT * FROM celebrities
        WHERE LOWER(name) ILIKE :partial_name
           OR LOWER(professional_name) ILIKE :partial_name
           OR EXISTS (
               SELECT 1 FROM jsonb_array_elements_text(aliases) AS alias
               WHERE LOWER(alias) ILIKE :partial_name
           )
        ORDER BY confidence_score DESC
        LIMIT :limit
        """

        partial_name = f"%{name.lower()}%"
        params = {"partial_name": partial_name, "limit": self.fuzzy_search_limit}

        async with connection_manager.database.get_session() as session:
            result = await session.execute(text(fuzzy_query), params)
            candidates = [self._row_to_celebrity(row) for row in result.fetchall()]

        # Enhanced similarity calculation
        matches = []
        name_lower = name.lower()

        for celebrity in candidates:
            match_result = self._calculate_enhanced_similarity(name_lower, celebrity)
            if match_result.similarity_score >= self.name_similarity_threshold:
                matches.append(match_result)

        return matches

    def _calculate_enhanced_similarity(self, query_name: str, celebrity: CelebrityData) -> CelebrityMatchResult:
        """Calculate enhanced similarity with multiple factors."""
        similarities = {}
        confidence_factors = []

        # Main name similarity
        main_sim = SequenceMatcher(None, query_name, celebrity.name.lower()).ratio()
        similarities['main_name'] = main_sim

        # Professional name similarity
        if celebrity.professional_name:
            prof_sim = SequenceMatcher(None, query_name, celebrity.professional_name.lower()).ratio()
            similarities['professional_name'] = prof_sim

        # Alias similarities
        alias_similarities = []
        for alias in celebrity.aliases:
            alias_sim = SequenceMatcher(None, query_name, alias.lower()).ratio()
            alias_similarities.append(alias_sim)
            if alias_sim > 0.9:
                confidence_factors.append(f'high_alias_match_{alias}')

        if alias_similarities:
            similarities['best_alias'] = max(alias_similarities)

        # Calculate weighted similarity
        weights = {
            'main_name': 0.4,
            'professional_name': 0.35,
            'best_alias': 0.25
        }

        weighted_similarity = 0.0
        total_weight = 0.0

        for sim_type, similarity in similarities.items():
            if sim_type in weights:
                weighted_similarity += similarity * weights[sim_type]
                total_weight += weights[sim_type]

        final_similarity = weighted_similarity / total_weight if total_weight > 0 else 0.0

        # Determine match type
        match_type = 'fuzzy'
        if similarities.get('main_name', 0) > 0.95:
            match_type = 'main_name'
        elif similarities.get('professional_name', 0) > 0.95:
            match_type = 'professional'
        elif similarities.get('best_alias', 0) > 0.95:
            match_type = 'alias'

        # Add confidence factors
        if celebrity.verified:
            confidence_factors.append('verified_celebrity')
        if celebrity.enhanced:
            confidence_factors.append('enhanced_data')
        if celebrity.confidence_score > 0.8:
            confidence_factors.append('high_confidence_score')

        return CelebrityMatchResult(
            celebrity=celebrity,
            similarity_score=final_similarity,
            match_type=match_type,
            confidence_factors=confidence_factors,
            search_metadata={
                'similarities': similarities,
                'weighted_score': final_similarity,
                'query': query_name
            }
        )

    async def search_celebrities(
        self,
        query: Optional[str] = None,
        verified_only: bool = False,
        enhanced_only: bool = False,
        min_confidence: Optional[float] = None,
        data_source: Optional[DataSource] = None,
        page: int = 1,
        per_page: int = 20
    ) -> PaginatedResponse:
        """
        Search celebrities with filters and pagination.
        
        Args:
            query: Text search query for name
            verified_only: Only return verified celebrities
            enhanced_only: Only return enhanced celebrities
            min_confidence: Minimum confidence score
            data_source: Filter by data source
            page: Page number (1-based)
            per_page: Items per page
            
        Returns:
            Paginated response with celebrities
        """
        connection_manager = await get_connection_manager()
        
        # Build WHERE conditions
        conditions = []
        params = {}
        
        if query:
            conditions.append("""
                (LOWER(name) ILIKE :query 
                 OR LOWER(professional_name) ILIKE :query
                 OR EXISTS (
                     SELECT 1 FROM jsonb_array_elements_text(aliases) AS alias 
                     WHERE LOWER(alias) ILIKE :query
                 ))
            """)
            params["query"] = f"%{query.lower()}%"
        
        if verified_only:
            conditions.append("verified = true")
        
        if enhanced_only:
            conditions.append("enhanced = true")
        
        if min_confidence is not None:
            conditions.append("confidence_score >= :min_confidence")
            params["min_confidence"] = min_confidence
        
        if data_source:
            conditions.append("data_sources ? :data_source")
            params["data_source"] = data_source.value
        
        where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""
        
        # Count total results
        count_sql = f"SELECT COUNT(*) FROM celebrities {where_clause}"
        
        # Get paginated results
        offset = (page - 1) * per_page
        params.update({"limit": per_page, "offset": offset})
        
        results_sql = f"""
        SELECT * FROM celebrities {where_clause}
        ORDER BY confidence_score DESC, name ASC
        LIMIT :limit OFFSET :offset
        """
        
        async with connection_manager.database.get_session() as session:
            # Get total count
            count_result = await session.execute(text(count_sql), params)
            total = count_result.scalar()
            
            # Get results
            results = await session.execute(text(results_sql), params)
            celebrities = [self._row_to_celebrity(row) for row in results.fetchall()]
        
        # Create pagination info
        pagination = create_pagination_info(total, page, per_page)
        
        return PaginatedResponse(
            items=celebrities,
            **pagination
        )
    
    async def update_celebrity(self, celebrity_id: str, updates: Dict[str, Any]) -> Optional[CelebrityData]:
        """
        Update celebrity with new data.
        
        Args:
            celebrity_id: Celebrity ID to update
            updates: Dictionary of fields to update
            
        Returns:
            Updated celebrity data if found, None otherwise
        """
        connection_manager = await get_connection_manager()
        
        # Add updated timestamp
        updates["updated_at"] = datetime.utcnow()
        
        # Handle special fields
        if "data_sources" in updates and isinstance(updates["data_sources"], list):
            updates["data_sources"] = json.dumps([ds.value if hasattr(ds, 'value') else ds for ds in updates["data_sources"]])
        
        if "aliases" in updates and isinstance(updates["aliases"], list):
            updates["aliases"] = json.dumps(updates["aliases"])
        
        # Build dynamic update query
        set_clauses = []
        params = {"celebrity_id": celebrity_id}
        
        for field, value in updates.items():
            set_clauses.append(f"{field} = :{field}")
            params[field] = value
        
        if not set_clauses:
            return await self.get_celebrity_by_id(celebrity_id)
        
        update_sql = f"""
        UPDATE celebrities 
        SET {', '.join(set_clauses)}
        WHERE id = :celebrity_id
        """
        
        async with connection_manager.database.get_session() as session:
            await session.execute(text(update_sql), params)
            await session.commit()
        
        # Clear cache and get updated celebrity
        await self._clear_celebrity_cache(celebrity_id)
        return await self.get_celebrity_by_id(celebrity_id)

    async def update_celebrity(self, celebrity: CelebrityData) -> Optional[CelebrityData]:
        """
        Update celebrity with CelebrityData object.

        Args:
            celebrity: Celebrity data to update

        Returns:
            Updated celebrity data
        """
        celebrity.updated_at = datetime.utcnow()

        connection_manager = await get_connection_manager()

        update_sql = """
        UPDATE celebrities SET
            name = :name,
            birth_date = :birth_date,
            birth_time = :birth_time,
            birth_location = :birth_location,
            birth_latitude = :birth_latitude,
            birth_longitude = :birth_longitude,
            birth_timezone = :birth_timezone,
            confidence_score = :confidence_score,
            data_sources = :data_sources,
            enhanced = :enhanced,
            verified = :verified,
            updated_at = :updated_at,
            aliases = :aliases,
            professional_name = :professional_name
        WHERE id = :id
        """

        async with connection_manager.database.get_session() as session:
            result = await session.execute(text(update_sql), {
                "id": celebrity.id,
                "name": celebrity.name,
                "birth_date": celebrity.birth_date,
                "birth_time": celebrity.birth_time,
                "birth_location": celebrity.birth_location,
                "birth_latitude": celebrity.birth_latitude,
                "birth_longitude": celebrity.birth_longitude,
                "birth_timezone": celebrity.birth_timezone,
                "confidence_score": celebrity.confidence_score,
                "data_sources": json.dumps([ds.value for ds in celebrity.data_sources]),
                "enhanced": celebrity.enhanced,
                "verified": celebrity.verified,
                "updated_at": celebrity.updated_at,
                "aliases": json.dumps(celebrity.aliases),
                "professional_name": celebrity.professional_name
            })
            await session.commit()

            if result.rowcount > 0:
                # Clear cache
                await self._clear_celebrity_cache(celebrity.id)
                return celebrity
            return None

    async def delete_celebrity(self, celebrity_id: str) -> bool:
        """
        Delete a celebrity.

        Args:
            celebrity_id: Celebrity ID to delete

        Returns:
            True if deleted successfully
        """
        connection_manager = await get_connection_manager()

        delete_sql = "DELETE FROM celebrities WHERE id = :celebrity_id"

        async with connection_manager.database.get_session() as session:
            result = await session.execute(text(delete_sql), {"celebrity_id": celebrity_id})
            await session.commit()

            if result.rowcount > 0:
                # Clear cache
                await self._clear_celebrity_cache(celebrity_id)
                return True
            return False
    
    def _row_to_celebrity(self, row) -> CelebrityData:
        """Convert database row to CelebrityData model."""
        data_sources = []
        if row.data_sources:
            source_values = json.loads(row.data_sources)
            data_sources = [DataSource(val) for val in source_values]
        
        return CelebrityData(
            id=str(row.id),
            name=row.name,
            birth_date=row.birth_date,
            birth_time=row.birth_time,
            birth_location=row.birth_location,
            birth_latitude=float(row.birth_latitude) if row.birth_latitude else None,
            birth_longitude=float(row.birth_longitude) if row.birth_longitude else None,
            birth_timezone=row.birth_timezone,
            confidence_score=float(row.confidence_score),
            data_sources=data_sources,
            enhanced=row.enhanced,
            verified=row.verified,
            created_at=row.created_at,
            updated_at=row.updated_at,
            last_verified_at=row.last_verified_at,
            aliases=json.loads(row.aliases) if row.aliases else [],
            professional_name=row.professional_name
        )
    
    async def _cache_celebrity(self, celebrity: CelebrityData):
        """Cache celebrity data in Redis."""
        try:
            connection_manager = await get_connection_manager()
            cache_key = f"{self.cache_prefix}{celebrity.id}"
            cache_value = celebrity.json()
            await connection_manager.cache.set(cache_key, cache_value, self.cache_ttl)
        except Exception as e:
            logger.warning(f"Failed to cache celebrity {celebrity.id}: {e}")
    
    async def _get_cached_celebrity(self, celebrity_id: str) -> Optional[CelebrityData]:
        """Get celebrity from cache."""
        try:
            connection_manager = await get_connection_manager()
            cache_key = f"{self.cache_prefix}{celebrity_id}"
            cached_data = await connection_manager.cache.get(cache_key)
            
            if cached_data:
                return CelebrityData.parse_raw(cached_data)
        except Exception as e:
            logger.warning(f"Failed to get cached celebrity {celebrity_id}: {e}")
        
        return None
    
    async def _clear_celebrity_cache(self, celebrity_id: str):
        """Clear celebrity from cache."""
        try:
            connection_manager = await get_connection_manager()
            cache_key = f"{self.cache_prefix}{celebrity_id}"
            await connection_manager.cache.delete(cache_key)
        except Exception as e:
            logger.warning(f"Failed to clear cache for celebrity {celebrity_id}: {e}")

    async def _get_cached_search_results(self, query: str) -> Optional[List[CelebrityMatchResult]]:
        """Get cached search results."""
        try:
            connection_manager = await get_connection_manager()
            cache_key = f"search:{hashlib.md5(query.lower().encode()).hexdigest()}"
            cached_data = await connection_manager.cache.get(cache_key)

            if cached_data:
                # Deserialize cached results
                data = json.loads(cached_data)
                results = []
                for item in data:
                    celebrity = CelebrityData.parse_obj(item['celebrity'])
                    match_result = CelebrityMatchResult(
                        celebrity=celebrity,
                        similarity_score=item['similarity_score'],
                        match_type=item['match_type'],
                        confidence_factors=item['confidence_factors'],
                        search_metadata=item['search_metadata']
                    )
                    results.append(match_result)
                return results
        except Exception as e:
            logger.warning(f"Failed to get cached search results for '{query}': {e}")

        return None

    async def _cache_search_results(self, query: str, results: List[CelebrityMatchResult]):
        """Cache search results."""
        try:
            connection_manager = await get_connection_manager()
            cache_key = f"search:{hashlib.md5(query.lower().encode()).hexdigest()}"

            # Serialize results
            serializable_results = []
            for result in results[:10]:  # Cache top 10 results only
                serializable_results.append({
                    'celebrity': result.celebrity.dict(),
                    'similarity_score': result.similarity_score,
                    'match_type': result.match_type,
                    'confidence_factors': result.confidence_factors,
                    'search_metadata': result.search_metadata
                })

            cache_value = json.dumps(serializable_results)
            await connection_manager.cache.set(cache_key, cache_value, self.search_cache_ttl)
        except Exception as e:
            logger.warning(f"Failed to cache search results for '{query}': {e}")

    async def _track_search_metrics(self, query: str, results_count: int, search_time_ms: float):
        """Track search metrics for performance monitoring."""
        if not self.accuracy_tracking_enabled:
            return

        try:
            connection_manager = await get_connection_manager()
            metrics = SearchMetrics(
                query=query,
                results_count=results_count,
                search_time_ms=search_time_ms
            )

            # Store in cache with TTL
            metrics_key = f"{self.search_metrics_prefix}{datetime.utcnow().isoformat()}"
            metrics_data = {
                'query': metrics.query,
                'results_count': metrics.results_count,
                'search_time_ms': metrics.search_time_ms,
                'timestamp': metrics.timestamp.isoformat()
            }

            ttl = self.search_metrics_retention_days * 24 * 3600
            await connection_manager.cache.set(metrics_key, json.dumps(metrics_data), ttl)

        except Exception as e:
            logger.warning(f"Failed to track search metrics: {e}")

    async def get_search_performance_stats(self, days: int = 7) -> Dict[str, Any]:
        """Get search performance statistics."""
        try:
            connection_manager = await get_connection_manager()

            # Get metrics from cache
            pattern = f"{self.search_metrics_prefix}*"
            keys = await connection_manager.cache.keys(pattern)

            metrics_data = []
            cutoff_date = datetime.utcnow() - timedelta(days=days)

            for key in keys:
                try:
                    data = await connection_manager.cache.get(key)
                    if data:
                        metrics = json.loads(data)
                        timestamp = datetime.fromisoformat(metrics['timestamp'])
                        if timestamp >= cutoff_date:
                            metrics_data.append(metrics)
                except Exception:
                    continue

            if not metrics_data:
                return {
                    'total_searches': 0,
                    'avg_search_time_ms': 0,
                    'avg_results_count': 0,
                    'period_days': days
                }

            # Calculate statistics
            total_searches = len(metrics_data)
            avg_search_time = sum(m['search_time_ms'] for m in metrics_data) / total_searches
            avg_results = sum(m['results_count'] for m in metrics_data) / total_searches

            # Top queries
            query_counts = {}
            for m in metrics_data:
                query = m['query'].lower()
                query_counts[query] = query_counts.get(query, 0) + 1

            top_queries = sorted(query_counts.items(), key=lambda x: x[1], reverse=True)[:10]

            return {
                'total_searches': total_searches,
                'avg_search_time_ms': round(avg_search_time, 2),
                'avg_results_count': round(avg_results, 2),
                'top_queries': top_queries,
                'period_days': days,
                'performance_trend': 'stable'  # Could be enhanced with trend analysis
            }

        except Exception as e:
            logger.error(f"Failed to get search performance stats: {e}")
            return {'error': str(e)}

    async def update_search_accuracy_feedback(self, query: str, selected_celebrity_id: str, was_correct: bool):
        """Update search accuracy based on user feedback."""
        if not self.accuracy_tracking_enabled:
            return

        try:
            connection_manager = await get_connection_manager()

            feedback_key = f"feedback:{hashlib.md5(f'{query}:{selected_celebrity_id}'.encode()).hexdigest()}"
            feedback_data = {
                'query': query,
                'celebrity_id': selected_celebrity_id,
                'was_correct': was_correct,
                'timestamp': datetime.utcnow().isoformat()
            }

            # Store feedback for accuracy analysis
            ttl = self.search_metrics_retention_days * 24 * 3600
            await connection_manager.cache.set(feedback_key, json.dumps(feedback_data), ttl)

            logger.info(f"Recorded search feedback: query='{query}', correct={was_correct}")

        except Exception as e:
            logger.warning(f"Failed to update search accuracy feedback: {e}")

    async def get_celebrity_lookup_performance(self) -> Dict[str, Any]:
        """Get celebrity lookup performance metrics."""
        try:
            connection_manager = await get_connection_manager()

            # Test lookup performance
            start_time = datetime.utcnow()

            # Sample query for performance testing
            test_query = """
            SELECT COUNT(*) as total_celebrities,
                   AVG(confidence_score) as avg_confidence,
                   COUNT(CASE WHEN verified = true THEN 1 END) as verified_count,
                   COUNT(CASE WHEN enhanced = true THEN 1 END) as enhanced_count
            FROM celebrities
            """

            async with connection_manager.database.get_session() as session:
                result = await session.execute(text(test_query))
                stats = result.fetchone()

            query_time = (datetime.utcnow() - start_time).total_seconds() * 1000

            return {
                'total_celebrities': stats.total_celebrities,
                'avg_confidence_score': round(float(stats.avg_confidence or 0), 3),
                'verified_count': stats.verified_count,
                'enhanced_count': stats.enhanced_count,
                'db_query_time_ms': round(query_time, 2),
                'cache_hit_rate': 'N/A',  # Could be enhanced with cache statistics
                'performance_status': 'optimal' if query_time < 100 else 'degraded'
            }

        except Exception as e:
            logger.error(f"Failed to get celebrity lookup performance: {e}")
            return {'error': str(e)}

    async def batch_create_celebrities(self, celebrities: List[CelebrityData]) -> List[CelebrityData]:
        """
        Create multiple celebrities in batch for performance optimization.

        Args:
            celebrities: List of celebrity data to create

        Returns:
            List of created celebrities with generated IDs
        """
        if not celebrities:
            return []

        connection_manager = await get_connection_manager()
        created_celebrities = []

        # Process in batches to avoid overwhelming the database
        for i in range(0, len(celebrities), self.batch_size):
            batch = celebrities[i:i + self.batch_size]

            async with connection_manager.database.get_session() as session:
                for celebrity in batch:
                    # Ensure timestamps are set
                    now = datetime.utcnow()
                    celebrity.created_at = now
                    celebrity.updated_at = now

                    # Generate ID if not provided
                    if not celebrity.id:
                        celebrity.id = f"celeb_{hashlib.md5(f'{celebrity.name}_{now.isoformat()}'.encode()).hexdigest()[:12]}"

                    # Insert celebrity
                    insert_sql = """
                    INSERT INTO celebrities (
                        id, name, birth_date, birth_time, birth_location,
                        birth_latitude, birth_longitude, birth_timezone,
                        confidence_score, data_sources, enhanced, verified,
                        created_at, updated_at, aliases, professional_name
                    ) VALUES (
                        :id, :name, :birth_date, :birth_time, :birth_location,
                        :birth_latitude, :birth_longitude, :birth_timezone,
                        :confidence_score, :data_sources, :enhanced, :verified,
                        :created_at, :updated_at, :aliases, :professional_name
                    )
                    """

                    params = {
                        "id": celebrity.id,
                        "name": celebrity.name,
                        "birth_date": celebrity.birth_date,
                        "birth_time": celebrity.birth_time,
                        "birth_location": celebrity.birth_location,
                        "birth_latitude": celebrity.birth_latitude,
                        "birth_longitude": celebrity.birth_longitude,
                        "birth_timezone": celebrity.birth_timezone,
                        "confidence_score": celebrity.confidence_score,
                        "data_sources": json.dumps([ds.value for ds in celebrity.data_sources]),
                        "enhanced": celebrity.enhanced,
                        "verified": celebrity.verified,
                        "created_at": celebrity.created_at,
                        "updated_at": celebrity.updated_at,
                        "aliases": json.dumps(celebrity.aliases),
                        "professional_name": celebrity.professional_name
                    }

                    await session.execute(text(insert_sql), params)
                    created_celebrities.append(celebrity)

                await session.commit()

        logger.info(f"Batch created {len(created_celebrities)} celebrities")
        return created_celebrities

    async def get_celebrities_by_confidence_range(
        self,
        min_confidence: float,
        max_confidence: float = 1.0,
        limit: int = 100
    ) -> List[CelebrityData]:
        """
        Get celebrities within a specific confidence score range.

        Args:
            min_confidence: Minimum confidence score
            max_confidence: Maximum confidence score
            limit: Maximum number of results

        Returns:
            List of celebrities within confidence range
        """
        connection_manager = await get_connection_manager()

        query = """
        SELECT * FROM celebrities
        WHERE confidence_score >= :min_confidence
          AND confidence_score <= :max_confidence
        ORDER BY confidence_score DESC, name ASC
        LIMIT :limit
        """

        params = {
            "min_confidence": min_confidence,
            "max_confidence": max_confidence,
            "limit": limit
        }

        async with connection_manager.database.get_session() as session:
            result = await session.execute(text(query), params)
            return [self._row_to_celebrity(row) for row in result.fetchall()]

    async def get_celebrities_by_data_source(self, data_source: DataSource, limit: int = 100) -> List[CelebrityData]:
        """
        Get celebrities from a specific data source.

        Args:
            data_source: Data source to filter by
            limit: Maximum number of results

        Returns:
            List of celebrities from the specified data source
        """
        connection_manager = await get_connection_manager()

        query = """
        SELECT * FROM celebrities
        WHERE data_sources ? :data_source
        ORDER BY confidence_score DESC, name ASC
        LIMIT :limit
        """

        params = {
            "data_source": data_source.value,
            "limit": limit
        }

        async with connection_manager.database.get_session() as session:
            result = await session.execute(text(query), params)
            return [self._row_to_celebrity(row) for row in result.fetchall()]

    async def get_database_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive database statistics for monitoring and optimization.

        Returns:
            Dictionary with database statistics
        """
        connection_manager = await get_connection_manager()

        stats_query = """
        SELECT
            COUNT(*) as total_celebrities,
            COUNT(CASE WHEN verified = true THEN 1 END) as verified_count,
            COUNT(CASE WHEN enhanced = true THEN 1 END) as enhanced_count,
            AVG(confidence_score) as avg_confidence,
            MIN(confidence_score) as min_confidence,
            MAX(confidence_score) as max_confidence,
            COUNT(CASE WHEN birth_date IS NOT NULL THEN 1 END) as with_birth_date,
            COUNT(CASE WHEN birth_location IS NOT NULL THEN 1 END) as with_birth_location,
            COUNT(CASE WHEN aliases IS NOT NULL AND aliases != '[]' THEN 1 END) as with_aliases
        FROM celebrities
        """

        data_sources_query = """
        SELECT
            jsonb_array_elements_text(data_sources) as source,
            COUNT(*) as count
        FROM celebrities
        WHERE data_sources IS NOT NULL
        GROUP BY jsonb_array_elements_text(data_sources)
        ORDER BY count DESC
        """

        async with connection_manager.database.get_session() as session:
            # Get main statistics
            stats_result = await session.execute(text(stats_query))
            stats = stats_result.fetchone()

            # Get data source distribution
            sources_result = await session.execute(text(data_sources_query))
            data_source_distribution = {row.source: row.count for row in sources_result.fetchall()}

        return {
            'total_celebrities': stats.total_celebrities,
            'verified_count': stats.verified_count,
            'enhanced_count': stats.enhanced_count,
            'avg_confidence_score': round(float(stats.avg_confidence or 0), 3),
            'min_confidence_score': float(stats.min_confidence or 0),
            'max_confidence_score': float(stats.max_confidence or 0),
            'with_birth_date': stats.with_birth_date,
            'with_birth_location': stats.with_birth_location,
            'with_aliases': stats.with_aliases,
            'data_source_distribution': data_source_distribution,
            'data_completeness_percentage': round(
                (stats.with_birth_date / max(stats.total_celebrities, 1)) * 100, 1
            )
        }
