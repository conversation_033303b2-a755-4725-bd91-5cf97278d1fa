"""
Database Configuration Management

Environment-aware configuration for PostgreSQL and Redis connections
with support for development, testing, and production environments.
"""

import os
import logging
from typing import Optional, Dict, Any
from pathlib import Path

from .models import DatabaseConfig, RedisConfig

logger = logging.getLogger(__name__)


class DatabaseConfigManager:
    """Manages database configuration across different environments."""
    
    def __init__(self, environment: Optional[str] = None):
        """
        Initialize configuration manager.
        
        Args:
            environment: Environment name (development, testing, production)
        """
        self.environment = environment or os.getenv("ENVIRONMENT", "development")
        self.config_cache = {}
    
    def get_database_config(self) -> DatabaseConfig:
        """
        Get PostgreSQL database configuration for current environment.
        
        Returns:
            Database configuration
        """
        if "database" in self.config_cache:
            return self.config_cache["database"]
        
        if self.environment == "testing":
            config = self._get_testing_database_config()
        elif self.environment == "production":
            config = self._get_production_database_config()
        else:
            config = self._get_development_database_config()
        
        self.config_cache["database"] = config
        logger.info(f"Database configuration loaded for environment: {self.environment}")
        
        return config
    
    def get_redis_config(self) -> RedisConfig:
        """
        Get Redis configuration for current environment.
        
        Returns:
            Redis configuration
        """
        if "redis" in self.config_cache:
            return self.config_cache["redis"]
        
        if self.environment == "testing":
            config = self._get_testing_redis_config()
        elif self.environment == "production":
            config = self._get_production_redis_config()
        else:
            config = self._get_development_redis_config()
        
        self.config_cache["redis"] = config
        logger.info(f"Redis configuration loaded for environment: {self.environment}")
        
        return config
    
    def _get_development_database_config(self) -> DatabaseConfig:
        """Get development database configuration."""
        # Default development PostgreSQL settings
        host = os.getenv("DB_HOST", "localhost")
        port = os.getenv("DB_PORT", "5432")
        database = os.getenv("DB_NAME", "starpower_dev")
        username = os.getenv("DB_USER", "starpower")
        password = os.getenv("DB_PASSWORD", "starpower_dev_pass")
        
        url = f"postgresql+asyncpg://{username}:{password}@{host}:{port}/{database}"
        
        return DatabaseConfig(
            url=url,
            pool_size=5,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=3600,
            echo=True,  # Enable SQL logging in development
            retry_attempts=3,
            retry_delay=1.0
        )
    
    def _get_testing_database_config(self) -> DatabaseConfig:
        """Get testing database configuration."""
        # Use SQLite for testing by default, or test PostgreSQL
        if os.getenv("USE_POSTGRES_TESTING", "false").lower() == "true":
            host = os.getenv("TEST_DB_HOST", "localhost")
            port = os.getenv("TEST_DB_PORT", "5432")
            database = os.getenv("TEST_DB_NAME", "starpower_test")
            username = os.getenv("TEST_DB_USER", "starpower")
            password = os.getenv("TEST_DB_PASSWORD", "starpower_test_pass")
            
            url = f"postgresql+asyncpg://{username}:{password}@{host}:{port}/{database}"
        else:
            # Use in-memory SQLite for fast testing
            test_db_path = os.getenv("TEST_DB_PATH", ":memory:")
            url = f"sqlite+aiosqlite:///{test_db_path}"
        
        return DatabaseConfig(
            url=url,
            pool_size=1,
            max_overflow=0,
            pool_timeout=10,
            pool_recycle=300,
            echo=False,  # Disable SQL logging in tests
            retry_attempts=1,
            retry_delay=0.1
        )
    
    def _get_production_database_config(self) -> DatabaseConfig:
        """Get production database configuration."""
        # Production requires all environment variables to be set
        required_vars = ["PROD_DB_HOST", "PROD_DB_NAME", "PROD_DB_USER", "PROD_DB_PASSWORD"]
        missing_vars = [var for var in required_vars if not os.getenv(var)]
        
        if missing_vars:
            raise ValueError(f"Missing required production database environment variables: {missing_vars}")
        
        host = os.getenv("PROD_DB_HOST")
        port = os.getenv("PROD_DB_PORT", "5432")
        database = os.getenv("PROD_DB_NAME")
        username = os.getenv("PROD_DB_USER")
        password = os.getenv("PROD_DB_PASSWORD")
        
        # Support SSL and other production options
        ssl_mode = os.getenv("PROD_DB_SSL_MODE", "require")
        url = f"postgresql+asyncpg://{username}:{password}@{host}:{port}/{database}?sslmode={ssl_mode}"
        
        return DatabaseConfig(
            url=url,
            pool_size=int(os.getenv("PROD_DB_POOL_SIZE", "20")),
            max_overflow=int(os.getenv("PROD_DB_MAX_OVERFLOW", "30")),
            pool_timeout=int(os.getenv("PROD_DB_POOL_TIMEOUT", "60")),
            pool_recycle=int(os.getenv("PROD_DB_POOL_RECYCLE", "7200")),
            echo=False,  # Disable SQL logging in production
            retry_attempts=int(os.getenv("PROD_DB_RETRY_ATTEMPTS", "5")),
            retry_delay=float(os.getenv("PROD_DB_RETRY_DELAY", "2.0"))
        )
    
    def _get_development_redis_config(self) -> RedisConfig:
        """Get development Redis configuration."""
        host = os.getenv("REDIS_HOST", "localhost")
        port = os.getenv("REDIS_PORT", "6379")
        db = os.getenv("REDIS_DB", "0")
        password = os.getenv("REDIS_PASSWORD", "")
        
        if password:
            url = f"redis://:{password}@{host}:{port}/{db}"
        else:
            url = f"redis://{host}:{port}/{db}"
        
        return RedisConfig(
            url=url,
            max_connections=10,
            socket_timeout=5.0,
            socket_connect_timeout=5.0,
            retry_on_timeout=True,
            default_ttl=3600,
            key_prefix="starpower:dev:"
        )
    
    def _get_testing_redis_config(self) -> RedisConfig:
        """Get testing Redis configuration."""
        # Use separate Redis DB for testing
        host = os.getenv("TEST_REDIS_HOST", "localhost")
        port = os.getenv("TEST_REDIS_PORT", "6379")
        db = os.getenv("TEST_REDIS_DB", "1")  # Different DB for testing
        
        url = f"redis://{host}:{port}/{db}"
        
        return RedisConfig(
            url=url,
            max_connections=5,
            socket_timeout=2.0,
            socket_connect_timeout=2.0,
            retry_on_timeout=False,
            default_ttl=300,  # Shorter TTL for testing
            key_prefix="starpower:test:"
        )
    
    def _get_production_redis_config(self) -> RedisConfig:
        """Get production Redis configuration."""
        # Production Redis configuration
        redis_url = os.getenv("PROD_REDIS_URL")
        
        if not redis_url:
            # Build URL from components
            host = os.getenv("PROD_REDIS_HOST")
            port = os.getenv("PROD_REDIS_PORT", "6379")
            db = os.getenv("PROD_REDIS_DB", "0")
            password = os.getenv("PROD_REDIS_PASSWORD")
            
            if not host:
                raise ValueError("Missing required production Redis configuration: PROD_REDIS_URL or PROD_REDIS_HOST")
            
            if password:
                redis_url = f"redis://:{password}@{host}:{port}/{db}"
            else:
                redis_url = f"redis://{host}:{port}/{db}"
        
        return RedisConfig(
            url=redis_url,
            max_connections=int(os.getenv("PROD_REDIS_MAX_CONNECTIONS", "50")),
            socket_timeout=float(os.getenv("PROD_REDIS_SOCKET_TIMEOUT", "10.0")),
            socket_connect_timeout=float(os.getenv("PROD_REDIS_CONNECT_TIMEOUT", "10.0")),
            retry_on_timeout=True,
            default_ttl=int(os.getenv("PROD_REDIS_DEFAULT_TTL", "7200")),
            key_prefix="starpower:prod:"
        )
    
    def validate_configuration(self) -> Dict[str, Any]:
        """
        Validate current configuration and return status.
        
        Returns:
            Dictionary with validation results
        """
        results = {
            "environment": self.environment,
            "database": {"valid": False, "errors": []},
            "redis": {"valid": False, "errors": []}
        }
        
        # Validate database configuration
        try:
            db_config = self.get_database_config()
            
            # Basic URL validation
            if not db_config.url:
                results["database"]["errors"].append("Database URL is empty")
            elif not (db_config.url.startswith("postgresql") or db_config.url.startswith("sqlite")):
                results["database"]["errors"].append("Invalid database URL scheme")
            
            # Pool configuration validation
            if db_config.pool_size <= 0:
                results["database"]["errors"].append("Pool size must be positive")
            
            if not results["database"]["errors"]:
                results["database"]["valid"] = True
                
        except Exception as e:
            results["database"]["errors"].append(f"Configuration error: {str(e)}")
        
        # Validate Redis configuration
        try:
            redis_config = self.get_redis_config()
            
            # Basic URL validation
            if not redis_config.url:
                results["redis"]["errors"].append("Redis URL is empty")
            elif not redis_config.url.startswith("redis://"):
                results["redis"]["errors"].append("Invalid Redis URL scheme")
            
            # Connection configuration validation
            if redis_config.max_connections <= 0:
                results["redis"]["errors"].append("Max connections must be positive")
            
            if not results["redis"]["errors"]:
                results["redis"]["valid"] = True
                
        except Exception as e:
            results["redis"]["errors"].append(f"Configuration error: {str(e)}")
        
        return results
    
    def clear_cache(self):
        """Clear configuration cache."""
        self.config_cache.clear()
        logger.info("Configuration cache cleared")


# Global configuration manager
_config_manager: Optional[DatabaseConfigManager] = None


def get_config_manager(environment: Optional[str] = None) -> DatabaseConfigManager:
    """
    Get global configuration manager instance.
    
    Args:
        environment: Environment name (only used on first call)
        
    Returns:
        Configuration manager instance
    """
    global _config_manager
    if not _config_manager:
        _config_manager = DatabaseConfigManager(environment)
    return _config_manager


def get_database_config(environment: Optional[str] = None) -> DatabaseConfig:
    """
    Get database configuration for environment.
    
    Args:
        environment: Environment name
        
    Returns:
        Database configuration
    """
    config_manager = get_config_manager(environment)
    return config_manager.get_database_config()


def get_redis_config(environment: Optional[str] = None) -> RedisConfig:
    """
    Get Redis configuration for environment.
    
    Args:
        environment: Environment name
        
    Returns:
        Redis configuration
    """
    config_manager = get_config_manager(environment)
    return config_manager.get_redis_config()


def validate_all_configurations() -> Dict[str, Any]:
    """
    Validate all database configurations.
    
    Returns:
        Validation results for all environments
    """
    results = {}
    
    for env in ["development", "testing", "production"]:
        try:
            config_manager = DatabaseConfigManager(env)
            results[env] = config_manager.validate_configuration()
        except Exception as e:
            results[env] = {
                "environment": env,
                "database": {"valid": False, "errors": [str(e)]},
                "redis": {"valid": False, "errors": [str(e)]}
            }
    
    return results
