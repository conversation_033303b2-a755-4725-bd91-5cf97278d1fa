"""
Database Connection Manager

Manages PostgreSQL and Redis connections with connection pooling,
health monitoring, and automatic reconnection capabilities.
"""

import asyncio
import logging
import time
from typing import Optional, Dict, Any, AsyncGenerator
from contextlib import asynccontextmanager
from datetime import datetime

import asyncpg
import redis.asyncio as redis
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import declarative_base
from sqlalchemy.pool import NullPool
from sqlalchemy import text

from .models import DatabaseConfig, RedisConfig

logger = logging.getLogger(__name__)

# SQLAlchemy base for ORM models
Base = declarative_base()


class DatabaseConnectionManager:
    """Manages PostgreSQL database connections with pooling and health monitoring."""
    
    def __init__(self, config: DatabaseConfig):
        """
        Initialize database connection manager.
        
        Args:
            config: Database configuration
        """
        self.config = config
        self.engine = None
        self.session_factory = None
        self._health_status = {"status": "disconnected", "last_check": None}
        
    async def initialize(self) -> bool:
        """
        Initialize database connection and create session factory.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            # Create async engine with connection pooling
            self.engine = create_async_engine(
                self.config.url,
                pool_size=self.config.pool_size,
                max_overflow=self.config.max_overflow,
                pool_timeout=self.config.pool_timeout,
                pool_recycle=self.config.pool_recycle,
                echo=self.config.echo,
                poolclass=NullPool if "sqlite" in self.config.url else None
            )
            
            # Create session factory
            self.session_factory = async_sessionmaker(
                self.engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            
            # Test connection
            await self._test_connection()
            
            logger.info("Database connection manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize database connection: {e}")
            self._health_status = {
                "status": "error",
                "last_check": datetime.utcnow(),
                "error": str(e)
            }
            return False
    
    async def _test_connection(self) -> bool:
        """Test database connection health."""
        try:
            async with self.engine.begin() as conn:
                await conn.execute(text("SELECT 1"))
            
            self._health_status = {
                "status": "healthy",
                "last_check": datetime.utcnow()
            }
            return True
            
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            self._health_status = {
                "status": "unhealthy",
                "last_check": datetime.utcnow(),
                "error": str(e)
            }
            return False
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """
        Get database session with automatic cleanup.
        
        Yields:
            AsyncSession: Database session
        """
        if not self.session_factory:
            raise RuntimeError("Database not initialized")
        
        async with self.session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def execute_query(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> Any:
        """
        Execute raw SQL query.
        
        Args:
            query: SQL query string
            parameters: Query parameters
            
        Returns:
            Query result
        """
        async with self.get_session() as session:
            result = await session.execute(text(query), parameters or {})
            return result.fetchall()
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get current database health status."""
        # Refresh health status if it's been more than 30 seconds
        if (not self._health_status.get("last_check") or 
            (datetime.utcnow() - self._health_status["last_check"]).seconds > 30):
            await self._test_connection()
        
        return self._health_status.copy()
    
    async def close(self):
        """Close database connections."""
        if self.engine:
            await self.engine.dispose()
            logger.info("Database connections closed")


class RedisConnectionManager:
    """Manages Redis connections with health monitoring and automatic reconnection."""
    
    def __init__(self, config: RedisConfig):
        """
        Initialize Redis connection manager.
        
        Args:
            config: Redis configuration
        """
        self.config = config
        self.pool = None
        self.client = None
        self._health_status = {"status": "disconnected", "last_check": None}
    
    async def initialize(self) -> bool:
        """
        Initialize Redis connection pool.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            # Create connection pool
            self.pool = redis.ConnectionPool.from_url(
                self.config.url,
                max_connections=self.config.max_connections,
                socket_timeout=self.config.socket_timeout,
                socket_connect_timeout=self.config.socket_connect_timeout,
                retry_on_timeout=self.config.retry_on_timeout
            )
            
            # Create Redis client
            self.client = redis.Redis(connection_pool=self.pool)
            
            # Test connection
            await self._test_connection()
            
            logger.info("Redis connection manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis connection: {e}")
            self._health_status = {
                "status": "error",
                "last_check": datetime.utcnow(),
                "error": str(e)
            }
            return False
    
    async def _test_connection(self) -> bool:
        """Test Redis connection health."""
        try:
            await self.client.ping()
            self._health_status = {
                "status": "healthy",
                "last_check": datetime.utcnow()
            }
            return True
            
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            self._health_status = {
                "status": "unhealthy",
                "last_check": datetime.utcnow(),
                "error": str(e)
            }
            return False
    
    async def get(self, key: str) -> Optional[str]:
        """Get value from Redis cache."""
        try:
            full_key = f"{self.config.key_prefix}{key}"
            return await self.client.get(full_key)
        except Exception as e:
            logger.error(f"Redis GET failed for key {key}: {e}")
            return None
    
    async def set(self, key: str, value: str, ttl: Optional[int] = None) -> bool:
        """Set value in Redis cache."""
        try:
            full_key = f"{self.config.key_prefix}{key}"
            ttl = ttl or self.config.default_ttl
            await self.client.setex(full_key, ttl, value)
            return True
        except Exception as e:
            logger.error(f"Redis SET failed for key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from Redis cache."""
        try:
            full_key = f"{self.config.key_prefix}{key}"
            await self.client.delete(full_key)
            return True
        except Exception as e:
            logger.error(f"Redis DELETE failed for key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in Redis cache."""
        try:
            full_key = f"{self.config.key_prefix}{key}"
            return bool(await self.client.exists(full_key))
        except Exception as e:
            logger.error(f"Redis EXISTS failed for key {key}: {e}")
            return False
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get current Redis health status."""
        # Refresh health status if it's been more than 30 seconds
        if (not self._health_status.get("last_check") or 
            (datetime.utcnow() - self._health_status["last_check"]).seconds > 30):
            await self._test_connection()
        
        return self._health_status.copy()
    
    async def close(self):
        """Close Redis connections."""
        if self.client:
            await self.client.close()
        if self.pool:
            await self.pool.disconnect()
        logger.info("Redis connections closed")


class ConnectionManager:
    """Unified connection manager for PostgreSQL and Redis."""
    
    def __init__(self, db_config: DatabaseConfig, redis_config: RedisConfig):
        """
        Initialize unified connection manager.
        
        Args:
            db_config: Database configuration
            redis_config: Redis configuration
        """
        self.db_manager = DatabaseConnectionManager(db_config)
        self.redis_manager = RedisConnectionManager(redis_config)
        self._initialized = False
    
    async def initialize(self) -> bool:
        """
        Initialize both database and Redis connections.
        
        Returns:
            True if both connections successful, False otherwise
        """
        db_success = await self.db_manager.initialize()
        redis_success = await self.redis_manager.initialize()
        
        self._initialized = db_success and redis_success
        
        if self._initialized:
            logger.info("All database connections initialized successfully")
        else:
            logger.error("Failed to initialize some database connections")
        
        return self._initialized
    
    @property
    def database(self) -> DatabaseConnectionManager:
        """Get database connection manager."""
        return self.db_manager
    
    @property
    def cache(self) -> RedisConnectionManager:
        """Get Redis connection manager."""
        return self.redis_manager
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status for all connections."""
        db_health = await self.db_manager.get_health_status()
        redis_health = await self.redis_manager.get_health_status()
        
        overall_status = "healthy"
        if db_health["status"] != "healthy" or redis_health["status"] != "healthy":
            overall_status = "degraded"
        if db_health["status"] == "error" and redis_health["status"] == "error":
            overall_status = "unhealthy"
        
        return {
            "overall_status": overall_status,
            "database": db_health,
            "redis": redis_health,
            "timestamp": datetime.utcnow()
        }
    
    async def close(self):
        """Close all database connections."""
        await self.db_manager.close()
        await self.redis_manager.close()
        logger.info("All database connections closed")


# Global connection manager instance
_connection_manager: Optional[ConnectionManager] = None


async def get_connection_manager() -> ConnectionManager:
    """Get global connection manager instance."""
    global _connection_manager
    if not _connection_manager:
        raise RuntimeError("Connection manager not initialized")
    return _connection_manager


async def initialize_connections(db_config: DatabaseConfig, redis_config: RedisConfig) -> bool:
    """Initialize global connection manager."""
    global _connection_manager
    _connection_manager = ConnectionManager(db_config, redis_config)
    return await _connection_manager.initialize()


async def close_connections():
    """Close global connection manager."""
    global _connection_manager
    if _connection_manager:
        await _connection_manager.close()
        _connection_manager = None
