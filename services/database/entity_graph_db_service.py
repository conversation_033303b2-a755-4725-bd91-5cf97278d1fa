"""
Entity Graph Database Service

Database service for managing entity relationships, actions, and endeavors.
Handles the storage and retrieval of the entity graph structure from Isolate processing.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from sqlalchemy import text, and_, or_, desc, func
from sqlalchemy.ext.asyncio import AsyncSession

from .models import EntityRelationship, EntityAction, EntityEndeavor, PaginatedResponse, create_pagination_info
from .connection_manager import get_connection_manager

logger = logging.getLogger(__name__)


class EntityGraphDBService:
    """
    Database service for entity graph management.
    
    Handles relationships, actions, and endeavors between entities
    extracted from Isolate processing.
    """
    
    def __init__(self):
        """Initialize the entity graph database service."""
        self.connection_manager = get_connection_manager()
        
        # Statistics
        self.stats = {
            "relationships_created": 0,
            "actions_created": 0,
            "endeavors_created": 0,
            "graph_queries": 0
        }
    
    # Relationship Management
    
    async def create_relationship(self, relationship: EntityRelationship) -> str:
        """
        Create a new entity relationship.
        
        Args:
            relationship: Relationship data to create
            
        Returns:
            Created relationship ID
        """
        try:
            async with self.connection_manager.get_session() as session:
                # Set timestamps
                relationship.created_at = datetime.utcnow()
                relationship.updated_at = datetime.utcnow()
                
                # In a real implementation, this would insert into the database
                relationship_id = relationship.id
                
                self.stats["relationships_created"] += 1
                logger.info(f"Created relationship: {relationship.entity1_name} -> {relationship.entity2_name}")
                
                return relationship_id
                
        except Exception as e:
            logger.error(f"Failed to create relationship: {e}")
            raise
    
    async def get_relationships_for_entity(self, entity_id: str) -> List[EntityRelationship]:
        """
        Get all relationships for an entity.
        
        Args:
            entity_id: Entity ID
            
        Returns:
            List of relationships involving the entity
        """
        try:
            async with self.connection_manager.get_session() as session:
                # In a real implementation, this would query the database
                # For now, return empty list
                
                self.stats["graph_queries"] += 1
                return []
                
        except Exception as e:
            logger.error(f"Failed to get relationships for entity {entity_id}: {e}")
            raise
    
    async def get_relationships_for_article(self, article_id: str) -> List[EntityRelationship]:
        """
        Get all relationships extracted from an article.
        
        Args:
            article_id: Article ID
            
        Returns:
            List of relationships from the article
        """
        try:
            async with self.connection_manager.get_session() as session:
                # In a real implementation, this would query the database
                # For now, return empty list
                
                return []
                
        except Exception as e:
            logger.error(f"Failed to get relationships for article {article_id}: {e}")
            raise
    
    # Action Management
    
    async def create_action(self, action: EntityAction) -> str:
        """
        Create a new entity action.
        
        Args:
            action: Action data to create
            
        Returns:
            Created action ID
        """
        try:
            async with self.connection_manager.get_session() as session:
                # Set timestamps
                action.created_at = datetime.utcnow()
                action.updated_at = datetime.utcnow()
                
                # In a real implementation, this would insert into the database
                action_id = action.id
                
                self.stats["actions_created"] += 1
                logger.info(f"Created action: {action.actor_name} -> {action.target_name} ({action.action_type})")
                
                return action_id
                
        except Exception as e:
            logger.error(f"Failed to create action: {e}")
            raise
    
    async def get_actions_for_entity(self, entity_id: str, as_actor: bool = True) -> List[EntityAction]:
        """
        Get all actions for an entity.
        
        Args:
            entity_id: Entity ID
            as_actor: If True, get actions where entity is actor; if False, where entity is target
            
        Returns:
            List of actions involving the entity
        """
        try:
            async with self.connection_manager.get_session() as session:
                # In a real implementation, this would query the database
                # For now, return empty list
                
                self.stats["graph_queries"] += 1
                return []
                
        except Exception as e:
            logger.error(f"Failed to get actions for entity {entity_id}: {e}")
            raise
    
    async def get_actions_for_article(self, article_id: str) -> List[EntityAction]:
        """
        Get all actions extracted from an article.
        
        Args:
            article_id: Article ID
            
        Returns:
            List of actions from the article
        """
        try:
            async with self.connection_manager.get_session() as session:
                # In a real implementation, this would query the database
                # For now, return empty list
                
                return []
                
        except Exception as e:
            logger.error(f"Failed to get actions for article {article_id}: {e}")
            raise
    
    # Endeavor Management
    
    async def create_endeavor(self, endeavor: EntityEndeavor) -> str:
        """
        Create a new entity endeavor.
        
        Args:
            endeavor: Endeavor data to create
            
        Returns:
            Created endeavor ID
        """
        try:
            async with self.connection_manager.get_session() as session:
                # Set timestamps
                endeavor.created_at = datetime.utcnow()
                endeavor.updated_at = datetime.utcnow()
                
                # In a real implementation, this would insert into the database
                endeavor_id = endeavor.id
                
                self.stats["endeavors_created"] += 1
                logger.info(f"Created endeavor: {endeavor.entity_name} -> {endeavor.endeavor_label}")
                
                return endeavor_id
                
        except Exception as e:
            logger.error(f"Failed to create endeavor: {e}")
            raise
    
    async def get_endeavors_for_entity(self, entity_id: str) -> List[EntityEndeavor]:
        """
        Get all endeavors for an entity.
        
        Args:
            entity_id: Entity ID
            
        Returns:
            List of endeavors for the entity
        """
        try:
            async with self.connection_manager.get_session() as session:
                # In a real implementation, this would query the database
                # For now, return empty list
                
                self.stats["graph_queries"] += 1
                return []
                
        except Exception as e:
            logger.error(f"Failed to get endeavors for entity {entity_id}: {e}")
            raise
    
    async def get_endeavors_for_article(self, article_id: str) -> List[EntityEndeavor]:
        """
        Get all endeavors extracted from an article.
        
        Args:
            article_id: Article ID
            
        Returns:
            List of endeavors from the article
        """
        try:
            async with self.connection_manager.get_session() as session:
                # In a real implementation, this would query the database
                # For now, return empty list
                
                return []
                
        except Exception as e:
            logger.error(f"Failed to get endeavors for article {article_id}: {e}")
            raise
    
    # Graph Analysis
    
    async def get_entity_graph(self, entity_id: str, depth: int = 1) -> Dict[str, Any]:
        """
        Get the complete graph for an entity up to specified depth.
        
        Args:
            entity_id: Central entity ID
            depth: Graph traversal depth
            
        Returns:
            Entity graph data
        """
        try:
            # Get relationships
            relationships = await self.get_relationships_for_entity(entity_id)
            
            # Get actions (as both actor and target)
            actions_as_actor = await self.get_actions_for_entity(entity_id, as_actor=True)
            actions_as_target = await self.get_actions_for_entity(entity_id, as_actor=False)
            
            # Get endeavors
            endeavors = await self.get_endeavors_for_entity(entity_id)
            
            graph = {
                "entity_id": entity_id,
                "relationships": [rel.dict() for rel in relationships],
                "actions_as_actor": [action.dict() for action in actions_as_actor],
                "actions_as_target": [action.dict() for action in actions_as_target],
                "endeavors": [endeavor.dict() for endeavor in endeavors],
                "depth": depth
            }
            
            self.stats["graph_queries"] += 1
            return graph
            
        except Exception as e:
            logger.error(f"Failed to get entity graph for {entity_id}: {e}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """Get service statistics."""
        return self.stats.copy()
    
    async def health_check(self) -> bool:
        """Check service health."""
        try:
            # Simple health check - try to get connection
            async with self.connection_manager.get_session() as session:
                return True
        except Exception as e:
            logger.error(f"Entity graph service health check failed: {e}")
            return False
