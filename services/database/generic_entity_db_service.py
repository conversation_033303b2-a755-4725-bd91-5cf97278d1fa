"""
Generic Entity Database Service

Database service for managing non-person entities (organizations, objects, events, etc.)
with CRUD operations, search capabilities, and relationship management.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from difflib import SequenceMatcher

from sqlalchemy import text, and_, or_, desc, func
from sqlalchemy.ext.asyncio import AsyncSession

from .models import GenericEntity, DataSource, PaginatedResponse, create_pagination_info
from .connection_manager import get_connection_manager

logger = logging.getLogger(__name__)


class GenericEntityDBService:
    """
    Database service for generic entity management.
    
    Provides CRUD operations, search capabilities, and entity resolution
    for non-person entities in the Star Power system.
    """
    
    def __init__(self):
        """Initialize the generic entity database service."""
        self.connection_manager = get_connection_manager()
        
        # Statistics
        self.stats = {
            "entities_created": 0,
            "entities_updated": 0,
            "entities_retrieved": 0,
            "search_queries": 0
        }
    
    async def create_entity(self, entity: GenericEntity) -> str:
        """
        Create a new generic entity in the database.
        
        Args:
            entity: Generic entity data to create
            
        Returns:
            Created entity ID
        """
        try:
            async with self.connection_manager.get_session() as session:
                # In a real implementation, this would use SQLAlchemy ORM
                # For now, we'll simulate the creation
                
                # Validate entity data
                if not entity.name or not entity.entity_type:
                    raise ValueError("Entity name and type are required")
                
                # Set timestamps
                entity.created_at = datetime.utcnow()
                entity.updated_at = datetime.utcnow()
                
                # In a real implementation, this would insert into the database
                # For now, we'll just return the entity ID
                entity_id = entity.id
                
                self.stats["entities_created"] += 1
                logger.info(f"Created generic entity: {entity.name} ({entity.entity_type}) -> {entity_id}")
                
                return entity_id
                
        except Exception as e:
            logger.error(f"Failed to create generic entity {entity.name}: {e}")
            raise
    
    async def get_entity(self, entity_id: str) -> Optional[GenericEntity]:
        """
        Retrieve a generic entity by ID.
        
        Args:
            entity_id: Entity ID to retrieve
            
        Returns:
            Generic entity data or None if not found
        """
        try:
            async with self.connection_manager.get_session() as session:
                # In a real implementation, this would query the database
                # For now, we'll return None (entity not found)
                
                self.stats["entities_retrieved"] += 1
                return None
                
        except Exception as e:
            logger.error(f"Failed to retrieve generic entity {entity_id}: {e}")
            raise
    
    async def update_entity(self, entity_id: str, update_data: Dict[str, Any]) -> bool:
        """
        Update a generic entity.
        
        Args:
            entity_id: Entity ID to update
            update_data: Data to update
            
        Returns:
            True if updated successfully
        """
        try:
            async with self.connection_manager.get_session() as session:
                # In a real implementation, this would update the database
                # For now, we'll simulate success
                
                update_data["updated_at"] = datetime.utcnow()
                
                self.stats["entities_updated"] += 1
                logger.info(f"Updated generic entity {entity_id}")
                
                return True
                
        except Exception as e:
            logger.error(f"Failed to update generic entity {entity_id}: {e}")
            raise
    
    async def search_entities(
        self,
        query: Optional[str] = None,
        entity_type: Optional[str] = None,
        min_confidence: Optional[float] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[GenericEntity]:
        """
        Search for generic entities.
        
        Args:
            query: Search query (name-based)
            entity_type: Filter by entity type
            min_confidence: Minimum confidence score
            limit: Maximum number of results
            offset: Offset for pagination
            
        Returns:
            List of matching generic entities
        """
        try:
            async with self.connection_manager.get_session() as session:
                # In a real implementation, this would query the database
                # For now, we'll return an empty list
                
                self.stats["search_queries"] += 1
                logger.debug(f"Searching entities: query='{query}', type='{entity_type}'")
                
                return []
                
        except Exception as e:
            logger.error(f"Failed to search generic entities: {e}")
            raise
    
    async def find_by_name_and_type(self, name: str, entity_type: str) -> Optional[GenericEntity]:
        """
        Find entity by exact name and type match.
        
        Args:
            name: Entity name
            entity_type: Entity type
            
        Returns:
            Matching entity or None
        """
        try:
            entities = await self.search_entities(
                query=name,
                entity_type=entity_type,
                limit=10
            )
            
            # Look for exact match
            for entity in entities:
                if entity.name.lower() == name.lower() and entity.entity_type == entity_type:
                    return entity
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to find entity by name and type: {e}")
            raise
    
    async def find_or_create(self, entity_data: GenericEntity) -> str:
        """
        Find existing entity or create new one.
        
        Args:
            entity_data: Entity data to find or create
            
        Returns:
            Entity ID (existing or newly created)
        """
        try:
            # Try to find existing entity
            existing_entity = await self.find_by_name_and_type(
                entity_data.name, 
                entity_data.entity_type
            )
            
            if existing_entity:
                logger.debug(f"Found existing entity: {entity_data.name} ({entity_data.entity_type})")
                return existing_entity.id
            
            # Create new entity
            entity_id = await self.create_entity(entity_data)
            logger.debug(f"Created new entity: {entity_data.name} ({entity_data.entity_type})")
            return entity_id
            
        except Exception as e:
            logger.error(f"Failed to find or create entity: {e}")
            raise
    
    async def get_entities_by_article(self, article_id: str) -> List[GenericEntity]:
        """
        Get all entities associated with an article.
        
        Args:
            article_id: Article ID
            
        Returns:
            List of entities associated with the article
        """
        try:
            async with self.connection_manager.get_session() as session:
                # In a real implementation, this would query relationships
                # For now, return empty list
                
                return []
                
        except Exception as e:
            logger.error(f"Failed to get entities for article {article_id}: {e}")
            raise
    
    async def delete_entity(self, entity_id: str) -> bool:
        """
        Delete a generic entity.
        
        Args:
            entity_id: Entity ID to delete
            
        Returns:
            True if deleted successfully
        """
        try:
            async with self.connection_manager.get_session() as session:
                # In a real implementation, this would delete from database
                # For now, simulate success
                
                logger.info(f"Deleted generic entity {entity_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to delete generic entity {entity_id}: {e}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """Get service statistics."""
        return self.stats.copy()
    
    async def health_check(self) -> bool:
        """Check service health."""
        try:
            # Simple health check - try to get connection
            async with self.connection_manager.get_session() as session:
                return True
        except Exception as e:
            logger.error(f"Generic entity service health check failed: {e}")
            return False
