"""
Database Migrations

Handles database schema creation and migration management for PostgreSQL.
Provides version control for database schema changes.
"""

import logging
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime
from pathlib import Path

from sqlalchemy import text, MetaData, Table, Column, String, DateTime, Integer, <PERSON>olean
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.ext.asyncio import AsyncSession

from .connection_manager import get_connection_manager

logger = logging.getLogger(__name__)


class MigrationManager:
    """Manages database schema migrations."""
    
    def __init__(self):
        """Initialize migration manager."""
        self.migrations_table = "schema_migrations"
        self._applied_migrations = set()
    
    async def initialize_migration_table(self):
        """Create migrations tracking table if it doesn't exist."""
        connection_manager = await get_connection_manager()
        
        create_table_sql = f"""
        CREATE TABLE IF NOT EXISTS {self.migrations_table} (
            id SERIAL PRIMARY KEY,
            version VARCHAR(50) UNIQUE NOT NULL,
            name VARCHAR(200) NOT NULL,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            checksum VARCHAR(64)
        );
        """
        
        async with connection_manager.database.get_session() as session:
            await session.execute(text(create_table_sql))
            await session.commit()
        
        logger.info("Migration tracking table initialized")
    
    async def get_applied_migrations(self) -> List[str]:
        """Get list of applied migration versions."""
        connection_manager = await get_connection_manager()
        
        query = f"SELECT version FROM {self.migrations_table} ORDER BY applied_at"
        
        async with connection_manager.database.get_session() as session:
            result = await session.execute(text(query))
            return [row[0] for row in result.fetchall()]
    
    async def mark_migration_applied(self, version: str, name: str, checksum: str):
        """Mark a migration as applied."""
        connection_manager = await get_connection_manager()
        
        insert_sql = f"""
        INSERT INTO {self.migrations_table} (version, name, checksum)
        VALUES (:version, :name, :checksum)
        """
        
        async with connection_manager.database.get_session() as session:
            await session.execute(text(insert_sql), {
                "version": version,
                "name": name,
                "checksum": checksum
            })
            await session.commit()
        
        logger.info(f"Migration {version} marked as applied")
    
    async def apply_migration(self, migration: 'Migration'):
        """Apply a single migration."""
        try:
            connection_manager = await get_connection_manager()
            
            async with connection_manager.database.get_session() as session:
                # Execute migration SQL
                for sql_statement in migration.get_sql_statements():
                    await session.execute(text(sql_statement))
                
                await session.commit()
            
            # Mark as applied
            await self.mark_migration_applied(
                migration.version,
                migration.name,
                migration.checksum
            )
            
            logger.info(f"Successfully applied migration {migration.version}: {migration.name}")
            
        except Exception as e:
            logger.error(f"Failed to apply migration {migration.version}: {e}")
            raise
    
    async def run_migrations(self, migrations: List['Migration']):
        """Run all pending migrations."""
        await self.initialize_migration_table()
        applied_migrations = await self.get_applied_migrations()
        
        pending_migrations = [
            m for m in migrations 
            if m.version not in applied_migrations
        ]
        
        if not pending_migrations:
            logger.info("No pending migrations")
            return
        
        logger.info(f"Applying {len(pending_migrations)} pending migrations")
        
        for migration in sorted(pending_migrations, key=lambda m: m.version):
            await self.apply_migration(migration)
        
        logger.info("All migrations applied successfully")


class Migration:
    """Represents a single database migration."""
    
    def __init__(self, version: str, name: str, sql_statements: List[str]):
        """
        Initialize migration.
        
        Args:
            version: Migration version (e.g., "001", "002")
            name: Human-readable migration name
            sql_statements: List of SQL statements to execute
        """
        self.version = version
        self.name = name
        self.sql_statements = sql_statements
        self.checksum = self._calculate_checksum()
    
    def _calculate_checksum(self) -> str:
        """Calculate checksum for migration integrity."""
        import hashlib
        content = f"{self.version}{self.name}{''.join(self.sql_statements)}"
        return hashlib.sha256(content.encode()).hexdigest()[:16]
    
    def get_sql_statements(self) -> List[str]:
        """Get SQL statements for this migration."""
        return self.sql_statements


# Define all migrations

def get_all_migrations() -> List[Migration]:
    """Get all database migrations in order."""
    return [
        create_articles_table_migration(),
        create_celebrities_table_migration(),
        create_charts_table_migration(),
        create_analysis_results_table_migration(),
        create_processing_jobs_table_migration(),
        create_indexes_migration(),
    ]


def create_articles_table_migration() -> Migration:
    """Create articles table migration."""
    sql = """
    CREATE TABLE IF NOT EXISTS articles (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        title VARCHAR(500) NOT NULL,
        content TEXT NOT NULL,
        url VARCHAR(1000) NOT NULL,
        published_at TIMESTAMP NOT NULL,
        source VARCHAR(100) NOT NULL,
        entities JSONB DEFAULT '[]',
        processing_status VARCHAR(20) DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        celebrity_mentions JSONB DEFAULT '[]',
        extraction_confidence DECIMAL(3,2),
        language VARCHAR(10),
        word_count INTEGER
    );
    """
    
    return Migration("001", "Create articles table", [sql])


def create_celebrities_table_migration() -> Migration:
    """Create celebrities table migration."""
    sql = """
    CREATE TABLE IF NOT EXISTS celebrities (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(200) NOT NULL,
        birth_date TIMESTAMP,
        birth_time VARCHAR(10),
        birth_location VARCHAR(200),
        birth_latitude DECIMAL(10,8),
        birth_longitude DECIMAL(11,8),
        birth_timezone VARCHAR(50),
        confidence_score DECIMAL(3,2) NOT NULL,
        data_sources JSONB DEFAULT '[]',
        enhanced BOOLEAN DEFAULT FALSE,
        verified BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_verified_at TIMESTAMP,
        aliases JSONB DEFAULT '[]',
        professional_name VARCHAR(200)
    );
    """
    
    return Migration("002", "Create celebrities table", [sql])


def create_charts_table_migration() -> Migration:
    """Create charts table migration."""
    sql = """
    CREATE TABLE IF NOT EXISTS charts (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        celebrity_id UUID NOT NULL REFERENCES celebrities(id) ON DELETE CASCADE,
        chart_type VARCHAR(50) NOT NULL,
        calculation_time TIMESTAMP NOT NULL,
        location_name VARCHAR(200),
        latitude DECIMAL(10,8) NOT NULL,
        longitude DECIMAL(11,8) NOT NULL,
        timezone VARCHAR(50) NOT NULL,
        planetary_positions JSONB DEFAULT '{}',
        house_positions JSONB DEFAULT '{}',
        va_circuits JSONB DEFAULT '[]',
        total_circuits_found INTEGER DEFAULT 0,
        active_harmonics JSONB DEFAULT '[]',
        circuit_quality_score DECIMAL(3,2),
        calculation_method VARCHAR(50) DEFAULT 'swiss_ephemeris',
        processing_time_ms DECIMAL(10,3),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    return Migration("003", "Create charts table", [sql])


def create_analysis_results_table_migration() -> Migration:
    """Create analysis results table migration."""
    sql = """
    CREATE TABLE IF NOT EXISTS analysis_results (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        article_id UUID NOT NULL REFERENCES articles(id) ON DELETE CASCADE,
        celebrity_id UUID NOT NULL REFERENCES celebrities(id) ON DELETE CASCADE,
        chart_id UUID REFERENCES charts(id) ON DELETE SET NULL,
        analysis_text TEXT NOT NULL,
        key_insights JSONB DEFAULT '[]',
        astrological_themes JSONB DEFAULT '[]',
        relevance_score DECIMAL(3,2) NOT NULL,
        confidence_level VARCHAR(20) DEFAULT 'unknown',
        word_count INTEGER,
        llm_model VARCHAR(100) NOT NULL,
        processing_time_ms DECIMAL(10,3),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        published BOOLEAN DEFAULT FALSE,
        published_at TIMESTAMP
    );
    """
    
    return Migration("004", "Create analysis results table", [sql])


def create_processing_jobs_table_migration() -> Migration:
    """Create processing jobs table migration."""
    sql = """
    CREATE TABLE IF NOT EXISTS processing_jobs (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        job_type VARCHAR(100) NOT NULL,
        status VARCHAR(20) DEFAULT 'pending',
        parameters JSONB DEFAULT '{}',
        priority INTEGER DEFAULT 0,
        progress_percentage DECIMAL(5,2) DEFAULT 0.0,
        current_step VARCHAR(200),
        total_steps INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        started_at TIMESTAMP,
        completed_at TIMESTAMP,
        result JSONB,
        error_message TEXT,
        retry_count INTEGER DEFAULT 0,
        max_retries INTEGER DEFAULT 3
    );
    """
    
    return Migration("005", "Create processing jobs table", [sql])


def create_indexes_migration() -> Migration:
    """Create database indexes for performance."""
    sql_statements = [
        # Articles indexes
        "CREATE INDEX IF NOT EXISTS idx_articles_published_at ON articles(published_at);",
        "CREATE INDEX IF NOT EXISTS idx_articles_source ON articles(source);",
        "CREATE INDEX IF NOT EXISTS idx_articles_processing_status ON articles(processing_status);",
        "CREATE INDEX IF NOT EXISTS idx_articles_created_at ON articles(created_at);",
        
        # Celebrities indexes
        "CREATE INDEX IF NOT EXISTS idx_celebrities_name ON celebrities(name);",
        "CREATE INDEX IF NOT EXISTS idx_celebrities_birth_date ON celebrities(birth_date);",
        "CREATE INDEX IF NOT EXISTS idx_celebrities_confidence_score ON celebrities(confidence_score);",
        "CREATE INDEX IF NOT EXISTS idx_celebrities_verified ON celebrities(verified);",
        
        # Charts indexes
        "CREATE INDEX IF NOT EXISTS idx_charts_celebrity_id ON charts(celebrity_id);",
        "CREATE INDEX IF NOT EXISTS idx_charts_chart_type ON charts(chart_type);",
        "CREATE INDEX IF NOT EXISTS idx_charts_calculation_time ON charts(calculation_time);",
        "CREATE INDEX IF NOT EXISTS idx_charts_total_circuits ON charts(total_circuits_found);",
        
        # Analysis results indexes
        "CREATE INDEX IF NOT EXISTS idx_analysis_article_id ON analysis_results(article_id);",
        "CREATE INDEX IF NOT EXISTS idx_analysis_celebrity_id ON analysis_results(celebrity_id);",
        "CREATE INDEX IF NOT EXISTS idx_analysis_chart_id ON analysis_results(chart_id);",
        "CREATE INDEX IF NOT EXISTS idx_analysis_relevance_score ON analysis_results(relevance_score);",
        "CREATE INDEX IF NOT EXISTS idx_analysis_published ON analysis_results(published);",
        
        # Processing jobs indexes
        "CREATE INDEX IF NOT EXISTS idx_jobs_status ON processing_jobs(status);",
        "CREATE INDEX IF NOT EXISTS idx_jobs_job_type ON processing_jobs(job_type);",
        "CREATE INDEX IF NOT EXISTS idx_jobs_priority ON processing_jobs(priority);",
        "CREATE INDEX IF NOT EXISTS idx_jobs_created_at ON processing_jobs(created_at);",
    ]
    
    return Migration("006", "Create performance indexes", sql_statements)


# Migration runner function

async def run_all_migrations():
    """Run all database migrations."""
    migration_manager = MigrationManager()
    migrations = get_all_migrations()
    await migration_manager.run_migrations(migrations)


if __name__ == "__main__":
    # Allow running migrations directly
    asyncio.run(run_all_migrations())
