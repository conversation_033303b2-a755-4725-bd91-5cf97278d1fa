"""
Database Models

Comprehensive Pydantic data models for all core entities in the Star Power application.
These models define the structure for database storage and API serialization.
"""

from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field, field_validator, ConfigDict
from uuid import UUID, uuid4


class ProcessingStatus(str, Enum):
    """Status of data processing operations."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class ConfidenceLevel(str, Enum):
    """Confidence levels for data quality assessment."""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    UNKNOWN = "unknown"


class DataSource(str, Enum):
    """Sources of data in the system."""
    GNEWS = "gnews"
    ASTRO_DATABANK = "astro_databank"
    WIKIPEDIA = "wikipedia"
    MANUAL = "manual"
    CELEBRITY_DB = "celebrity_db"
    USER_INPUT = "user_input"
    LLM_ENHANCED = "llm_enhanced"


# Core Entity Models

class ArticleData(BaseModel):
    """News article with metadata and processing status."""
    id: str = Field(default_factory=lambda: str(uuid4()))
    title: str = Field(..., min_length=1, max_length=500)
    content: str = Field(..., min_length=1)
    url: str = Field(..., pattern=r'^https?://')
    published_at: datetime
    source: str = Field(..., min_length=1, max_length=100)
    entities: List[str] = Field(default_factory=list)
    processing_status: ProcessingStatus = ProcessingStatus.PENDING
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Processing metadata
    celebrity_mentions: List[str] = Field(default_factory=list)
    extraction_confidence: Optional[float] = Field(None, ge=0.0, le=1.0)
    language: Optional[str] = Field(None, max_length=10)
    word_count: Optional[int] = Field(None, ge=0)

    # Isolate processing metadata
    isolate_processed_at: Optional[datetime] = Field(None, description="Timestamp for Isolate processing")
    isolate_entity_ids: List[str] = Field(default_factory=list, description="Store resolved DB entity IDs")
    isolate_confidence: Optional[float] = Field(None, ge=0.0, le=1.0, description="Isolate extraction confidence")
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class CelebrityData(BaseModel):
    """Celebrity with birth data and validation metadata."""
    id: str = Field(default_factory=lambda: str(uuid4()))
    name: str = Field(..., min_length=1, max_length=200)
    birth_date: Optional[datetime] = None
    birth_time: Optional[str] = Field(None, pattern=r'^\d{2}:\d{2}(:\d{2})?$')
    birth_location: Optional[str] = Field(None, max_length=200)
    birth_latitude: Optional[float] = Field(None, ge=-90, le=90)
    birth_longitude: Optional[float] = Field(None, ge=-180, le=180)
    birth_timezone: Optional[str] = Field(None, max_length=50)
    
    # Data quality and sources
    confidence_score: float = Field(..., ge=0.0, le=1.0)
    data_sources: List[DataSource] = Field(default_factory=list)
    enhanced: bool = False
    verified: bool = False
    
    # Processing metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_verified_at: Optional[datetime] = None
    
    # Alternative names and aliases
    aliases: List[str] = Field(default_factory=list)
    professional_name: Optional[str] = Field(None, max_length=200)

    # Isolate compatibility fields
    entity_type: str = Field(default="person", description="Entity type for Isolate compatibility")
    roles: List[str] = Field(default_factory=list, description="Entity roles")
    
    @field_validator('birth_date')
    @classmethod
    def validate_birth_date(cls, v):
        if v and v > datetime.utcnow():
            raise ValueError('Birth date cannot be in the future')
        return v

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class ChartData(BaseModel):
    """Astrological chart data with VA circuit analysis."""
    id: str = Field(default_factory=lambda: str(uuid4()))
    celebrity_id: str = Field(..., description="Reference to celebrity")
    chart_type: str = Field(..., description="Type of chart (natal, transit, etc.)")
    calculation_time: datetime
    location_name: Optional[str] = Field(None, max_length=200)
    latitude: float = Field(..., ge=-90, le=90)
    longitude: float = Field(..., ge=-180, le=180)
    timezone: str = Field(..., max_length=50)
    
    # Planetary positions (nested structure for flexibility)
    planetary_positions: Dict[str, Dict[str, float]] = Field(default_factory=dict)
    house_positions: Dict[str, int] = Field(default_factory=dict)
    
    # VA Circuit Analysis Results
    va_circuits: List[Dict[str, Any]] = Field(default_factory=list)
    total_circuits_found: int = Field(default=0, ge=0)
    active_harmonics: List[int] = Field(default_factory=list)
    circuit_quality_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    
    # Processing metadata
    calculation_method: str = Field(default="swiss_ephemeris")
    processing_time_ms: Optional[float] = Field(None, ge=0)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class AnalysisResult(BaseModel):
    """Results from LLM analysis of celebrity-article combinations."""
    id: str = Field(default_factory=lambda: str(uuid4()))
    article_id: str = Field(..., description="Reference to article")
    celebrity_id: str = Field(..., description="Reference to celebrity")
    chart_id: Optional[str] = Field(None, description="Reference to chart if available")
    
    # Analysis content
    analysis_text: str = Field(..., min_length=1)
    key_insights: List[str] = Field(default_factory=list)
    astrological_themes: List[str] = Field(default_factory=list)
    
    # Quality metrics
    relevance_score: float = Field(..., ge=0.0, le=1.0)
    confidence_level: ConfidenceLevel = ConfidenceLevel.UNKNOWN
    word_count: Optional[int] = Field(None, ge=0)
    
    # Processing metadata
    llm_model: str = Field(..., description="LLM model used for analysis")
    processing_time_ms: Optional[float] = Field(None, ge=0)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    # Publication metadata
    published: bool = False
    published_at: Optional[datetime] = None
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class ProcessingJob(BaseModel):
    """Background processing job tracking."""
    id: str = Field(default_factory=lambda: str(uuid4()))
    job_type: str = Field(..., description="Type of processing job")
    status: ProcessingStatus = ProcessingStatus.PENDING
    
    # Job parameters
    parameters: Dict[str, Any] = Field(default_factory=dict)
    priority: int = Field(default=0, description="Job priority (higher = more urgent)")
    
    # Progress tracking
    progress_percentage: float = Field(default=0.0, ge=0.0, le=100.0)
    current_step: Optional[str] = None
    total_steps: Optional[int] = Field(None, ge=0)
    
    # Timing
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # Results and errors
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    retry_count: int = Field(default=0, ge=0)
    max_retries: int = Field(default=3, ge=0)
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


# Configuration Models

class DatabaseConfig(BaseModel):
    """Database connection configuration."""
    url: str = Field(..., description="Database connection URL")
    pool_size: int = Field(default=10, ge=1, le=100)
    max_overflow: int = Field(default=20, ge=0, le=100)
    pool_timeout: int = Field(default=30, ge=1)
    pool_recycle: int = Field(default=3600, ge=300)
    echo: bool = Field(default=False, description="Enable SQL query logging")
    
    # Connection retry settings
    retry_attempts: int = Field(default=3, ge=1, le=10)
    retry_delay: float = Field(default=1.0, ge=0.1, le=10.0)


class RedisConfig(BaseModel):
    """Redis cache configuration."""
    url: str = Field(default="redis://localhost:6379/0")
    max_connections: int = Field(default=20, ge=1, le=100)
    socket_timeout: float = Field(default=5.0, ge=0.1, le=30.0)
    socket_connect_timeout: float = Field(default=5.0, ge=0.1, le=30.0)
    retry_on_timeout: bool = True
    
    # Cache settings
    default_ttl: int = Field(default=3600, ge=60, description="Default TTL in seconds")
    key_prefix: str = Field(default="starpower:", max_length=50)


# Response Models for API

class PaginatedResponse(BaseModel):
    """Generic paginated response wrapper."""
    items: List[Any]
    total: int = Field(..., ge=0)
    page: int = Field(..., ge=1)
    per_page: int = Field(..., ge=1, le=100)
    pages: int = Field(..., ge=0)
    has_next: bool
    has_prev: bool


class HealthCheck(BaseModel):
    """System health check response."""
    status: str = Field(..., description="Overall system status")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    services: Dict[str, Dict[str, Any]] = Field(default_factory=dict)
    version: str = Field(default="1.0.0")
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


# Validation and utility functions

def validate_uuid(value: str) -> bool:
    """Validate UUID string format."""
    try:
        UUID(value)
        return True
    except ValueError:
        return False


def create_pagination_info(total: int, page: int, per_page: int) -> Dict[str, Any]:
    """Create pagination metadata."""
    pages = (total + per_page - 1) // per_page
    return {
        "total": total,
        "page": page,
        "per_page": per_page,
        "pages": pages,
        "has_next": page < pages,
        "has_prev": page > 1
    }


# Isolate Entity Models

class GenericEntity(BaseModel):
    """Model for non-person entities like organizations, objects, events, etc."""
    id: str = Field(default_factory=lambda: str(uuid4()))
    name: str = Field(..., min_length=1, max_length=200, description="Entity name")
    entity_type: str = Field(..., description="Entity type from Isolate schema")
    subtype: Optional[str] = Field(None, max_length=100, description="Entity subtype")
    roles: List[str] = Field(default_factory=list, description="Entity roles")

    # Inception data
    inception_date: Optional[datetime] = Field(None, description="Entity inception date")
    inception_time: Optional[str] = Field(None, pattern=r'^\d{2}:\d{2}(:\d{2})?$', description="Entity inception time")
    inception_location: Optional[str] = Field(None, max_length=200, description="Entity inception location")
    estimated_inception: bool = Field(default=False, description="Whether inception data is estimated")

    # Metadata
    relevance_reason: Optional[str] = Field(None, description="Reason for entity relevance")
    primary: bool = Field(default=False, description="Whether entity is primary")
    confidence_score: float = Field(default=0.7, ge=0.0, le=1.0, description="Entity confidence score")
    data_sources: List[DataSource] = Field(default_factory=list, description="Data sources")

    # Processing metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class EntityRelationship(BaseModel):
    """Ongoing relationships between entities"""
    id: str = Field(default_factory=lambda: str(uuid4()))
    entity1_id: str = Field(..., description="First entity ID")
    entity2_id: str = Field(..., description="Second entity ID")
    entity1_name: str = Field(..., description="First entity name (for reference)")
    entity2_name: str = Field(..., description="Second entity name (for reference)")
    relation_type: str = Field(..., description="Type of relationship")
    both_primary: bool = Field(default=False, description="Whether both entities are primary")

    # Inception data
    inception_date: Optional[datetime] = Field(None, description="Relationship inception date")
    inception_time: Optional[str] = Field(None, pattern=r'^\d{2}:\d{2}(:\d{2})?$', description="Relationship inception time")
    inception_location: Optional[str] = Field(None, max_length=200, description="Relationship inception location")
    estimated_inception: bool = Field(default=False, description="Whether inception data is estimated")

    # Metadata
    confidence_score: float = Field(default=0.7, ge=0.0, le=1.0, description="Relationship confidence score")
    article_id: Optional[str] = Field(None, description="Source article ID")

    # Processing metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class EntityAction(BaseModel):
    """Singular events with specific timing"""
    id: str = Field(default_factory=lambda: str(uuid4()))
    actor_id: str = Field(..., description="Actor entity ID")
    target_id: str = Field(..., description="Target entity ID")
    actor_name: str = Field(..., description="Actor entity name (for reference)")
    target_name: str = Field(..., description="Target entity name (for reference)")
    action_type: str = Field(..., description="Type of action")
    both_primary: bool = Field(default=False, description="Whether both entities are primary")

    # Inception data (required for actions)
    inception_date: datetime = Field(..., description="Action date")
    inception_time: Optional[str] = Field(None, pattern=r'^\d{2}:\d{2}(:\d{2})?$', description="Action time")
    inception_location: Optional[str] = Field(None, max_length=200, description="Action location")
    estimated_inception: bool = Field(default=False, description="Whether inception data is estimated")

    # Metadata
    confidence_score: float = Field(default=0.7, ge=0.0, le=1.0, description="Action confidence score")
    article_id: Optional[str] = Field(None, description="Source article ID")

    # Processing metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class EntityEndeavor(BaseModel):
    """Projects, releases, and significant activities"""
    id: str = Field(default_factory=lambda: str(uuid4()))
    entity_id: str = Field(..., description="Entity ID")
    entity_name: str = Field(..., description="Entity name (for reference)")
    endeavor_type: str = Field(..., description="Type of endeavor")
    endeavor_label: str = Field(..., description="Endeavor label/name")
    primary: bool = Field(default=False, description="Whether entity is primary")

    # Inception data
    inception_date: Optional[datetime] = Field(None, description="Endeavor inception date")
    inception_time: Optional[str] = Field(None, pattern=r'^\d{2}:\d{2}(:\d{2})?$', description="Endeavor inception time")
    inception_location: Optional[str] = Field(None, max_length=200, description="Endeavor inception location")
    estimated_inception: bool = Field(default=False, description="Whether inception data is estimated")

    # Metadata
    confidence_score: float = Field(default=0.7, ge=0.0, le=1.0, description="Endeavor confidence score")
    article_id: Optional[str] = Field(None, description="Source article ID")

    # Processing metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )
