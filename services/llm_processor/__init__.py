# LLM Processor Service Package
"""
LLM Processor Service - Multi-Model LLM Integration

This package handles LLM integration and processing:
- ollama_client.py: Local LLM integration
- openai_client.py: OpenAI API fallback
- response_processor.py: Multi-format response parsing
- quality_assessor.py: Response quality assessment
- llm_orchestrator.py: Multi-model coordination
- celebrity_data_validator.py: Celebrity data enhancement and validation
"""

from .models import (
    LLMProvider, LLMTaskType, LLMConfig, LLMRequest, LLMResponse,
    CelebrityMatch, CelebrityIdentificationRequest, CelebrityIdentificationResponse,
    LLMError, LLMTimeoutError, LLMQuotaError, LLMValidationError
)
from .isolate_models import (
    IsolateEntity, IsolateRelationship, IsolateAction, IsolateEndeavor, IsolateResponse,
    IsolateExtractionRequest, IsolateExtractionResponse
)
from .ollama_client import OllamaClient
from .openai_client import OpenAIClient
from .perplexity_client import PerplexityClient
from .entity_resolver import EntityResolver
from .response_processor import ResponseProcessor
from .quality_assessor import ResponseQualityAssessor
from .llm_orchestrator import LLMOrchestrator
from .celebrity_data_validator import CelebrityDataValidator

__version__ = "1.0.0"
__all__ = [
    # Models
    "LLMProvider", "LLMTaskType", "LLMConfig", "LLMRequest", "LLMResponse",
    "CelebrityMatch", "CelebrityIdentificationRequest", "CelebrityIdentificationResponse",
    "LLMError", "LLMTimeoutError", "LLMQuotaError", "LLMValidationError",

    # Isolate Models
    "IsolateEntity", "IsolateRelationship", "IsolateAction", "IsolateEndeavor", "IsolateResponse",
    "IsolateExtractionRequest", "IsolateExtractionResponse",

    # Clients
    "OllamaClient", "OpenAIClient", "PerplexityClient",

    # Processors
    "ResponseProcessor", "ResponseQualityAssessor", "CelebrityDataValidator",
    "EntityResolver",

    # Orchestrator
    "LLMOrchestrator"
]
