"""
Celebrity Data Enhancement & Validation Service

This service provides multi-source celebrity data validation, birth data confidence scoring,
cultural name matching with alias expansion, and context validation using article content.
"""

import asyncio
import logging
import re
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from difflib import SequenceMatcher
import aiohttp
from urllib.parse import quote

from .models import CelebrityMatch, LLMRequest, LLMResponse
from ..database.models import CelebrityData, DataSource
from ..database.celebrity_db_service import CelebrityDBService

logger = logging.getLogger(__name__)


class CelebrityDataValidator:
    """
    Multi-source celebrity data validation and enhancement service.
    
    Provides birth data confidence scoring, cultural name matching,
    and context validation using article content analysis.
    """
    
    def __init__(self):
        """Initialize the celebrity data validator."""
        self.celebrity_db = CelebrityDBService()
        self.session = None
        
        # Cultural name patterns for better matching
        self.cultural_patterns = {
            'western': r'^[A-Za-z\s\-\'\.]+$',
            'chinese': r'[\u4e00-\u9fff]+',
            'japanese': r'[\u3040-\u309f\u30a0-\u30ff\u4e00-\u9fff]+',
            'korean': r'[\uac00-\ud7af]+',
            'arabic': r'[\u0600-\u06ff]+',
            'cyrillic': r'[\u0400-\u04ff]+'
        }
        
        # Common name variations and aliases
        self.name_variations = {
            'william': ['bill', 'billy', 'will', 'willie'],
            'robert': ['bob', 'bobby', 'rob', 'robbie'],
            'richard': ['rick', 'ricky', 'dick', 'rich'],
            'michael': ['mike', 'mickey', 'mick'],
            'elizabeth': ['liz', 'lizzy', 'beth', 'betty'],
            'jennifer': ['jen', 'jenny', 'jenn'],
            'christopher': ['chris', 'christie'],
            'matthew': ['matt', 'matty']
        }
        
        # Birth data validation thresholds
        self.min_birth_year = 1850  # Reasonable historical limit
        self.max_birth_year = datetime.now().year - 10  # Must be at least 10 years old

        # External data source configurations
        self.external_sources = {
            'wikipedia': {
                'base_url': 'https://en.wikipedia.org/api/rest_v1',
                'search_url': 'https://en.wikipedia.org/w/api.php',
                'enabled': True
            },
            'astrodatabank': {
                'base_url': 'https://www.astro.com/astro-databank',
                'enabled': True  # Note: This would require web scraping or API access
            },
            'imdb': {
                'base_url': 'https://www.imdb.com',
                'enabled': True  # Note: This would require web scraping or API access
            }
        }
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def validate_and_enhance_celebrity(
        self, 
        celebrity_match: CelebrityMatch, 
        article_content: str
    ) -> Tuple[CelebrityData, float]:
        """
        Validate and enhance celebrity data from multiple sources.
        
        Args:
            celebrity_match: Initial celebrity match from LLM
            article_content: Article content for context validation
            
        Returns:
            Tuple of (enhanced_celebrity_data, confidence_score)
        """
        logger.info(f"Validating celebrity: {celebrity_match.name}")
        
        # Step 1: Cultural name matching and alias expansion
        expanded_names = await self._expand_name_aliases(celebrity_match.name)
        
        # Step 2: Multi-source data validation
        validation_results = await self._validate_from_multiple_sources(
            celebrity_match, expanded_names
        )
        
        # Step 3: Birth data confidence scoring
        birth_confidence = await self._score_birth_data_confidence(
            celebrity_match, validation_results
        )
        
        # Step 4: Context validation using article content
        context_confidence = await self._validate_context(
            celebrity_match, article_content
        )
        
        # Step 5: Calculate overall confidence score
        overall_confidence = self._calculate_overall_confidence(
            celebrity_match.confidence,
            birth_confidence,
            context_confidence,
            validation_results
        )
        
        # Step 6: Create enhanced celebrity data
        enhanced_celebrity = await self._create_enhanced_celebrity_data(
            celebrity_match, validation_results, overall_confidence
        )
        
        logger.info(f"Enhanced celebrity {celebrity_match.name} with confidence {overall_confidence:.2f}")
        return enhanced_celebrity, overall_confidence
    
    async def _expand_name_aliases(self, name: str) -> List[str]:
        """
        Expand name with cultural variations and common aliases.
        
        Args:
            name: Celebrity name
            
        Returns:
            List of name variations and aliases
        """
        aliases = [name.lower().strip()]
        
        # Add common nickname variations
        name_parts = name.lower().split()
        for part in name_parts:
            if part in self.name_variations:
                for variation in self.name_variations[part]:
                    # Create variations with the nickname
                    new_name = name.lower().replace(part, variation)
                    aliases.append(new_name)
        
        # Add initials version (e.g., "J.K. Rowling")
        if len(name_parts) >= 2:
            initials = '.'.join([part[0].upper() for part in name_parts[:-1]])
            aliases.append(f"{initials}. {name_parts[-1].title()}")
        
        # Add reversed name order for some cultures
        if len(name_parts) == 2:
            aliases.append(f"{name_parts[1].title()} {name_parts[0].title()}")
        
        return list(set(aliases))
    
    async def _validate_from_multiple_sources(
        self, 
        celebrity_match: CelebrityMatch, 
        expanded_names: List[str]
    ) -> Dict[str, Any]:
        """
        Validate celebrity data from multiple sources.
        
        Args:
            celebrity_match: Celebrity match to validate
            expanded_names: List of name variations
            
        Returns:
            Dictionary with validation results from multiple sources
        """
        validation_results = {
            'database_matches': [],
            'external_sources': {},
            'birth_data_sources': [],
            'confidence_factors': []
        }
        
        # Check existing database with error handling
        try:
            for name_variant in expanded_names:
                db_matches = await self.celebrity_db.find_celebrity_by_name(name_variant)
                for celebrity, similarity in db_matches:
                    if similarity >= 0.8:  # High similarity threshold
                        validation_results['database_matches'].append({
                            'celebrity': celebrity,
                            'similarity': similarity,
                            'name_variant': name_variant
                        })
        except Exception as e:
            logger.warning(f"Database error during celebrity validation: {e}")
            validation_results['confidence_factors'].append('database_error_encountered')

        # Validate from external sources
        external_validation = await self._validate_from_external_sources(
            celebrity_match, expanded_names
        )
        validation_results['external_sources'] = external_validation

        # Validate birth data if provided
        if celebrity_match.birth_date:
            birth_validation = await self._validate_birth_date(celebrity_match.birth_date)
            validation_results['birth_data_sources'].append({
                'source': 'llm_provided',
                'birth_date': celebrity_match.birth_date,
                'validation': birth_validation
            })
        
        # Add confidence factors based on available data
        if celebrity_match.birth_location:
            validation_results['confidence_factors'].append('birth_location_provided')
        
        if celebrity_match.profession:
            validation_results['confidence_factors'].append('profession_provided')
        
        if celebrity_match.aliases:
            validation_results['confidence_factors'].append('aliases_provided')
        
        return validation_results
    
    async def _validate_birth_date(self, birth_date_str: str) -> Dict[str, Any]:
        """
        Validate birth date string and extract confidence information.
        
        Args:
            birth_date_str: Birth date string
            
        Returns:
            Dictionary with validation results
        """
        validation = {
            'valid': False,
            'parsed_date': None,
            'confidence': 0.0,
            'issues': []
        }
        
        try:
            # Try to parse various date formats
            date_formats = [
                '%Y-%m-%d',
                '%m/%d/%Y',
                '%d/%m/%Y',
                '%B %d, %Y',
                '%d %B %Y',
                '%Y'
            ]
            
            parsed_date = None
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(birth_date_str.strip(), fmt)
                    break
                except ValueError:
                    continue
            
            if not parsed_date:
                validation['issues'].append('unparseable_date_format')
                return validation
            
            validation['parsed_date'] = parsed_date
            
            # Validate year range
            if parsed_date.year < self.min_birth_year:
                validation['issues'].append('too_old')
                validation['confidence'] = 0.1
            elif parsed_date.year > self.max_birth_year:
                validation['issues'].append('too_young')
                validation['confidence'] = 0.1
            else:
                validation['valid'] = True
                validation['confidence'] = 0.8
                
                # Higher confidence for more specific dates
                if parsed_date.month != 1 or parsed_date.day != 1:
                    validation['confidence'] = 0.9
        
        except Exception as e:
            validation['issues'].append(f'parsing_error: {str(e)}')
            logger.warning(f"Birth date validation error: {e}")
        
        return validation
    
    async def _score_birth_data_confidence(
        self, 
        celebrity_match: CelebrityMatch, 
        validation_results: Dict[str, Any]
    ) -> float:
        """
        Score birth data confidence based on validation results.
        
        Args:
            celebrity_match: Celebrity match
            validation_results: Validation results from multiple sources
            
        Returns:
            Birth data confidence score (0.0 to 1.0)
        """
        confidence = 0.0
        
        # Base confidence from birth date validation
        for birth_source in validation_results['birth_data_sources']:
            if birth_source['validation']['valid']:
                confidence += birth_source['validation']['confidence'] * 0.6
        
        # Bonus for birth location
        if celebrity_match.birth_location:
            confidence += 0.2
        
        # Bonus for database matches with birth data
        for match in validation_results['database_matches']:
            if match['celebrity'].birth_date:
                confidence += 0.1 * match['similarity']
        
        # Bonus for multiple confidence factors
        factor_bonus = min(0.2, len(validation_results['confidence_factors']) * 0.05)
        confidence += factor_bonus
        
        return min(1.0, confidence)
    
    async def _validate_context(
        self, 
        celebrity_match: CelebrityMatch, 
        article_content: str
    ) -> float:
        """
        Validate celebrity mention using article content context.
        
        Args:
            celebrity_match: Celebrity match
            article_content: Article content
            
        Returns:
            Context validation confidence score (0.0 to 1.0)
        """
        if not article_content:
            return 0.5  # Neutral if no content
        
        confidence = 0.0
        content_lower = article_content.lower()
        name_lower = celebrity_match.name.lower()
        
        # Check if name appears in article
        if name_lower in content_lower:
            confidence += 0.4
            
            # Bonus for multiple mentions
            mention_count = content_lower.count(name_lower)
            confidence += min(0.2, mention_count * 0.05)
        
        # Check for profession context
        if celebrity_match.profession:
            profession_keywords = celebrity_match.profession.lower().split()
            for keyword in profession_keywords:
                if keyword in content_lower:
                    confidence += 0.1
        
        # Check for aliases in content
        for alias in celebrity_match.aliases:
            if alias.lower() in content_lower:
                confidence += 0.1
        
        # Check for celebrity-related keywords
        celebrity_keywords = [
            'actor', 'actress', 'singer', 'musician', 'director', 'producer',
            'celebrity', 'star', 'famous', 'artist', 'performer', 'entertainer'
        ]
        
        keyword_matches = sum(1 for keyword in celebrity_keywords if keyword in content_lower)
        confidence += min(0.2, keyword_matches * 0.05)
        
        return min(1.0, confidence)
    
    def _calculate_overall_confidence(
        self,
        llm_confidence: float,
        birth_confidence: float,
        context_confidence: float,
        validation_results: Dict[str, Any]
    ) -> float:
        """
        Calculate overall confidence score from multiple factors.
        
        Args:
            llm_confidence: Original LLM confidence
            birth_confidence: Birth data confidence
            context_confidence: Context validation confidence
            validation_results: Validation results
            
        Returns:
            Overall confidence score (0.0 to 1.0)
        """
        # Weighted average of confidence factors
        weights = {
            'llm': 0.25,
            'birth': 0.2,
            'context': 0.2,
            'database': 0.15,
            'external': 0.2
        }

        # Database confidence from existing matches
        database_confidence = 0.0
        if validation_results['database_matches']:
            best_match = max(validation_results['database_matches'], key=lambda x: x['similarity'])
            database_confidence = best_match['similarity']

        # External sources confidence
        external_confidence = 0.0
        if validation_results.get('external_sources'):
            external_sources = validation_results['external_sources']
            source_confidences = []

            for source_name, source_data in external_sources.items():
                if source_data.get('found') and source_data.get('confidence', 0) > 0:
                    source_confidences.append(source_data['confidence'])

            if source_confidences:
                external_confidence = max(source_confidences)  # Use best external source

        overall_confidence = (
            llm_confidence * weights['llm'] +
            birth_confidence * weights['birth'] +
            context_confidence * weights['context'] +
            database_confidence * weights['database'] +
            external_confidence * weights['external']
        )
        
        # Bonus for multiple validation sources
        source_count = len([
            c for c in [llm_confidence, birth_confidence, context_confidence, database_confidence]
            if c > 0.5
        ])
        
        if source_count >= 3:
            overall_confidence += 0.1
        
        return min(1.0, overall_confidence)
    
    async def _create_enhanced_celebrity_data(
        self,
        celebrity_match: CelebrityMatch,
        validation_results: Dict[str, Any],
        confidence_score: float
    ) -> CelebrityData:
        """
        Create enhanced celebrity data from validation results.
        
        Args:
            celebrity_match: Original celebrity match
            validation_results: Validation results
            confidence_score: Overall confidence score
            
        Returns:
            Enhanced celebrity data
        """
        # Start with best database match if available
        if validation_results['database_matches']:
            best_match = max(validation_results['database_matches'], key=lambda x: x['similarity'])
            base_celebrity = best_match['celebrity']
            
            # Update with new information
            enhanced_celebrity = CelebrityData(
                id=base_celebrity.id,
                name=base_celebrity.name,
                birth_date=base_celebrity.birth_date,
                birth_time=base_celebrity.birth_time,
                birth_location=base_celebrity.birth_location,
                birth_latitude=base_celebrity.birth_latitude,
                birth_longitude=base_celebrity.birth_longitude,
                birth_timezone=base_celebrity.birth_timezone,
                confidence_score=max(base_celebrity.confidence_score, confidence_score),
                data_sources=list(set(base_celebrity.data_sources + [DataSource.LLM_ENHANCED])),
                enhanced=True,
                verified=base_celebrity.verified,
                aliases=list(set(base_celebrity.aliases + celebrity_match.aliases)),
                professional_name=base_celebrity.professional_name or celebrity_match.name
            )
        else:
            # Create new celebrity data
            birth_date = None
            if celebrity_match.birth_date:
                for birth_source in validation_results['birth_data_sources']:
                    if birth_source['validation']['valid']:
                        birth_date = birth_source['validation']['parsed_date']
                        break
            
            enhanced_celebrity = CelebrityData(
                name=celebrity_match.name,
                birth_date=birth_date,
                birth_location=celebrity_match.birth_location,
                confidence_score=confidence_score,
                data_sources=[DataSource.LLM_ENHANCED],
                enhanced=True,
                verified=False,
                aliases=celebrity_match.aliases,
                professional_name=celebrity_match.name
            )
        
        return enhanced_celebrity

    async def _validate_from_external_sources(
        self,
        celebrity_match: CelebrityMatch,
        expanded_names: List[str]
    ) -> Dict[str, Any]:
        """
        Validate celebrity data from external sources (Wikipedia, AstroDatabank, IMDB).

        Args:
            celebrity_match: Celebrity match to validate
            expanded_names: List of name variations

        Returns:
            Dictionary with external source validation results
        """
        external_results = {
            'wikipedia': {'found': False, 'confidence': 0.0, 'data': {}},
            'astrodatabank': {'found': False, 'confidence': 0.0, 'data': {}},
            'imdb': {'found': False, 'confidence': 0.0, 'data': {}}
        }

        # Validate from Wikipedia
        if self.external_sources['wikipedia']['enabled']:
            try:
                wikipedia_data = await self._validate_wikipedia(celebrity_match, expanded_names)
                external_results['wikipedia'] = wikipedia_data
            except Exception as e:
                logger.warning(f"Wikipedia validation error: {e}")

        # Validate from AstroDatabank (placeholder for future implementation)
        if self.external_sources['astrodatabank']['enabled']:
            try:
                astrodatabank_data = await self._validate_astrodatabank(celebrity_match, expanded_names)
                external_results['astrodatabank'] = astrodatabank_data
            except Exception as e:
                logger.warning(f"AstroDatabank validation error: {e}")

        # Validate from IMDB (placeholder for future implementation)
        if self.external_sources['imdb']['enabled']:
            try:
                imdb_data = await self._validate_imdb(celebrity_match, expanded_names)
                external_results['imdb'] = imdb_data
            except Exception as e:
                logger.warning(f"IMDB validation error: {e}")

        return external_results

    async def _validate_wikipedia(
        self,
        celebrity_match: CelebrityMatch,
        expanded_names: List[str]
    ) -> Dict[str, Any]:
        """
        Validate celebrity data from Wikipedia.

        Args:
            celebrity_match: Celebrity match to validate
            expanded_names: List of name variations

        Returns:
            Dictionary with Wikipedia validation results
        """
        result = {'found': False, 'confidence': 0.0, 'data': {}}

        if not self.session:
            return result

        try:
            # Search for the celebrity on Wikipedia
            for name in expanded_names[:3]:  # Limit to first 3 variations
                search_params = {
                    'action': 'query',
                    'format': 'json',
                    'list': 'search',
                    'srsearch': name,
                    'srlimit': 3
                }

                search_url = self.external_sources['wikipedia']['search_url']
                async with self.session.get(search_url, params=search_params) as response:
                    if response.status == 200:
                        search_data = await response.json()

                        if search_data.get('query', {}).get('search'):
                            # Get the first search result
                            first_result = search_data['query']['search'][0]
                            page_title = first_result['title']

                            # Get page summary
                            summary_url = f"{self.external_sources['wikipedia']['base_url']}/page/summary/{quote(page_title)}"
                            async with self.session.get(summary_url) as summary_response:
                                if summary_response.status == 200:
                                    summary_data = await summary_response.json()

                                    result['found'] = True
                                    result['data'] = {
                                        'title': page_title,
                                        'extract': summary_data.get('extract', ''),
                                        'birth_date': self._extract_birth_date_from_text(
                                            summary_data.get('extract', '')
                                        )
                                    }

                                    # Calculate confidence based on name similarity
                                    name_similarity = SequenceMatcher(
                                        None,
                                        celebrity_match.name.lower(),
                                        page_title.lower()
                                    ).ratio()

                                    result['confidence'] = min(0.9, name_similarity + 0.1)

                                    if result['confidence'] > 0.7:
                                        break

        except Exception as e:
            logger.warning(f"Wikipedia API error: {e}")

        return result

    async def _validate_astrodatabank(
        self,
        celebrity_match: CelebrityMatch,
        expanded_names: List[str]
    ) -> Dict[str, Any]:
        """
        Validate celebrity data from AstroDatabank.

        Note: This is a placeholder implementation. Full implementation would require
        web scraping or API access to AstroDatabank.

        Args:
            celebrity_match: Celebrity match to validate
            expanded_names: List of name variations

        Returns:
            Dictionary with AstroDatabank validation results
        """
        result = {'found': False, 'confidence': 0.0, 'data': {}}

        # Placeholder implementation - would need actual AstroDatabank integration
        logger.info(f"AstroDatabank validation placeholder for: {celebrity_match.name}")

        # For now, return empty result
        # Future implementation would:
        # 1. Search AstroDatabank for celebrity
        # 2. Extract birth data with high precision
        # 3. Validate astrological information
        # 4. Return confidence score based on data quality

        return result

    async def _validate_imdb(
        self,
        celebrity_match: CelebrityMatch,
        expanded_names: List[str]
    ) -> Dict[str, Any]:
        """
        Validate celebrity data from IMDB.

        Note: This is a placeholder implementation. Full implementation would require
        web scraping or API access to IMDB.

        Args:
            celebrity_match: Celebrity match to validate
            expanded_names: List of name variations

        Returns:
            Dictionary with IMDB validation results
        """
        result = {'found': False, 'confidence': 0.0, 'data': {}}

        # Placeholder implementation - would need actual IMDB integration
        logger.info(f"IMDB validation placeholder for: {celebrity_match.name}")

        # For now, return empty result
        # Future implementation would:
        # 1. Search IMDB for celebrity
        # 2. Extract filmography and birth data
        # 3. Validate entertainment industry information
        # 4. Return confidence score based on career data

        return result

    def _extract_birth_date_from_text(self, text: str) -> Optional[str]:
        """
        Extract birth date from text using regex patterns.

        Args:
            text: Text to search for birth date

        Returns:
            Extracted birth date string if found, None otherwise
        """
        if not text:
            return None

        # Common birth date patterns
        patterns = [
            r'born\s+(\w+\s+\d{1,2},\s+\d{4})',  # born January 1, 1990
            r'born\s+(\d{1,2}\s+\w+\s+\d{4})',   # born 1 January 1990
            r'born\s+(\d{4}-\d{2}-\d{2})',       # born 1990-01-01
            r'born\s+(\d{1,2}/\d{1,2}/\d{4})',   # born 1/1/1990
            r'\(born\s+([^)]+)\)',               # (born January 1, 1990)
            r'birth.*?(\w+\s+\d{1,2},\s+\d{4})', # birth: January 1, 1990
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()

        return None

    async def get_data_quality_metrics(self) -> Dict[str, Any]:
        """
        Get data quality monitoring metrics.

        Returns:
            Dictionary with data quality metrics
        """
        try:
            # Get database statistics
            db_stats = await self.celebrity_db.get_database_statistics()

            # Calculate quality metrics
            total_celebrities = db_stats.get('total_celebrities', 0)
            enhanced_celebrities = db_stats.get('enhanced_celebrities', 0)
            verified_celebrities = db_stats.get('verified_celebrities', 0)

            quality_metrics = {
                'total_celebrities': total_celebrities,
                'enhanced_celebrities': enhanced_celebrities,
                'verified_celebrities': verified_celebrities,
                'enhancement_rate': enhanced_celebrities / max(1, total_celebrities),
                'verification_rate': verified_celebrities / max(1, total_celebrities),
                'data_sources': {
                    'database_coverage': 1.0,  # Always available
                    'wikipedia_enabled': self.external_sources['wikipedia']['enabled'],
                    'astrodatabank_enabled': self.external_sources['astrodatabank']['enabled'],
                    'imdb_enabled': self.external_sources['imdb']['enabled']
                },
                'confidence_distribution': await self._get_confidence_distribution(),
                'last_updated': datetime.now().isoformat()
            }

            return quality_metrics

        except Exception as e:
            logger.error(f"Error getting data quality metrics: {e}")
            return {
                'error': str(e),
                'last_updated': datetime.now().isoformat()
            }

    async def _get_confidence_distribution(self) -> Dict[str, int]:
        """
        Get distribution of confidence scores across celebrities.

        Returns:
            Dictionary with confidence score distribution
        """
        try:
            # Get celebrities by confidence ranges
            high_confidence = await self.celebrity_db.get_celebrities_by_confidence_range(0.8, 1.0)
            medium_confidence = await self.celebrity_db.get_celebrities_by_confidence_range(0.5, 0.8)
            low_confidence = await self.celebrity_db.get_celebrities_by_confidence_range(0.0, 0.5)

            return {
                'high_confidence': len(high_confidence),
                'medium_confidence': len(medium_confidence),
                'low_confidence': len(low_confidence)
            }
        except Exception as e:
            logger.warning(f"Error getting confidence distribution: {e}")
            return {
                'high_confidence': 0,
                'medium_confidence': 0,
                'low_confidence': 0
            }
