"""
Entity Resolver - Maps Isolate entities to database entities and stores relationship graphs
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from difflib import SequenceMatcher

from .isolate_models import IsolateResponse, IsolateEntity, IsolateRelationship, IsolateAction, IsolateEndeavor
from ..database.models import (
    CelebrityData, GenericEntity, EntityRelationship, EntityAction, EntityEndeavor,
    ArticleData, DataSource, ConfidenceLevel
)
from ..database.celebrity_db_service import CelebrityDBService
from ..database.article_db_service import ArticleDBService
from ..database.generic_entity_db_service import GenericEntityDBService
from ..database.entity_graph_db_service import EntityGraphDBService

logger = logging.getLogger(__name__)


class EntityResolver:
    """
    Maps Isolate entities to DB entities and stores the resulting graph.
    
    This service handles the crucial logic of mapping entities from the Perplexity response
    to the database, including entity deduplication and relationship storage.
    """
    
    def __init__(
        self,
        celebrity_db_service: CelebrityDBService,
        article_db_service: ArticleDBService,
        generic_entity_db_service: GenericEntityDBService,
        entity_graph_db_service: EntityGraphDBService
    ):
        """
        Initialize EntityResolver.

        Args:
            celebrity_db_service: Celebrity database service
            article_db_service: Article database service
            generic_entity_db_service: Generic entity database service
            entity_graph_db_service: Entity graph database service
        """
        self.celebrity_db = celebrity_db_service
        self.article_db = article_db_service
        self.generic_entity_db = generic_entity_db_service
        self.entity_graph_db = entity_graph_db_service
        
        # Entity caches for deduplication within a single resolution session
        self._celebrity_cache: Dict[str, str] = {}  # name -> id
        self._generic_entity_cache: Dict[str, str] = {}  # name -> id
        
        # Statistics
        self.stats = {
            "entities_resolved": 0,
            "celebrities_created": 0,
            "generic_entities_created": 0,
            "relationships_stored": 0,
            "actions_stored": 0,
            "endeavors_stored": 0
        }
    
    async def resolve_and_store_entities(self, isolate_response: IsolateResponse) -> Dict[str, str]:
        """
        Resolves all entities from an Isolate response, stores them in the DB,
        and returns a map of {isolate_entity_name: db_entity_id}.
        
        Args:
            isolate_response: Isolate response containing entities
            
        Returns:
            Dictionary mapping entity names to database IDs
        """
        entity_map = {}
        
        # Clear caches for this resolution session
        self._celebrity_cache.clear()
        self._generic_entity_cache.clear()
        
        logger.info(f"Resolving {len(isolate_response.entities)} entities from Isolate response")
        
        for entity in isolate_response.entities:
            try:
                entity_id = await self._resolve_single_entity(entity)
                entity_map[entity.name] = entity_id
                self.stats["entities_resolved"] += 1
                
            except Exception as e:
                logger.error(f"Failed to resolve entity '{entity.name}': {e}")
                continue
        
        logger.info(f"Successfully resolved {len(entity_map)} entities")
        return entity_map
    
    async def store_isolate_graph(self, article_id: str, isolate_response: IsolateResponse) -> Dict[str, Any]:
        """
        Resolves all entities, then stores the relationships, actions,
        and endeavors connecting them.
        
        Args:
            article_id: ID of the source article
            isolate_response: Isolate response containing the full graph
            
        Returns:
            Summary of stored data
        """
        try:
            # 1. Get all entities into the DB and get their IDs
            entity_map = await self.resolve_and_store_entities(isolate_response)
            
            # 2. Store relationships
            relationships_stored = await self._store_relationships(
                isolate_response.relationships, entity_map, article_id
            )
            
            # 3. Store actions
            actions_stored = await self._store_actions(
                isolate_response.actions, entity_map, article_id
            )
            
            # 4. Store endeavors
            endeavors_stored = await self._store_endeavors(
                isolate_response.endeavors, entity_map, article_id
            )
            
            # 5. Update the ArticleData record with metadata
            await self._update_article_metadata(article_id, entity_map, isolate_response)
            
            summary = {
                "entities_resolved": len(entity_map),
                "relationships_stored": relationships_stored,
                "actions_stored": actions_stored,
                "endeavors_stored": endeavors_stored,
                "entity_ids": list(entity_map.values())
            }
            
            logger.info(f"Stored Isolate graph for article {article_id}: {summary}")
            return summary
            
        except Exception as e:
            logger.error(f"Failed to store Isolate graph for article {article_id}: {e}")
            raise
    
    async def _resolve_single_entity(self, entity: IsolateEntity) -> str:
        """
        Resolve a single entity to a database ID.
        
        Args:
            entity: Isolate entity to resolve
            
        Returns:
            Database entity ID
        """
        if entity.type == "person":
            return await self._resolve_celebrity(entity)
        else:
            return await self._resolve_generic_entity(entity)
    
    async def _resolve_celebrity(self, entity: IsolateEntity) -> str:
        """
        Resolve a person entity to a CelebrityData record.
        
        Args:
            entity: Person entity from Isolate
            
        Returns:
            Celebrity database ID
        """
        # Check cache first
        if entity.name in self._celebrity_cache:
            return self._celebrity_cache[entity.name]
        
        # Try to find existing celebrity by name (with fuzzy matching)
        existing_celebrity = await self._find_existing_celebrity(entity.name)
        
        if existing_celebrity:
            celebrity_id = existing_celebrity.id
            logger.debug(f"Found existing celebrity: {entity.name} -> {celebrity_id}")
        else:
            # Create new celebrity
            celebrity_data = self._create_celebrity_from_entity(entity)
            celebrity_id = await self.celebrity_db.create_celebrity(celebrity_data)
            self.stats["celebrities_created"] += 1
            logger.debug(f"Created new celebrity: {entity.name} -> {celebrity_id}")
        
        # Cache the result
        self._celebrity_cache[entity.name] = celebrity_id
        return celebrity_id
    
    async def _resolve_generic_entity(self, entity: IsolateEntity) -> str:
        """
        Resolve a non-person entity to a GenericEntity record.
        
        Args:
            entity: Non-person entity from Isolate
            
        Returns:
            Generic entity database ID
        """
        # Check cache first
        cache_key = f"{entity.type}:{entity.name}"
        if cache_key in self._generic_entity_cache:
            return self._generic_entity_cache[cache_key]
        
        # Try to find existing entity by name and type
        existing_entity = await self._find_existing_generic_entity(entity.name, entity.type)
        
        if existing_entity:
            entity_id = existing_entity.id
            logger.debug(f"Found existing generic entity: {entity.name} ({entity.type}) -> {entity_id}")
        else:
            # Create new generic entity
            generic_entity = self._create_generic_entity_from_isolate(entity)
            # Note: We'll need to implement create_generic_entity in the database service
            entity_id = await self._create_generic_entity(generic_entity)
            self.stats["generic_entities_created"] += 1
            logger.debug(f"Created new generic entity: {entity.name} ({entity.type}) -> {entity_id}")
        
        # Cache the result
        self._generic_entity_cache[cache_key] = entity_id
        return entity_id
    
    async def _find_existing_celebrity(self, name: str) -> Optional[CelebrityData]:
        """Find existing celebrity with fuzzy name matching."""
        # First try exact match
        celebrities = await self.celebrity_db.search_celebrities(name, limit=10)
        
        if not celebrities:
            return None
        
        # Check for exact matches first
        for celebrity in celebrities:
            if celebrity.name.lower() == name.lower():
                return celebrity
            
            # Check aliases
            for alias in celebrity.aliases:
                if alias.lower() == name.lower():
                    return celebrity
        
        # If no exact match, try fuzzy matching
        best_match = None
        best_score = 0.0
        
        for celebrity in celebrities:
            # Compare with main name
            score = SequenceMatcher(None, name.lower(), celebrity.name.lower()).ratio()
            if score > best_score and score >= 0.85:  # 85% similarity threshold
                best_score = score
                best_match = celebrity
            
            # Compare with aliases
            for alias in celebrity.aliases:
                score = SequenceMatcher(None, name.lower(), alias.lower()).ratio()
                if score > best_score and score >= 0.85:
                    best_score = score
                    best_match = celebrity
        
        return best_match
    
    async def _find_existing_generic_entity(self, name: str, entity_type: str) -> Optional[GenericEntity]:
        """Find existing generic entity by name and type."""
        return await self.generic_entity_db.find_by_name_and_type(name, entity_type)
    
    def _create_celebrity_from_entity(self, entity: IsolateEntity) -> CelebrityData:
        """Create CelebrityData from IsolateEntity."""
        return CelebrityData(
            name=entity.name,
            birth_date=self._parse_date(entity.inception_date) if entity.inception_date else None,
            birth_time=entity.inception_time,
            birth_location=entity.inception_location,
            confidence_score=0.7,  # Default confidence for Isolate-derived data
            data_sources=[DataSource.LLM_ENHANCED],
            enhanced=True,
            verified=False,
            entity_type="person",
            roles=entity.roles,
            aliases=[]
        )
    
    def _create_generic_entity_from_isolate(self, entity: IsolateEntity) -> GenericEntity:
        """Create GenericEntity from IsolateEntity."""
        return GenericEntity(
            name=entity.name,
            entity_type=entity.type,
            subtype=entity.subtype,
            roles=entity.roles,
            inception_date=self._parse_date(entity.inception_date) if entity.inception_date else None,
            inception_time=entity.inception_time,
            inception_location=entity.inception_location,
            estimated_inception=entity.estimated_inception,
            relevance_reason=entity.relevance_reason,
            primary=entity.primary,
            confidence_score=0.7,
            data_sources=[DataSource.LLM_ENHANCED]
        )

    async def _create_generic_entity(self, entity: GenericEntity) -> str:
        """Create a generic entity in the database."""
        return await self.generic_entity_db.create_entity(entity)

    async def _store_relationships(
        self,
        relationships: List[IsolateRelationship],
        entity_map: Dict[str, str],
        article_id: str
    ) -> int:
        """Store entity relationships."""
        stored_count = 0

        for relationship in relationships:
            try:
                # Get entity IDs
                entity1_id = entity_map.get(relationship.entity1)
                entity2_id = entity_map.get(relationship.entity2)

                if not entity1_id or not entity2_id:
                    logger.warning(f"Skipping relationship - missing entity IDs: {relationship.entity1} -> {relationship.entity2}")
                    continue

                # Create relationship record
                relationship_record = EntityRelationship(
                    entity1_id=entity1_id,
                    entity2_id=entity2_id,
                    entity1_name=relationship.entity1,
                    entity2_name=relationship.entity2,
                    relation_type=relationship.relation_type,
                    both_primary=relationship.both_primary,
                    inception_date=self._parse_date(relationship.inception_date) if relationship.inception_date else None,
                    inception_time=relationship.inception_time,
                    inception_location=relationship.inception_location,
                    estimated_inception=relationship.estimated_inception,
                    article_id=article_id
                )

                # Store relationship (would need database service implementation)
                await self._store_relationship_record(relationship_record)
                stored_count += 1

            except Exception as e:
                logger.error(f"Failed to store relationship {relationship.entity1} -> {relationship.entity2}: {e}")
                continue

        self.stats["relationships_stored"] += stored_count
        return stored_count

    async def _store_actions(
        self,
        actions: List[IsolateAction],
        entity_map: Dict[str, str],
        article_id: str
    ) -> int:
        """Store entity actions."""
        stored_count = 0

        for action in actions:
            try:
                # Get entity IDs
                actor_id = entity_map.get(action.entity1)
                target_id = entity_map.get(action.entity2)

                if not actor_id or not target_id:
                    logger.warning(f"Skipping action - missing entity IDs: {action.entity1} -> {action.entity2}")
                    continue

                # Create action record
                action_record = EntityAction(
                    actor_id=actor_id,
                    target_id=target_id,
                    actor_name=action.entity1,
                    target_name=action.entity2,
                    action_type=action.action_type,
                    both_primary=action.both_primary,
                    inception_date=self._parse_date(action.inception_date),
                    inception_time=action.inception_time,
                    inception_location=action.inception_location,
                    estimated_inception=action.estimated_inception,
                    article_id=article_id
                )

                # Store action (would need database service implementation)
                await self._store_action_record(action_record)
                stored_count += 1

            except Exception as e:
                logger.error(f"Failed to store action {action.entity1} -> {action.entity2}: {e}")
                continue

        self.stats["actions_stored"] += stored_count
        return stored_count

    async def _store_endeavors(
        self,
        endeavors: List[IsolateEndeavor],
        entity_map: Dict[str, str],
        article_id: str
    ) -> int:
        """Store entity endeavors."""
        stored_count = 0

        for endeavor in endeavors:
            try:
                # Get entity ID
                entity_id = entity_map.get(endeavor.entity)

                if not entity_id:
                    logger.warning(f"Skipping endeavor - missing entity ID: {endeavor.entity}")
                    continue

                # Create endeavor record
                endeavor_record = EntityEndeavor(
                    entity_id=entity_id,
                    entity_name=endeavor.entity,
                    endeavor_type=endeavor.endeavor_type,
                    endeavor_label=endeavor.endeavor_label,
                    primary=endeavor.primary,
                    inception_date=self._parse_date(endeavor.inception_date) if endeavor.inception_date else None,
                    inception_time=endeavor.inception_time,
                    inception_location=endeavor.inception_location,
                    estimated_inception=endeavor.estimated_inception,
                    article_id=article_id
                )

                # Store endeavor (would need database service implementation)
                await self._store_endeavor_record(endeavor_record)
                stored_count += 1

            except Exception as e:
                logger.error(f"Failed to store endeavor for {endeavor.entity}: {e}")
                continue

        self.stats["endeavors_stored"] += stored_count
        return stored_count

    async def _update_article_metadata(
        self,
        article_id: str,
        entity_map: Dict[str, str],
        isolate_response: IsolateResponse
    ):
        """Update article with Isolate processing metadata."""
        try:
            # Calculate confidence based on data completeness
            total_items = len(isolate_response.entities) + len(isolate_response.relationships) + len(isolate_response.actions) + len(isolate_response.endeavors)
            confidence = min(1.0, 0.7 + (total_items * 0.05))  # Base 0.7, +0.05 per item

            # Update article
            update_data = {
                "isolate_processed_at": datetime.utcnow(),
                "isolate_entity_ids": list(entity_map.values()),
                "isolate_confidence": confidence
            }

            await self.article_db.update_article(article_id, update_data)
            logger.info(f"Updated article {article_id} with Isolate metadata")

        except Exception as e:
            logger.error(f"Failed to update article metadata for {article_id}: {e}")

    # Helper methods for database operations (would be implemented with proper DB services)

    async def _store_relationship_record(self, relationship: EntityRelationship):
        """Store relationship record in database."""
        await self.entity_graph_db.create_relationship(relationship)

    async def _store_action_record(self, action: EntityAction):
        """Store action record in database."""
        await self.entity_graph_db.create_action(action)

    async def _store_endeavor_record(self, endeavor: EntityEndeavor):
        """Store endeavor record in database."""
        await self.entity_graph_db.create_endeavor(endeavor)

    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """Parse date string to datetime object."""
        if not date_str:
            return None

        try:
            # Handle YYYY-MM-DD format
            return datetime.strptime(date_str, "%Y-%m-%d")
        except ValueError:
            try:
                # Handle other common formats
                return datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                logger.warning(f"Could not parse date: {date_str}")
                return None

    def get_stats(self) -> Dict[str, Any]:
        """Get resolution statistics."""
        return self.stats.copy()
