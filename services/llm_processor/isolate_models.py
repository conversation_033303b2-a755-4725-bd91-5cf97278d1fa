"""
Isolate Models - Pydantic models for Perplexity API entity extraction
"""

from datetime import datetime
from typing import List, Optional, Literal
from pydantic import BaseModel, Field, ConfigDict


class IsolateEntity(BaseModel):
    """Entity extracted from article text"""
    
    name: str = Field(..., description="Entity name")
    type: Literal["person", "organization", "object", "event", "endeavor", "ship", "location"] = Field(
        ..., description="Entity type"
    )
    subtype: Optional[str] = Field(None, description="Entity subtype")
    roles: List[str] = Field(default_factory=list, description="Entity roles")
    primary: bool = Field(..., description="Whether entity is primary")
    inception_date: Optional[str] = Field(None, description="Inception date (YYYY-MM-DD)")
    inception_time: Optional[str] = Field(None, description="Inception time (HH:MM)")
    inception_location: Optional[str] = Field(None, description="Inception location")
    estimated_inception: bool = Field(default=False, description="Whether inception data is estimated")
    relevance_reason: str = Field(..., description="Reason for entity relevance")
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class IsolateRelationship(BaseModel):
    """Relationship between entities"""
    
    entity1: str = Field(..., description="First entity name")
    entity2: str = Field(..., description="Second entity name")
    relation_type: str = Field(..., description="Type of relationship")
    both_primary: bool = Field(..., description="Whether both entities are primary")
    inception_date: Optional[str] = Field(None, description="Relationship inception date (YYYY-MM-DD)")
    inception_time: Optional[str] = Field(None, description="Relationship inception time (HH:MM)")
    inception_location: Optional[str] = Field(None, description="Relationship inception location")
    estimated_inception: bool = Field(default=False, description="Whether inception data is estimated")
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class IsolateAction(BaseModel):
    """Action between entities"""
    
    entity1: str = Field(..., description="Actor entity name")
    entity2: str = Field(..., description="Target entity name")
    action_type: str = Field(..., description="Type of action")
    both_primary: bool = Field(..., description="Whether both entities are primary")
    inception_date: Optional[str] = Field(None, description="Action date (YYYY-MM-DD)")
    inception_time: Optional[str] = Field(None, description="Action time (HH:MM)")
    inception_location: Optional[str] = Field(None, description="Action location")
    estimated_inception: bool = Field(default=False, description="Whether inception data is estimated")
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class IsolateEndeavor(BaseModel):
    """Endeavor associated with entity"""
    
    entity: str = Field(..., description="Entity name")
    endeavor_type: str = Field(..., description="Type of endeavor")
    endeavor_label: str = Field(..., description="Endeavor label/name")
    primary: bool = Field(..., description="Whether entity is primary")
    inception_date: Optional[str] = Field(None, description="Endeavor inception date (YYYY-MM-DD)")
    inception_time: Optional[str] = Field(None, description="Endeavor inception time (HH:MM)")
    inception_location: Optional[str] = Field(None, description="Endeavor inception location")
    estimated_inception: bool = Field(default=False, description="Whether inception data is estimated")
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class IsolateResponse(BaseModel):
    """Complete Isolate extraction response"""
    
    source: str = Field(..., description="Source of the article")
    entities: List[IsolateEntity] = Field(default_factory=list, description="Extracted entities")
    relationships: List[IsolateRelationship] = Field(default_factory=list, description="Entity relationships")
    actions: List[IsolateAction] = Field(default_factory=list, description="Entity actions")
    endeavors: List[IsolateEndeavor] = Field(default_factory=list, description="Entity endeavors")
    notes: List[str] = Field(default_factory=list, description="Additional notes")
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class IsolateExtractionRequest(BaseModel):
    """Request for Isolate entity extraction"""
    
    article_text: str = Field(..., description="Article text to process")
    article_title: Optional[str] = Field(None, description="Article title")
    article_url: Optional[str] = Field(None, description="Article URL")
    source: Optional[str] = Field(None, description="Article source")
    
    # Processing options
    temperature: float = Field(default=0.1, ge=0.0, le=2.0, description="Model temperature")
    max_tokens: int = Field(default=4000, ge=1, description="Maximum tokens")
    timeout: int = Field(default=60, ge=5, description="Request timeout in seconds")
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class IsolateExtractionResponse(BaseModel):
    """Response from Isolate entity extraction"""
    
    # Extracted data
    isolate_data: IsolateResponse = Field(..., description="Extracted Isolate data")
    
    # Processing metadata
    processing_time_ms: float = Field(..., ge=0.0, description="Processing time in milliseconds")
    tokens_used: Optional[int] = Field(None, ge=0, description="Tokens used")
    cost_estimate: Optional[float] = Field(None, ge=0.0, description="Cost estimate")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Extraction confidence")
    
    # Quality metrics
    entities_extracted: int = Field(default=0, ge=0, description="Number of entities extracted")
    relationships_extracted: int = Field(default=0, ge=0, description="Number of relationships extracted")
    actions_extracted: int = Field(default=0, ge=0, description="Number of actions extracted")
    endeavors_extracted: int = Field(default=0, ge=0, description="Number of endeavors extracted")
    
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Response creation time")
    
    def __init__(self, **data):
        super().__init__(**data)
        # Auto-calculate counts
        if 'isolate_data' in data:
            isolate_data = data['isolate_data']
            if isinstance(isolate_data, dict):
                self.entities_extracted = len(isolate_data.get('entities', []))
                self.relationships_extracted = len(isolate_data.get('relationships', []))
                self.actions_extracted = len(isolate_data.get('actions', []))
                self.endeavors_extracted = len(isolate_data.get('endeavors', []))
            elif hasattr(isolate_data, 'entities'):
                self.entities_extracted = len(isolate_data.entities)
                self.relationships_extracted = len(isolate_data.relationships)
                self.actions_extracted = len(isolate_data.actions)
                self.endeavors_extracted = len(isolate_data.endeavors)
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )
