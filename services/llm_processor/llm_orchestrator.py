"""
LLM Orchestrator - Multi-model LLM coordination for Star Power
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List

from .models import (
    LLMConfig, LLMProvider, LLMRequest, LLMResponse, LLMTaskType,
    CelebrityIdentificationRequest, CelebrityIdentificationResponse,
    LLMError, LLMTimeoutError, LLMQuotaError
)
from .ollama_client import OllamaClient
from .openai_client import OpenAIClient
from .perplexity_client import PerplexityClient
from .response_processor import ResponseProcessor
from .quality_assessor import ResponseQualityAssessor
from .isolate_models import IsolateExtractionRequest, IsolateExtractionResponse

logger = logging.getLogger(__name__)


class LLMOrchestrator:
    """
    Multi-model LLM orchestrator with intelligent fallback.
    
    Coordinates between Ollama (primary) and OpenAI (fallback) models,
    with quality assessment and response processing.
    """
    
    def __init__(self, config: Optional[LLMConfig] = None):
        """
        Initialize LLM orchestrator.
        
        Args:
            config: LLM configuration (uses defaults if None)
        """
        self.config = config or LLMConfig()
        
        # Initialize clients
        self.clients = {}
        
        # Primary client (Ollama)
        if self.config.primary_provider == LLMProvider.OLLAMA:
            self.clients[LLMProvider.OLLAMA] = OllamaClient(
                base_url=self.config.primary_base_url,
                model=self.config.primary_model,
                timeout=self.config.timeout_seconds,
                max_retries=self.config.max_retries
            )
        
        # Fallback client (OpenAI)
        if self.config.fallback_provider == LLMProvider.OPENAI:
            self.clients[LLMProvider.OPENAI] = OpenAIClient(
                api_key=self.config.fallback_api_key,
                model=self.config.fallback_model,
                timeout=self.config.timeout_seconds,
                max_retries=self.config.max_retries
            )

        # Perplexity client (for Isolate extraction)
        try:
            self.clients[LLMProvider.PERPLEXITY] = PerplexityClient(
                model="sonar-pro",
                timeout=self.config.timeout_seconds,
                max_retries=self.config.max_retries
            )
        except ValueError as e:
            logger.warning(f"Perplexity client not initialized: {e}")
            # Continue without Perplexity if API key is missing
        
        # Initialize processors
        self.response_processor = ResponseProcessor()
        self.quality_assessor = ResponseQualityAssessor()
        
        # Statistics
        self.stats = {
            "requests_processed": 0,
            "primary_successes": 0,
            "fallback_uses": 0,
            "quality_failures": 0,
            "total_processing_time": 0.0
        }
        
        logger.info(f"Initialized LLMOrchestrator with {len(self.clients)} clients")
    
    async def process_request(self, request: LLMRequest) -> LLMResponse:
        """
        Process LLM request with intelligent fallback.
        
        Args:
            request: LLM request to process
            
        Returns:
            Processed LLM response
            
        Raises:
            LLMError: If all processing attempts fail
        """
        start_time = time.time()
        self.stats["requests_processed"] += 1
        
        try:
            # Determine processing order
            providers = self._get_processing_order(request)
            
            last_error = None
            
            for provider in providers:
                if provider not in self.clients:
                    logger.warning(f"Client for {provider} not available")
                    continue
                
                try:
                    # Process with specific provider
                    response = await self._process_with_provider(request, provider)
                    
                    # Process and assess response
                    processed_response = self.response_processor.process_response(response)
                    quality_score = self.quality_assessor.assess_quality(processed_response)
                    
                    # Update quality score in response
                    processed_response.quality_score = quality_score
                    
                    # Check if quality meets threshold
                    if quality_score >= self.config.quality_threshold:
                        if provider == self.config.primary_provider:
                            self.stats["primary_successes"] += 1
                        else:
                            self.stats["fallback_uses"] += 1
                        
                        processing_time = time.time() - start_time
                        self.stats["total_processing_time"] += processing_time
                        
                        logger.info(f"Request processed successfully with {provider}, quality: {quality_score:.3f}")
                        return processed_response
                    else:
                        self.stats["quality_failures"] += 1
                        logger.warning(f"Quality threshold not met for {provider}: {quality_score:.3f} < {self.config.quality_threshold}")
                        
                        # Continue to next provider if quality is insufficient
                        last_error = LLMError(f"Quality threshold not met: {quality_score:.3f}")
                        continue
                
                except (LLMTimeoutError, LLMQuotaError) as e:
                    logger.warning(f"Provider {provider} failed with {type(e).__name__}: {e}")
                    last_error = e
                    continue
                except LLMError as e:
                    logger.error(f"Provider {provider} failed: {e}")
                    last_error = e
                    continue
            
            # All providers failed
            processing_time = time.time() - start_time
            self.stats["total_processing_time"] += processing_time
            
            if last_error:
                raise last_error
            else:
                raise LLMError("All LLM providers failed or unavailable")
        
        except Exception as e:
            processing_time = time.time() - start_time
            self.stats["total_processing_time"] += processing_time
            
            if isinstance(e, LLMError):
                raise
            else:
                raise LLMError(f"LLM orchestration failed: {e}")
    
    async def identify_celebrities(
        self,
        article_title: str,
        article_content: str,
        article_url: Optional[str] = None,
        known_entities: Optional[List[str]] = None,
        context_hints: Optional[List[str]] = None
    ) -> CelebrityIdentificationResponse:
        """
        Identify celebrities in article content.
        
        Args:
            article_title: Article title
            article_content: Article content
            article_url: Article URL (optional)
            known_entities: Pre-identified entities (optional)
            context_hints: Context hints (optional)
            
        Returns:
            Celebrity identification response
            
        Raises:
            LLMError: If celebrity identification fails
        """
        request = CelebrityIdentificationRequest(
            article_title=article_title,
            article_content=article_content,
            article_url=article_url,
            known_entities=known_entities or [],
            context_hints=context_hints or []
        )
        
        response = await self.process_request(request)
        
        if isinstance(response, CelebrityIdentificationResponse):
            return response
        else:
            # Convert generic response to celebrity response
            return CelebrityIdentificationResponse(
                raw_response=response.raw_response,
                parsed_content=response.parsed_content,
                extracted_data=response.extracted_data,
                provider=response.provider,
                model=response.model,
                task_type=response.task_type,
                confidence_score=response.confidence_score,
                quality_score=response.quality_score,
                processing_time_ms=response.processing_time_ms,
                tokens_used=response.tokens_used,
                cost_estimate=response.cost_estimate,
                created_at=response.created_at,
                celebrities=[],
                article_title=article_title
            )
    
    def _get_processing_order(self, request: LLMRequest) -> List[LLMProvider]:
        """
        Determine provider processing order.
        
        Args:
            request: LLM request
            
        Returns:
            List of providers in processing order
        """
        # Check for preferred provider
        if request.preferred_provider and request.preferred_provider in self.clients:
            providers = [request.preferred_provider]
            # Add other providers as fallback
            for provider in [self.config.primary_provider, self.config.fallback_provider]:
                if provider != request.preferred_provider and provider in self.clients:
                    providers.append(provider)
            return providers
        
        # Default order: primary then fallback
        providers = []
        if self.config.primary_provider in self.clients:
            providers.append(self.config.primary_provider)
        if self.config.fallback_provider in self.clients and self.config.fallback_provider != self.config.primary_provider:
            providers.append(self.config.fallback_provider)
        
        return providers
    
    async def _process_with_provider(self, request: LLMRequest, provider: LLMProvider) -> LLMResponse:
        """
        Process request with specific provider.
        
        Args:
            request: LLM request
            provider: Provider to use
            
        Returns:
            LLM response
            
        Raises:
            LLMError: If processing fails
        """
        client = self.clients[provider]
        
        try:
            response = await client.process_request(request)
            return response
        except Exception as e:
            logger.error(f"Provider {provider} processing failed: {e}")
            raise

    async def extract_isolate_entities(self, request: IsolateExtractionRequest) -> IsolateExtractionResponse:
        """
        Extract entities using Perplexity Isolate processing.

        Args:
            request: Isolate extraction request

        Returns:
            Isolate extraction response

        Raises:
            LLMError: If Perplexity client is not available or extraction fails
        """
        start_time = time.time()
        self.stats["requests_processed"] += 1

        try:
            # Check if Perplexity client is available
            if LLMProvider.PERPLEXITY not in self.clients:
                raise LLMError("Perplexity client not available for Isolate extraction", LLMProvider.PERPLEXITY)

            perplexity_client = self.clients[LLMProvider.PERPLEXITY]

            # Extract entities using Perplexity
            response = await perplexity_client.extract_entities(request)

            # Update statistics
            processing_time = time.time() - start_time
            self.stats["total_processing_time"] += processing_time
            self.stats["primary_successes"] += 1

            logger.info(f"Isolate extraction completed in {processing_time:.2f}s")
            logger.info(f"Extracted: {response.entities_extracted} entities, {response.relationships_extracted} relationships, "
                       f"{response.actions_extracted} actions, {response.endeavors_extracted} endeavors")

            return response

        except Exception as e:
            self.stats["quality_failures"] += 1
            logger.error(f"Isolate extraction failed: {str(e)}")
            raise LLMError(f"Isolate extraction failed: {str(e)}", LLMProvider.PERPLEXITY)

    async def health_check(self) -> Dict[str, Any]:
        """
        Check health of all LLM clients.
        
        Returns:
            Health status for all clients
        """
        health_status = {
            "orchestrator": {
                "status": "healthy",
                "config": {
                    "primary_provider": self.config.primary_provider.value,
                    "fallback_provider": self.config.fallback_provider.value,
                    "quality_threshold": self.config.quality_threshold
                },
                "stats": self.stats.copy()
            },
            "clients": {}
        }
        
        # Check each client
        for provider, client in self.clients.items():
            try:
                client_health = await client.health_check()
                health_status["clients"][provider.value] = client_health
            except Exception as e:
                health_status["clients"][provider.value] = {
                    "status": "error",
                    "error": str(e)
                }
        
        return health_status
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get processing statistics.
        
        Returns:
            Processing statistics
        """
        stats = self.stats.copy()
        
        if stats["requests_processed"] > 0:
            stats["average_processing_time"] = stats["total_processing_time"] / stats["requests_processed"]
            stats["primary_success_rate"] = stats["primary_successes"] / stats["requests_processed"]
            stats["fallback_rate"] = stats["fallback_uses"] / stats["requests_processed"]
            stats["quality_failure_rate"] = stats["quality_failures"] / stats["requests_processed"]
        
        return stats
