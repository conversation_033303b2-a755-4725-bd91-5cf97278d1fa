"""
LLM Processor Models - Data models for LLM processing
"""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, ConfigDict


class LLMProvider(str, Enum):
    """LLM provider types"""
    OLLAMA = "ollama"
    OPENAI = "openai"
    PERPLEXITY = "perplexity"
    LOCAL = "local"


class LLMTaskType(str, Enum):
    """Types of LLM tasks"""
    CELEBRITY_IDENTIFICATION = "celebrity_identification"
    CONTENT_ANALYSIS = "content_analysis"
    ISOLATE_EXTRACTION = "isolate_extraction"
    DATA_ENHANCEMENT = "data_enhancement"
    VALIDATION = "validation"


class LLMConfig(BaseModel):
    """LLM configuration settings"""
    
    # Primary model configuration
    primary_provider: LLMProvider = LLMProvider.OLLAMA
    primary_model: str = "dolphin-mixtral"
    primary_base_url: Optional[str] = "http://localhost:11434"
    
    # Fallback model configuration
    fallback_provider: LLMProvider = LLMProvider.OPENAI
    fallback_model: str = "gpt-4"
    fallback_api_key: Optional[str] = None
    
    # Quality assessment
    quality_threshold: float = Field(default=0.7, ge=0.0, le=1.0)
    max_retries: int = Field(default=3, ge=1)
    timeout_seconds: int = Field(default=30, ge=5)
    
    # Response processing
    enable_thinking_tags: bool = True
    enable_json_extraction: bool = True
    enable_structured_parsing: bool = True
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class LLMRequest(BaseModel):
    """LLM processing request"""
    
    text: str = Field(..., description="Input text to process")
    task_type: LLMTaskType = Field(..., description="Type of LLM task")
    context: Optional[str] = Field(None, description="Additional context")
    parameters: Dict[str, Any] = Field(default_factory=dict)
    
    # Processing options
    preferred_provider: Optional[LLMProvider] = None
    max_tokens: Optional[int] = Field(None, ge=1)
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0)
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class LLMResponse(BaseModel):
    """LLM processing response"""
    
    # Response content
    raw_response: str = Field(..., description="Raw LLM response")
    parsed_content: Optional[Dict[str, Any]] = Field(None, description="Parsed structured content")
    extracted_data: Optional[Dict[str, Any]] = Field(None, description="Extracted data")
    
    # Metadata
    provider: LLMProvider = Field(..., description="Provider used")
    model: str = Field(..., description="Model used")
    task_type: LLMTaskType = Field(..., description="Task type")
    
    # Quality metrics
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Response confidence")
    quality_score: float = Field(..., ge=0.0, le=1.0, description="Response quality")
    processing_time_ms: float = Field(..., ge=0.0, description="Processing time in milliseconds")
    
    # Processing metadata
    tokens_used: Optional[int] = Field(None, ge=0)
    cost_estimate: Optional[float] = Field(None, ge=0.0)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class CelebrityIdentificationRequest(LLMRequest):
    """Celebrity identification specific request"""
    
    article_title: str = Field(..., description="Article title")
    article_content: str = Field(..., description="Article content")
    article_url: Optional[str] = Field(None, description="Article URL")
    
    # Processing hints
    known_entities: List[str] = Field(default_factory=list, description="Pre-identified entities")
    context_hints: List[str] = Field(default_factory=list, description="Context hints")
    
    def __init__(self, **data):
        # Set text from article content if not provided
        if 'text' not in data and 'article_content' in data:
            data['text'] = data['article_content']
        if 'task_type' not in data:
            data['task_type'] = LLMTaskType.CELEBRITY_IDENTIFICATION
        super().__init__(**data)


class CelebrityMatch(BaseModel):
    """Celebrity identification match"""
    
    name: str = Field(..., description="Celebrity name")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Match confidence")
    context: str = Field(..., description="Context where celebrity was mentioned")
    
    # Additional data
    aliases: List[str] = Field(default_factory=list, description="Known aliases")
    birth_date: Optional[str] = Field(None, description="Birth date if known")
    birth_location: Optional[str] = Field(None, description="Birth location if known")
    profession: Optional[str] = Field(None, description="Profession/occupation")
    
    # Extraction metadata
    mention_start: Optional[int] = Field(None, description="Start position in text")
    mention_end: Optional[int] = Field(None, description="End position in text")
    reasoning: Optional[str] = Field(None, description="LLM reasoning for match")
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )


class CelebrityIdentificationResponse(LLMResponse):
    """Celebrity identification specific response"""
    
    celebrities: List[CelebrityMatch] = Field(default_factory=list, description="Identified celebrities")
    total_matches: int = Field(default=0, ge=0, description="Total number of matches")
    high_confidence_matches: int = Field(default=0, ge=0, description="High confidence matches")
    
    # Analysis metadata
    article_title: str = Field(..., description="Processed article title")
    article_summary: Optional[str] = Field(None, description="Article summary")
    key_topics: List[str] = Field(default_factory=list, description="Key topics identified")
    
    def __init__(self, **data):
        super().__init__(**data)
        # Auto-calculate counts
        if 'celebrities' in data:
            self.total_matches = len(data['celebrities'])
            self.high_confidence_matches = len([c for c in data['celebrities'] if c.confidence >= 0.8])


class LLMError(Exception):
    """Base LLM processing error"""
    
    def __init__(self, message: str, provider: Optional[LLMProvider] = None, model: Optional[str] = None):
        super().__init__(message)
        self.provider = provider
        self.model = model


class LLMTimeoutError(LLMError):
    """LLM processing timeout error"""
    pass


class LLMQuotaError(LLMError):
    """LLM quota/rate limit error"""
    pass


class LLMValidationError(LLMError):
    """LLM response validation error"""
    pass
