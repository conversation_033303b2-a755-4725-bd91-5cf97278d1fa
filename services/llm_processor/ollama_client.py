"""
Ollama Client - Local LLM integration for Star Power
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional

import ollama
from ollama import AsyncClient

from .models import (
    LLMProvider, LLMRequest, LLMResponse, LLMTaskType,
    LLMError, LLMTimeoutError, LLMQuotaError
)

logger = logging.getLogger(__name__)


class OllamaClient:
    """
    Ollama local LLM client for celebrity identification and content analysis.
    
    Provides async interface to local Ollama models with error handling,
    timeout management, and response quality assessment.
    """
    
    def __init__(
        self,
        base_url: str = "http://localhost:11434",
        model: str = "dolphin-mixtral",
        timeout: int = 30,
        max_retries: int = 3
    ):
        """
        Initialize Ollama client.
        
        Args:
            base_url: Ollama server URL
            model: Model name to use
            timeout: Request timeout in seconds
            max_retries: Maximum retry attempts
        """
        self.base_url = base_url
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        
        # Initialize async client
        self.client = AsyncClient(host=base_url)
        
        # Model availability cache
        self._model_available = None
        self._last_check = 0
        self._check_interval = 300  # 5 minutes
        
        logger.info(f"Initialized Ollama client: {base_url}, model: {model}")
    
    async def _check_model_availability(self) -> bool:
        """
        Check if the specified model is available.
        
        Returns:
            True if model is available, False otherwise
        """
        current_time = time.time()
        
        # Use cached result if recent
        if (self._model_available is not None and 
            current_time - self._last_check < self._check_interval):
            return self._model_available
        
        try:
            # List available models
            models = await self.client.list()
            available_models = [model['name'] for model in models.get('models', [])]
            
            self._model_available = self.model in available_models
            self._last_check = current_time
            
            if not self._model_available:
                logger.warning(f"Model {self.model} not available. Available: {available_models}")
            
            return self._model_available
            
        except Exception as e:
            logger.error(f"Failed to check model availability: {e}")
            self._model_available = False
            return False
    
    async def _ensure_model_available(self) -> None:
        """
        Ensure the model is available, pull if necessary.
        
        Raises:
            LLMError: If model cannot be made available
        """
        if await self._check_model_availability():
            return
        
        try:
            logger.info(f"Pulling model {self.model}...")
            await self.client.pull(self.model)
            
            # Verify model is now available
            if not await self._check_model_availability():
                raise LLMError(f"Failed to pull model {self.model}", LLMProvider.OLLAMA, self.model)
                
            logger.info(f"Successfully pulled model {self.model}")
            
        except Exception as e:
            raise LLMError(f"Failed to ensure model availability: {e}", LLMProvider.OLLAMA, self.model)
    
    def _build_prompt(self, request: LLMRequest) -> str:
        """
        Build prompt for the specific task type.
        
        Args:
            request: LLM request
            
        Returns:
            Formatted prompt string
        """
        if request.task_type == LLMTaskType.CELEBRITY_IDENTIFICATION:
            return self._build_celebrity_identification_prompt(request)
        elif request.task_type == LLMTaskType.CONTENT_ANALYSIS:
            return self._build_content_analysis_prompt(request)
        else:
            return self._build_generic_prompt(request)
    
    def _build_celebrity_identification_prompt(self, request: LLMRequest) -> str:
        """Build celebrity identification prompt."""
        prompt = """<think>
I need to identify celebrities mentioned in this news article. I should look for:
1. Full names of famous people (actors, musicians, politicians, athletes, etc.)
2. Context clues that indicate celebrity status
3. Avoid common names without clear celebrity context
4. Consider aliases and stage names
</think>

Please analyze the following news article and identify any celebrities mentioned. For each celebrity found, provide:
- Full name
- Confidence level (0.0-1.0)
- Context where they were mentioned
- Any aliases or alternative names
- Birth date if known
- Profession/occupation

Article Title: {title}

Article Content:
{content}

Please respond in JSON format:
{{
    "celebrities": [
        {{
            "name": "Celebrity Name",
            "confidence": 0.95,
            "context": "context where mentioned",
            "aliases": ["alias1", "alias2"],
            "birth_date": "YYYY-MM-DD or null",
            "profession": "actor/musician/etc"
        }}
    ],
    "summary": "Brief article summary",
    "key_topics": ["topic1", "topic2"]
}}"""
        
        # Extract title and content from request
        title = getattr(request, 'article_title', 'Unknown')
        content = request.text
        
        return prompt.format(title=title, content=content)
    
    def _build_content_analysis_prompt(self, request: LLMRequest) -> str:
        """Build content analysis prompt."""
        prompt = """Please analyze the following content and provide insights:

Content:
{content}

Context: {context}

Please provide analysis including key themes, sentiment, and notable entities."""
        
        return prompt.format(
            content=request.text,
            context=request.context or "General analysis"
        )
    
    def _build_generic_prompt(self, request: LLMRequest) -> str:
        """Build generic prompt."""
        prompt = f"Please process the following text:\n\n{request.text}"
        if request.context:
            prompt += f"\n\nContext: {request.context}"
        return prompt
    
    async def process_request(self, request: LLMRequest) -> LLMResponse:
        """
        Process LLM request with Ollama.
        
        Args:
            request: LLM request to process
            
        Returns:
            LLM response with results
            
        Raises:
            LLMError: If processing fails
            LLMTimeoutError: If request times out
        """
        start_time = time.time()
        
        try:
            # Ensure model is available
            await self._ensure_model_available()
            
            # Build prompt
            prompt = self._build_prompt(request)
            
            # Prepare request parameters
            params = {
                'model': self.model,
                'prompt': prompt,
                'stream': False,
                'options': {}
            }
            
            # Add optional parameters
            if request.max_tokens:
                params['options']['num_predict'] = request.max_tokens
            if request.temperature is not None:
                params['options']['temperature'] = request.temperature
            
            # Make request with timeout
            try:
                response = await asyncio.wait_for(
                    self.client.generate(**params),
                    timeout=self.timeout
                )
            except asyncio.TimeoutError:
                raise LLMTimeoutError(f"Request timed out after {self.timeout}s", LLMProvider.OLLAMA, self.model)
            
            # Extract response text
            raw_response = response.get('response', '')
            if not raw_response:
                raise LLMError("Empty response from Ollama", LLMProvider.OLLAMA, self.model)
            
            # Calculate processing time
            processing_time = (time.time() - start_time) * 1000
            
            # Create response object
            llm_response = LLMResponse(
                raw_response=raw_response,
                provider=LLMProvider.OLLAMA,
                model=self.model,
                task_type=request.task_type,
                confidence_score=0.8,  # Default confidence for Ollama
                quality_score=0.8,     # Will be assessed by quality assessor
                processing_time_ms=processing_time,
                tokens_used=response.get('eval_count', 0)
            )
            
            logger.info(f"Ollama request completed in {processing_time:.1f}ms")
            return llm_response
            
        except (LLMError, LLMTimeoutError):
            raise
        except Exception as e:
            logger.error(f"Ollama request failed: {e}")
            raise LLMError(f"Ollama processing failed: {e}", LLMProvider.OLLAMA, self.model)
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check Ollama service health.
        
        Returns:
            Health status information
        """
        try:
            # Check if service is reachable
            models = await self.client.list()
            
            # Check if our model is available
            model_available = await self._check_model_availability()
            
            return {
                "status": "healthy",
                "base_url": self.base_url,
                "model": self.model,
                "model_available": model_available,
                "available_models": [m['name'] for m in models.get('models', [])],
                "last_check": self._last_check
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "base_url": self.base_url,
                "model": self.model,
                "error": str(e)
            }
