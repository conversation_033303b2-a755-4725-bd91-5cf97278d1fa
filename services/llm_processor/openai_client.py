"""
OpenAI Client - Fallback LLM integration for Star Power
"""

import asyncio
import json
import logging
import os
import time
from typing import Dict, Any, Optional

from openai import AsyncOpenAI

from .models import (
    LLMProvider, LLMRequest, LLMResponse, LLMTaskType,
    LLMError, LLMTimeoutError, LLMQuotaError
)

logger = logging.getLogger(__name__)


class OpenAIClient:
    """
    OpenAI API client for fallback LLM processing.
    
    Provides async interface to OpenAI models with error handling,
    quota management, and response quality assessment.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "gpt-4",
        timeout: int = 30,
        max_retries: int = 3
    ):
        """
        Initialize OpenAI client.
        
        Args:
            api_key: OpenAI API key (or from environment)
            model: Model name to use
            timeout: Request timeout in seconds
            max_retries: Maximum retry attempts
        """
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        
        if not self.api_key:
            logger.warning("No OpenAI API key provided - client will not be functional")
            self.client = None
        else:
            self.client = AsyncOpenAI(api_key=self.api_key)
        
        logger.info(f"Initialized OpenAI client: model: {model}")
    
    def _build_messages(self, request: LLMRequest) -> list:
        """
        Build messages for OpenAI chat completion.
        
        Args:
            request: LLM request
            
        Returns:
            List of messages for OpenAI API
        """
        if request.task_type == LLMTaskType.CELEBRITY_IDENTIFICATION:
            return self._build_celebrity_identification_messages(request)
        elif request.task_type == LLMTaskType.CONTENT_ANALYSIS:
            return self._build_content_analysis_messages(request)
        else:
            return self._build_generic_messages(request)
    
    def _build_celebrity_identification_messages(self, request: LLMRequest) -> list:
        """Build celebrity identification messages."""
        system_message = """You are an expert at identifying celebrities in news articles. Your task is to:

1. Identify any celebrities mentioned in the article (actors, musicians, politicians, athletes, etc.)
2. Provide confidence scores based on how certain you are about the identification
3. Include context where each celebrity was mentioned
4. Avoid false positives - only identify people who are clearly celebrities
5. Consider aliases and stage names

Respond in JSON format with the following structure:
{
    "celebrities": [
        {
            "name": "Full Celebrity Name",
            "confidence": 0.95,
            "context": "context where mentioned in article",
            "aliases": ["alias1", "alias2"],
            "birth_date": "YYYY-MM-DD or null",
            "profession": "actor/musician/politician/athlete/etc"
        }
    ],
    "summary": "Brief article summary",
    "key_topics": ["topic1", "topic2"]
}"""
        
        # Extract title and content
        title = getattr(request, 'article_title', 'Unknown')
        content = request.text
        
        user_message = f"""Article Title: {title}

Article Content:
{content}

Please identify any celebrities mentioned in this article."""
        
        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ]
    
    def _build_content_analysis_messages(self, request: LLMRequest) -> list:
        """Build content analysis messages."""
        system_message = "You are an expert content analyst. Provide detailed analysis of the given content."
        
        user_message = f"Please analyze the following content:\n\n{request.text}"
        if request.context:
            user_message += f"\n\nContext: {request.context}"
        
        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ]
    
    def _build_generic_messages(self, request: LLMRequest) -> list:
        """Build generic messages."""
        user_message = f"Please process the following text:\n\n{request.text}"
        if request.context:
            user_message += f"\n\nContext: {request.context}"
        
        return [
            {"role": "user", "content": user_message}
        ]
    
    async def process_request(self, request: LLMRequest) -> LLMResponse:
        """
        Process LLM request with OpenAI.
        
        Args:
            request: LLM request to process
            
        Returns:
            LLM response with results
            
        Raises:
            LLMError: If processing fails
            LLMTimeoutError: If request times out
            LLMQuotaError: If quota exceeded
        """
        if not self.client:
            raise LLMError("OpenAI client not initialized - missing API key", LLMProvider.OPENAI, self.model)
        
        start_time = time.time()
        
        try:
            # Build messages
            messages = self._build_messages(request)
            
            # Prepare request parameters
            params = {
                'model': self.model,
                'messages': messages,
                'timeout': self.timeout
            }
            
            # Add optional parameters
            if request.max_tokens:
                params['max_tokens'] = request.max_tokens
            if request.temperature is not None:
                params['temperature'] = request.temperature
            
            # Make request with timeout
            try:
                response = await asyncio.wait_for(
                    self.client.chat.completions.create(**params),
                    timeout=self.timeout
                )
            except asyncio.TimeoutError:
                raise LLMTimeoutError(f"Request timed out after {self.timeout}s", LLMProvider.OPENAI, self.model)
            
            # Extract response content
            if not response.choices or not response.choices[0].message.content:
                raise LLMError("Empty response from OpenAI", LLMProvider.OPENAI, self.model)
            
            raw_response = response.choices[0].message.content
            
            # Calculate processing time
            processing_time = (time.time() - start_time) * 1000
            
            # Calculate cost estimate (rough)
            tokens_used = response.usage.total_tokens if response.usage else 0
            cost_estimate = self._estimate_cost(tokens_used)
            
            # Create response object
            llm_response = LLMResponse(
                raw_response=raw_response,
                provider=LLMProvider.OPENAI,
                model=self.model,
                task_type=request.task_type,
                confidence_score=0.9,  # Default confidence for OpenAI
                quality_score=0.9,     # Will be assessed by quality assessor
                processing_time_ms=processing_time,
                tokens_used=tokens_used,
                cost_estimate=cost_estimate
            )
            
            logger.info(f"OpenAI request completed in {processing_time:.1f}ms, tokens: {tokens_used}")
            return llm_response
            
        except Exception as e:
            # Handle specific OpenAI errors
            error_message = str(e)
            
            if "rate_limit" in error_message.lower() or "quota" in error_message.lower():
                raise LLMQuotaError(f"OpenAI quota/rate limit exceeded: {e}", LLMProvider.OPENAI, self.model)
            elif "timeout" in error_message.lower():
                raise LLMTimeoutError(f"OpenAI request timed out: {e}", LLMProvider.OPENAI, self.model)
            else:
                logger.error(f"OpenAI request failed: {e}")
                raise LLMError(f"OpenAI processing failed: {e}", LLMProvider.OPENAI, self.model)
    
    def _estimate_cost(self, tokens: int) -> float:
        """
        Estimate cost based on token usage.
        
        Args:
            tokens: Number of tokens used
            
        Returns:
            Estimated cost in USD
        """
        # Rough cost estimates (as of 2024)
        cost_per_1k_tokens = {
            "gpt-4": 0.03,
            "gpt-4-turbo": 0.01,
            "gpt-3.5-turbo": 0.002
        }
        
        rate = cost_per_1k_tokens.get(self.model, 0.01)
        return (tokens / 1000) * rate
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check OpenAI service health.
        
        Returns:
            Health status information
        """
        if not self.client:
            return {
                "status": "unavailable",
                "model": self.model,
                "error": "No API key configured"
            }
        
        try:
            # Make a simple test request
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=5,
                timeout=10
            )
            
            return {
                "status": "healthy",
                "model": self.model,
                "test_response": response.choices[0].message.content if response.choices else None,
                "tokens_used": response.usage.total_tokens if response.usage else 0
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "model": self.model,
                "error": str(e)
            }
