"""
Perplexity Client - Perplexity API integration for Star Power Isolate
"""

import asyncio
import json
import logging
import os
import time
from typing import Dict, Any, Optional

import aiohttp

from .models import (
    LLMProvider, LLMRequest, LLMResponse, LLMTaskType,
    LLMError, LLMTimeoutError, LLMQuotaError
)
from .isolate_models import (
    IsolateExtractionRequest, IsolateExtractionResponse, IsolateResponse
)
from ..config import get_env_var

logger = logging.getLogger(__name__)


class PerplexityClient:
    """
    Perplexity API client for Isolate entity extraction.
    
    Provides async interface to Perplexity sonar-pro model with error handling,
    quota management, and response quality assessment.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "sonar-pro",
        timeout: int = 60,
        max_retries: int = 3
    ):
        """
        Initialize Perplexity client.
        
        Args:
            api_key: Perplexity API key (or from environment)
            model: Model name to use (default: sonar-pro)
            timeout: Request timeout in seconds
            max_retries: Maximum retry attempts
        """
        self.api_key = api_key or get_env_var('PERPLEXITY_API_KEY')
        self.model = model
        self.timeout = timeout
        self.max_retries = max_retries
        
        if not self.api_key:
            raise ValueError("Perplexity API key is required")
        
        # API configuration
        self.base_url = "https://api.perplexity.ai"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # Statistics
        self.stats = {
            "requests_made": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_tokens": 0,
            "total_cost": 0.0
        }
    
    async def extract_entities(self, request: IsolateExtractionRequest) -> IsolateExtractionResponse:
        """
        Extract entities from article text using Perplexity API.
        
        Args:
            request: Isolate extraction request
            
        Returns:
            Isolate extraction response with entities, relationships, actions, and endeavors
            
        Raises:
            LLMError: If extraction fails
        """
        start_time = time.time()
        
        try:
            # Load prompt template and schema
            prompt_template = self._load_prompt_template()
            schema = self._load_isolate_schema()
            
            # Format prompt
            formatted_prompt = prompt_template.format(
                article_text=request.article_text,
                schema=json.dumps(schema, indent=2)
            )
            
            # Make API request
            response_data = await self._make_api_request(
                prompt=formatted_prompt,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                timeout=request.timeout
            )
            
            # Parse response
            isolate_data = self._parse_isolate_response(response_data["choices"][0]["message"]["content"])
            
            # Calculate metrics
            processing_time_ms = (time.time() - start_time) * 1000
            tokens_used = response_data.get("usage", {}).get("total_tokens", 0)
            cost_estimate = self._calculate_cost(tokens_used)
            confidence_score = self._assess_confidence(isolate_data)
            
            # Update statistics
            self.stats["successful_requests"] += 1
            self.stats["total_tokens"] += tokens_used
            self.stats["total_cost"] += cost_estimate
            
            return IsolateExtractionResponse(
                isolate_data=isolate_data,
                processing_time_ms=processing_time_ms,
                tokens_used=tokens_used,
                cost_estimate=cost_estimate,
                confidence_score=confidence_score
            )
            
        except Exception as e:
            self.stats["failed_requests"] += 1
            logger.error(f"Perplexity entity extraction failed: {str(e)}")
            raise LLMError(f"Entity extraction failed: {str(e)}", LLMProvider.PERPLEXITY, self.model)
    
    async def _make_api_request(
        self,
        prompt: str,
        temperature: float = 0.1,
        max_tokens: int = 4000,
        timeout: int = 60
    ) -> Dict[str, Any]:
        """Make API request to Perplexity."""
        
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        self.stats["requests_made"] += 1
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
            for attempt in range(self.max_retries):
                try:
                    async with session.post(
                        f"{self.base_url}/chat/completions",
                        headers=self.headers,
                        json=payload
                    ) as response:
                        
                        if response.status == 200:
                            return await response.json()
                        elif response.status == 429:
                            # Rate limit - wait and retry
                            wait_time = 2 ** attempt
                            logger.warning(f"Rate limit hit, waiting {wait_time}s before retry {attempt + 1}")
                            await asyncio.sleep(wait_time)
                            continue
                        elif response.status == 401:
                            raise LLMError("Invalid API key", LLMProvider.PERPLEXITY, self.model)
                        else:
                            error_text = await response.text()
                            raise LLMError(f"API error {response.status}: {error_text}", LLMProvider.PERPLEXITY, self.model)
                            
                except asyncio.TimeoutError:
                    if attempt == self.max_retries - 1:
                        raise LLMTimeoutError(f"Request timeout after {timeout}s", LLMProvider.PERPLEXITY, self.model)
                    logger.warning(f"Request timeout, retrying {attempt + 1}/{self.max_retries}")
                    continue
                except Exception as e:
                    if attempt == self.max_retries - 1:
                        raise LLMError(f"Request failed: {str(e)}", LLMProvider.PERPLEXITY, self.model)
                    logger.warning(f"Request failed, retrying {attempt + 1}/{self.max_retries}: {str(e)}")
                    continue
        
        raise LLMError("All retry attempts failed", LLMProvider.PERPLEXITY, self.model)
    
    def _load_prompt_template(self) -> str:
        """Load the Isolate prompt template."""
        # For now, return the template inline. In production, this could be loaded from a file.
        return """ROLE: You are the Star Power Isolate component, specialized in extracting structured astrological data from celebrity news articles.

TASK: Extract entities, relationships, actions, and endeavors from the provided text and return them in the specified JSON format.

GUIDELINES:
1. Extract all primary entities (people, organizations, objects, events, endeavors, ships, locations)
2. Include non-primary entities if they meet the Hellenistic house relationship criteria
3. Capture relationships (ongoing states between entities)
4. Record actions (singular events with specific timing)
5. Document endeavors (projects, releases, activities)
6. Use precise inception dates/times/locations when available
7. Mark estimated data with estimated_inception: true

ARTICLE TEXT:
{article_text}

OUTPUT FORMAT: Return ONLY the JSON response following the exact schema structure provided below.

SCHEMA:
{schema}

RESPONSE:"""
    
    def _load_isolate_schema(self) -> Dict[str, Any]:
        """Load the Isolate JSON schema."""
        return {
            "source": "string",
            "entities": [
                {
                    "name": "string",
                    "type": "person|organization|object|event|endeavor|ship|location",
                    "subtype": "string",
                    "roles": ["role1", "role2"],
                    "primary": "true|false",
                    "inception_date": "YYYY-MM-DD | null",
                    "inception_time": "HH:MM | null",
                    "inception_location": "City, State/Country | null",
                    "estimated_inception": "true|false",
                    "relevance_reason": "string"
                }
            ],
            "relationships": [
                {
                    "entity1": "string",
                    "entity2": "string",
                    "relation_type": "string",
                    "both_primary": "true|false",
                    "inception_date": "YYYY-MM-DD | null",
                    "inception_time": "HH:MM | null",
                    "inception_location": "string | null",
                    "estimated_inception": "true|false"
                }
            ],
            "actions": [
                {
                    "entity1": "string",
                    "entity2": "string",
                    "action_type": "string",
                    "both_primary": "true|false",
                    "inception_date": "YYYY-MM-DD",
                    "inception_time": "HH:MM | null",
                    "inception_location": "string | null",
                    "estimated_inception": "true|false"
                }
            ],
            "endeavors": [
                {
                    "entity": "string",
                    "endeavor_type": "string",
                    "endeavor_label": "string",
                    "primary": "true|false",
                    "inception_date": "YYYY-MM-DD | null",
                    "inception_time": "HH:MM | null",
                    "inception_location": "string | null",
                    "estimated_inception": "true|false"
                }
            ],
            "notes": ["string"]
        }

    def _parse_isolate_response(self, response_text: str) -> IsolateResponse:
        """Parse Perplexity response into IsolateResponse model."""
        try:
            # Extract JSON from response (handle potential markdown formatting)
            response_text = response_text.strip()
            if response_text.startswith("```json"):
                response_text = response_text[7:]
            if response_text.endswith("```"):
                response_text = response_text[:-3]

            # Parse JSON
            response_data = json.loads(response_text.strip())

            # Validate and create IsolateResponse
            return IsolateResponse(**response_data)

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Response text: {response_text}")
            raise LLMError(f"Invalid JSON response: {str(e)}", LLMProvider.PERPLEXITY, self.model)
        except Exception as e:
            logger.error(f"Failed to create IsolateResponse: {e}")
            raise LLMError(f"Response validation failed: {str(e)}", LLMProvider.PERPLEXITY, self.model)

    def _calculate_cost(self, tokens_used: int) -> float:
        """Calculate estimated cost for sonar-pro model."""
        # Perplexity sonar-pro pricing (approximate)
        # Input: $0.0015 per 1K tokens, Output: $0.0015 per 1K tokens
        # Assuming roughly equal input/output split
        cost_per_1k_tokens = 0.0015
        return (tokens_used / 1000) * cost_per_1k_tokens

    def _assess_confidence(self, isolate_data: IsolateResponse) -> float:
        """Assess confidence in extraction quality."""
        # Simple heuristic based on data completeness
        total_items = len(isolate_data.entities) + len(isolate_data.relationships) + len(isolate_data.actions) + len(isolate_data.endeavors)

        if total_items == 0:
            return 0.0

        # Check for required fields
        entities_with_dates = sum(1 for e in isolate_data.entities if e.inception_date)
        relationships_with_dates = sum(1 for r in isolate_data.relationships if r.inception_date)
        actions_with_dates = sum(1 for a in isolate_data.actions if a.inception_date)

        completeness_score = (entities_with_dates + relationships_with_dates + actions_with_dates) / max(total_items, 1)

        # Base confidence starts at 0.7, adjusted by completeness
        base_confidence = 0.7
        confidence = min(1.0, base_confidence + (completeness_score * 0.3))

        return confidence

    def get_stats(self) -> Dict[str, Any]:
        """Get client statistics."""
        return self.stats.copy()

    async def health_check(self) -> bool:
        """Check if Perplexity API is accessible."""
        try:
            # Simple test request
            test_request = IsolateExtractionRequest(
                article_text="Test article about a celebrity.",
                max_tokens=100,
                timeout=10
            )

            await self.extract_entities(test_request)
            return True

        except Exception as e:
            logger.error(f"Perplexity health check failed: {e}")
            return False
