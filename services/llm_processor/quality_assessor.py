"""
Quality Assessor - LLM response quality evaluation for Star Power
"""

import json
import logging
import re
from typing import Dict, Any, List, Optional

from .models import LLMResponse, LLMTaskType, CelebrityIdentificationResponse

logger = logging.getLogger(__name__)


class ResponseQualityAssessor:
    """
    Evaluates LLM response quality for different task types.
    
    Provides quality scoring based on:
    - Response completeness
    - Format compliance
    - Content relevance
    - Task-specific criteria
    """
    
    def __init__(self):
        """Initialize quality assessor."""
        self.quality_thresholds = {
            LLMTaskType.CELEBRITY_IDENTIFICATION: 0.7,
            LLMTaskType.CONTENT_ANALYSIS: 0.6,
            LLMTaskType.DATA_ENHANCEMENT: 0.6,
            LLMTaskType.VALIDATION: 0.8
        }
        
        logger.info("Initialized ResponseQualityAssessor")
    
    def assess_quality(self, response: LLMResponse) -> float:
        """
        Assess overall response quality.
        
        Args:
            response: LLM response to assess
            
        Returns:
            Quality score between 0.0 and 1.0
        """
        if response.task_type == LLMTaskType.CELEBRITY_IDENTIFICATION:
            return self._assess_celebrity_identification_quality(response)
        elif response.task_type == LLMTaskType.CONTENT_ANALYSIS:
            return self._assess_content_analysis_quality(response)
        else:
            return self._assess_generic_quality(response)
    
    def _assess_celebrity_identification_quality(self, response: LLMResponse) -> float:
        """
        Assess celebrity identification response quality.
        
        Args:
            response: Celebrity identification response
            
        Returns:
            Quality score
        """
        score_components = {}
        
        # 1. Response format quality (30%)
        format_score = self._assess_format_quality(response)
        score_components['format'] = format_score * 0.3
        
        # 2. Content completeness (25%)
        completeness_score = self._assess_completeness(response)
        score_components['completeness'] = completeness_score * 0.25
        
        # 3. Celebrity data quality (25%)
        if isinstance(response, CelebrityIdentificationResponse):
            celebrity_score = self._assess_celebrity_data_quality(response)
        else:
            celebrity_score = 0.5  # Default if not celebrity response
        score_components['celebrity_data'] = celebrity_score * 0.25
        
        # 4. Confidence consistency (20%)
        confidence_score = self._assess_confidence_consistency(response)
        score_components['confidence'] = confidence_score * 0.2
        
        total_score = sum(score_components.values())
        
        logger.debug(f"Celebrity ID quality assessment: {score_components}, total: {total_score:.3f}")
        return min(1.0, max(0.0, total_score))
    
    def _assess_content_analysis_quality(self, response: LLMResponse) -> float:
        """
        Assess content analysis response quality.
        
        Args:
            response: Content analysis response
            
        Returns:
            Quality score
        """
        score_components = {}
        
        # 1. Response length and detail (40%)
        length_score = self._assess_response_length(response.raw_response)
        score_components['length'] = length_score * 0.4
        
        # 2. Structure and organization (30%)
        structure_score = self._assess_structure_quality(response.raw_response)
        score_components['structure'] = structure_score * 0.3
        
        # 3. Format compliance (30%)
        format_score = self._assess_format_quality(response)
        score_components['format'] = format_score * 0.3
        
        total_score = sum(score_components.values())
        
        logger.debug(f"Content analysis quality assessment: {score_components}, total: {total_score:.3f}")
        return min(1.0, max(0.0, total_score))
    
    def _assess_generic_quality(self, response: LLMResponse) -> float:
        """
        Assess generic response quality.
        
        Args:
            response: Generic LLM response
            
        Returns:
            Quality score
        """
        score_components = {}
        
        # 1. Response completeness (50%)
        completeness_score = self._assess_completeness(response)
        score_components['completeness'] = completeness_score * 0.5
        
        # 2. Format quality (50%)
        format_score = self._assess_format_quality(response)
        score_components['format'] = format_score * 0.5
        
        total_score = sum(score_components.values())
        
        logger.debug(f"Generic quality assessment: {score_components}, total: {total_score:.3f}")
        return min(1.0, max(0.0, total_score))
    
    def _assess_format_quality(self, response: LLMResponse) -> float:
        """
        Assess response format quality.
        
        Args:
            response: LLM response
            
        Returns:
            Format quality score
        """
        raw_text = response.raw_response.strip()
        
        if not raw_text:
            return 0.0
        
        score = 0.0
        
        # Check for JSON format (high quality)
        try:
            json.loads(raw_text)
            score += 0.4
        except json.JSONDecodeError:
            # Check for JSON blocks
            if re.search(r'```json.*?```', raw_text, re.DOTALL):
                score += 0.3
            elif re.search(r'\{.*\}', raw_text, re.DOTALL):
                score += 0.2
        
        # Check for thinking tags
        if re.search(r'<think>.*?</think>', raw_text, re.DOTALL):
            score += 0.2
        
        # Check for structured content
        if re.search(r'^\s*[-*•]\s+', raw_text, re.MULTILINE):
            score += 0.1
        
        # Check for proper capitalization and punctuation
        if re.search(r'[A-Z].*[.!?]$', raw_text.strip()):
            score += 0.1
        
        # Penalize very short responses
        if len(raw_text) < 50:
            score *= 0.5
        
        return min(1.0, score)
    
    def _assess_completeness(self, response: LLMResponse) -> float:
        """
        Assess response completeness.
        
        Args:
            response: LLM response
            
        Returns:
            Completeness score
        """
        raw_text = response.raw_response.strip()
        
        if not raw_text:
            return 0.0
        
        # Base score from length
        length_score = min(1.0, len(raw_text) / 200)  # Normalize to 200 chars
        
        # Check for truncation indicators
        truncation_indicators = ['...', '[truncated]', '[cut off]', 'incomplete']
        if any(indicator in raw_text.lower() for indicator in truncation_indicators):
            length_score *= 0.5
        
        # Check for parsed content availability
        if response.parsed_content:
            length_score += 0.2
        
        return min(1.0, length_score)
    
    def _assess_celebrity_data_quality(self, response: CelebrityIdentificationResponse) -> float:
        """
        Assess celebrity data quality.
        
        Args:
            response: Celebrity identification response
            
        Returns:
            Celebrity data quality score
        """
        if not response.celebrities:
            return 0.0
        
        total_score = 0.0
        
        for celebrity in response.celebrities:
            celeb_score = 0.0
            
            # Name quality (40%)
            if celebrity.name and len(celebrity.name.strip()) > 2:
                celeb_score += 0.4
                # Bonus for full names
                if len(celebrity.name.split()) >= 2:
                    celeb_score += 0.1
            
            # Confidence score (30%)
            if 0.0 <= celebrity.confidence <= 1.0:
                celeb_score += 0.3 * celebrity.confidence
            
            # Context quality (20%)
            if celebrity.context and len(celebrity.context.strip()) > 10:
                celeb_score += 0.2
            
            # Additional data (10%)
            additional_data_count = sum([
                bool(celebrity.aliases),
                bool(celebrity.birth_date),
                bool(celebrity.birth_location),
                bool(celebrity.profession)
            ])
            celeb_score += 0.1 * (additional_data_count / 4)
            
            total_score += celeb_score
        
        # Average score across all celebrities
        average_score = total_score / len(response.celebrities)
        
        # Bonus for multiple high-quality matches
        high_confidence_count = len([c for c in response.celebrities if c.confidence >= 0.8])
        if high_confidence_count > 1:
            average_score += 0.1
        
        return min(1.0, average_score)
    
    def _assess_confidence_consistency(self, response: LLMResponse) -> float:
        """
        Assess confidence score consistency.
        
        Args:
            response: LLM response
            
        Returns:
            Confidence consistency score
        """
        if isinstance(response, CelebrityIdentificationResponse):
            if not response.celebrities:
                return 0.5  # Neutral score for no celebrities
            
            # Check if confidence scores are reasonable
            confidences = [c.confidence for c in response.celebrities]
            
            # Penalize if all confidences are the same (likely default)
            if len(set(confidences)) == 1 and len(confidences) > 1:
                return 0.3
            
            # Reward varied, reasonable confidence scores
            if all(0.0 <= c <= 1.0 for c in confidences):
                return 0.8
            else:
                return 0.2
        
        return 0.5  # Default for non-celebrity responses
    
    def _assess_response_length(self, text: str) -> float:
        """
        Assess response length appropriateness.
        
        Args:
            text: Response text
            
        Returns:
            Length quality score
        """
        length = len(text.strip())
        
        if length < 20:
            return 0.1  # Too short
        elif length < 100:
            return 0.5  # Short but acceptable
        elif length < 500:
            return 1.0  # Good length
        elif length < 1000:
            return 0.9  # Long but acceptable
        else:
            return 0.7  # Very long, might be verbose
    
    def _assess_structure_quality(self, text: str) -> float:
        """
        Assess text structure quality.
        
        Args:
            text: Response text
            
        Returns:
            Structure quality score
        """
        score = 0.0
        
        # Check for paragraphs
        paragraphs = text.split('\n\n')
        if len(paragraphs) > 1:
            score += 0.3
        
        # Check for lists or bullet points
        if re.search(r'^\s*[-*•]\s+', text, re.MULTILINE):
            score += 0.2
        
        # Check for proper sentence structure
        sentences = re.split(r'[.!?]+', text)
        if len(sentences) > 2:
            score += 0.3
        
        # Check for headers or sections
        if re.search(r'^[A-Z][^a-z]*:$', text, re.MULTILINE):
            score += 0.2
        
        return min(1.0, score)
