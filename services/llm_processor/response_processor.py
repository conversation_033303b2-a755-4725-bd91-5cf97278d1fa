"""
Response Processor - Multi-format LLM response parsing for Star Power
"""

import json
import logging
import re
from typing import Dict, Any, List, Optional, Union

from .models import (
    LLMResponse, LLMTaskType, CelebrityMatch, CelebrityIdentificationResponse,
    LLMValidationError
)

logger = logging.getLogger(__name__)


class ResponseProcessor:
    """
    Multi-format LLM response processor.
    
    Handles various response formats from different LLM providers:
    - JSON responses
    - Thinking tag responses (<think>...</think>)
    - Structured text responses
    - Plain text with extraction
    """
    
    def __init__(self):
        """Initialize response processor."""
        self.parsers = [
            self._parse_json,
            self._parse_thinking_tags,
            self._parse_structured_text,
            self._parse_plain_text
        ]
        
        logger.info("Initialized ResponseProcessor with multi-format support")
    
    def process_response(self, response: LLMResponse) -> LLMResponse:
        """
        Process LLM response and extract structured data.
        
        Args:
            response: Raw LLM response
            
        Returns:
            Enhanced response with parsed content
        """
        raw_text = response.raw_response.strip()
        
        # Try each parser in order
        parsed_content = None
        extraction_method = None
        
        for parser in self.parsers:
            try:
                parsed_content = parser(raw_text)
                if parsed_content:
                    extraction_method = parser.__name__
                    break
            except Exception as e:
                logger.debug(f"Parser {parser.__name__} failed: {e}")
                continue
        
        if not parsed_content:
            logger.warning("No parser succeeded, using fallback")
            parsed_content = {"raw_text": raw_text}
            extraction_method = "fallback"
        
        # Update response with parsed content
        response.parsed_content = parsed_content
        response.extracted_data = {"extraction_method": extraction_method}
        
        # Create task-specific response if applicable
        if response.task_type == LLMTaskType.CELEBRITY_IDENTIFICATION:
            return self._create_celebrity_response(response, parsed_content)
        
        return response
    
    def _parse_json(self, text: str) -> Optional[Dict[str, Any]]:
        """
        Parse JSON response.
        
        Args:
            text: Response text
            
        Returns:
            Parsed JSON data or None
        """
        # Look for JSON blocks
        json_patterns = [
            r'```json\s*(\{.*?\})\s*```',  # JSON code blocks
            r'```\s*(\{.*?\})\s*```',      # Generic code blocks with JSON
            r'(\{.*\})',                   # Direct JSON objects
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, text, re.DOTALL)
            for match in matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue
        
        # Try parsing the entire text as JSON
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            return None
    
    def _parse_thinking_tags(self, text: str) -> Optional[Dict[str, Any]]:
        """
        Parse response with thinking tags.
        
        Args:
            text: Response text
            
        Returns:
            Parsed content with thinking separated
        """
        # Extract thinking content
        thinking_pattern = r'<think>(.*?)</think>'
        thinking_matches = re.findall(thinking_pattern, text, re.DOTALL)
        
        # Remove thinking tags from main content
        main_content = re.sub(thinking_pattern, '', text, flags=re.DOTALL).strip()
        
        if not thinking_matches:
            return None
        
        # Try to parse the main content as JSON
        parsed_main = self._parse_json(main_content)
        
        result = {
            "thinking": [match.strip() for match in thinking_matches],
            "main_content": main_content
        }
        
        if parsed_main:
            result.update(parsed_main)
        
        return result
    
    def _parse_structured_text(self, text: str) -> Optional[Dict[str, Any]]:
        """
        Parse structured text response.
        
        Args:
            text: Response text
            
        Returns:
            Parsed structured data or None
        """
        # Look for key-value patterns
        patterns = {
            'celebrities': r'(?:celebrities?|people|names?):\s*(.+?)(?:\n\n|\n[A-Z]|$)',
            'summary': r'(?:summary|overview):\s*(.+?)(?:\n\n|\n[A-Z]|$)',
            'topics': r'(?:topics?|themes?):\s*(.+?)(?:\n\n|\n[A-Z]|$)',
            'confidence': r'(?:confidence|certainty):\s*([0-9.]+)',
        }
        
        result = {}
        found_any = False
        
        for key, pattern in patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE | re.DOTALL)
            if matches:
                found_any = True
                value = matches[0].strip()
                
                # Process specific fields
                if key == 'celebrities':
                    result[key] = self._extract_celebrity_list(value)
                elif key == 'topics':
                    result[key] = [t.strip() for t in re.split(r'[,\n]', value) if t.strip()]
                elif key == 'confidence':
                    try:
                        result[key] = float(value)
                    except ValueError:
                        result[key] = value
                else:
                    result[key] = value
        
        return result if found_any else None
    
    def _parse_plain_text(self, text: str) -> Optional[Dict[str, Any]]:
        """
        Parse plain text response with basic extraction.
        
        Args:
            text: Response text
            
        Returns:
            Basic extracted data
        """
        # Extract potential celebrity names (capitalized words)
        name_pattern = r'\b[A-Z][a-z]+ [A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b'
        potential_names = re.findall(name_pattern, text)
        
        # Filter out common non-celebrity patterns
        exclude_patterns = [
            r'^(The|A|An|This|That|These|Those)\s',
            r'^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)',
            r'^(January|February|March|April|May|June|July|August|September|October|November|December)',
            r'^(United States|New York|Los Angeles|San Francisco)',
        ]
        
        filtered_names = []
        for name in potential_names:
            if not any(re.match(pattern, name) for pattern in exclude_patterns):
                filtered_names.append(name)
        
        return {
            "potential_celebrities": list(set(filtered_names)),
            "raw_text": text,
            "extraction_method": "plain_text_fallback"
        }
    
    def _extract_celebrity_list(self, text: str) -> List[str]:
        """
        Extract celebrity names from text.
        
        Args:
            text: Text containing celebrity names
            
        Returns:
            List of celebrity names
        """
        # Split by common delimiters
        names = re.split(r'[,\n;]', text)
        
        # Clean and filter names
        cleaned_names = []
        for name in names:
            name = name.strip()
            # Remove bullet points, numbers, etc.
            name = re.sub(r'^[-*•\d.)\s]+', '', name)
            if name and len(name) > 2:
                cleaned_names.append(name)
        
        return cleaned_names
    
    def _create_celebrity_response(
        self, 
        response: LLMResponse, 
        parsed_content: Dict[str, Any]
    ) -> CelebrityIdentificationResponse:
        """
        Create celebrity identification specific response.
        
        Args:
            response: Base LLM response
            parsed_content: Parsed content
            
        Returns:
            Celebrity identification response
        """
        celebrities = []
        
        # Extract celebrities from parsed content
        if 'celebrities' in parsed_content and isinstance(parsed_content['celebrities'], list):
            for celeb_data in parsed_content['celebrities']:
                if isinstance(celeb_data, dict):
                    # Structured celebrity data
                    celebrities.append(CelebrityMatch(
                        name=celeb_data.get('name', ''),
                        confidence=float(celeb_data.get('confidence', 0.5)),
                        context=celeb_data.get('context', ''),
                        aliases=celeb_data.get('aliases', []),
                        birth_date=celeb_data.get('birth_date'),
                        birth_location=celeb_data.get('birth_location'),
                        profession=celeb_data.get('profession'),
                        reasoning=celeb_data.get('reasoning')
                    ))
                elif isinstance(celeb_data, str):
                    # Simple name string
                    celebrities.append(CelebrityMatch(
                        name=celeb_data,
                        confidence=0.5,
                        context="Extracted from text"
                    ))
        elif 'potential_celebrities' in parsed_content:
            # Fallback extraction
            for name in parsed_content['potential_celebrities']:
                celebrities.append(CelebrityMatch(
                    name=name,
                    confidence=0.3,
                    context="Potential match from text analysis"
                ))
        
        # Create celebrity identification response
        celeb_response = CelebrityIdentificationResponse(
            raw_response=response.raw_response,
            parsed_content=response.parsed_content,
            extracted_data=response.extracted_data,
            provider=response.provider,
            model=response.model,
            task_type=response.task_type,
            confidence_score=response.confidence_score,
            quality_score=response.quality_score,
            processing_time_ms=response.processing_time_ms,
            tokens_used=response.tokens_used,
            cost_estimate=response.cost_estimate,
            created_at=response.created_at,
            celebrities=celebrities,
            article_title=getattr(response, 'article_title', 'Unknown'),
            article_summary=parsed_content.get('summary'),
            key_topics=parsed_content.get('key_topics', [])
        )
        
        return celeb_response
