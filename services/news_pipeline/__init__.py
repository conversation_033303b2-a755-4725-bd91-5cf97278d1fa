# News Pipeline Service Package
"""
News Pipeline Service - Article Processing and Celebrity Identification

This package handles news article processing and celebrity identification:
- gnews_client.py: GNews API integration with circuit breaker and rate limiting
- content_extractor.py: Multi-strategy article content extraction
- news_ingestion_service.py: Orchestrated news processing pipeline
- actor_discovery.py: Entity/celebrity identification (TODO)
- geo_processor.py: Location/timezone processing (TODO)
- article_processor.py: Pipeline orchestration (TODO)
"""

from .gnews_client import (
    GNewsClient,
    GNewsConfig,
    GNewsArticle,
    GNewsResponse,
    GNewsLanguage,
    GNewsCountry,
    RateLimiter,
    CircuitBreaker
)

from .content_extractor import (
    ContentExtractor,
    ExtractedContent,
    ContentExtractionError,
    BaseExtractor,
    BeautifulSoupExtractor,
    ReadabilityExtractor,
    NewspaperExtractor,
    GeographicExtractor,
    GeographicLocation
)

from .news_ingestion_service import (
    NewsIngestionService,
    NewsIngestionConfig,
    NewsIngestionStats
)

__version__ = "1.0.0"
__all__ = [
    # GNews Client
    "GNewsClient",
    "GNewsConfig",
    "GNewsArticle",
    "GNewsResponse",
    "GNewsLanguage",
    "GNewsCountry",
    "RateLimiter",
    "CircuitBreaker",

    # Content Extractor
    "ContentExtractor",
    "ExtractedContent",
    "ContentExtractionError",
    "BaseExtractor",
    "BeautifulSoupExtractor",
    "ReadabilityExtractor",
    "NewspaperExtractor",
    "GeographicExtractor",
    "GeographicLocation",

    # News Ingestion Service
    "NewsIngestionService",
    "NewsIngestionConfig",
    "NewsIngestionStats"
]
