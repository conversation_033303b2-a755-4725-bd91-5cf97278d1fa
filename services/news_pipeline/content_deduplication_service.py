"""
Content Deduplication Service

Advanced content deduplication using multiple similarity algorithms,
semantic analysis, and intelligent clustering for news articles.
"""

import asyncio
import hashlib
import logging
import re
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Set, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class SimilarityMethod(str, Enum):
    """Content similarity detection methods."""
    EXACT_HASH = "exact_hash"
    FUZZY_HASH = "fuzzy_hash"
    TFIDF_COSINE = "tfidf_cosine"
    SEMANTIC_SIMILARITY = "semantic_similarity"
    TITLE_SIMILARITY = "title_similarity"


class DuplicateType(str, Enum):
    """Types of duplicate content."""
    EXACT_DUPLICATE = "exact_duplicate"
    NEAR_DUPLICATE = "near_duplicate"
    SEMANTIC_DUPLICATE = "semantic_duplicate"
    TITLE_DUPLICATE = "title_duplicate"
    REPOST = "repost"


@dataclass
class DuplicateMatch:
    """Represents a duplicate content match."""
    article_id_1: str
    article_id_2: str
    similarity_score: float
    method: SimilarityMethod
    duplicate_type: DuplicateType
    confidence: float
    metadata: Dict[str, Any]


@dataclass
class ContentFingerprint:
    """Content fingerprint for deduplication."""
    article_id: str
    exact_hash: str
    fuzzy_hash: str
    title_hash: str
    content_tokens: List[str]
    tfidf_vector: Optional[np.ndarray] = None
    semantic_embedding: Optional[np.ndarray] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now(timezone.utc)


class DeduplicationConfig(BaseModel):
    """Configuration for content deduplication."""
    enable_exact_matching: bool = Field(True, description="Enable exact hash matching")
    enable_fuzzy_matching: bool = Field(True, description="Enable fuzzy hash matching")
    enable_tfidf_matching: bool = Field(True, description="Enable TF-IDF cosine similarity")
    enable_semantic_matching: bool = Field(False, description="Enable semantic similarity")
    
    # Similarity thresholds
    exact_threshold: float = Field(1.0, description="Exact match threshold")
    fuzzy_threshold: float = Field(0.95, description="Fuzzy match threshold")
    tfidf_threshold: float = Field(0.85, description="TF-IDF similarity threshold")
    semantic_threshold: float = Field(0.90, description="Semantic similarity threshold")
    title_threshold: float = Field(0.80, description="Title similarity threshold")
    
    # Performance settings
    max_fingerprints_memory: int = Field(10000, description="Max fingerprints in memory")
    fingerprint_ttl_hours: int = Field(168, description="Fingerprint TTL in hours (7 days)")
    batch_size: int = Field(100, description="Batch size for similarity calculations")
    
    # Content preprocessing
    min_content_length: int = Field(50, description="Minimum content length for processing")
    remove_stopwords: bool = Field(True, description="Remove stopwords from content")
    normalize_whitespace: bool = Field(True, description="Normalize whitespace")


class DeduplicationStats(BaseModel):
    """Statistics for deduplication operations."""
    total_articles_processed: int = 0
    exact_duplicates_found: int = 0
    fuzzy_duplicates_found: int = 0
    tfidf_duplicates_found: int = 0
    semantic_duplicates_found: int = 0
    title_duplicates_found: int = 0
    
    fingerprints_created: int = 0
    fingerprints_in_memory: int = 0
    similarity_comparisons: int = 0
    
    processing_time_seconds: float = 0.0
    average_similarity_score: float = 0.0
    last_deduplication: Optional[datetime] = None


class ContentDeduplicationService:
    """
    Advanced content deduplication service using multiple similarity algorithms
    and intelligent clustering for accurate duplicate detection.
    """
    
    def __init__(self, config: Optional[DeduplicationConfig] = None):
        """
        Initialize content deduplication service.
        
        Args:
            config: Deduplication configuration
        """
        self.config = config or DeduplicationConfig()
        self.stats = DeduplicationStats()
        
        # In-memory fingerprint storage
        self.fingerprints: Dict[str, ContentFingerprint] = {}
        self.duplicate_matches: List[DuplicateMatch] = []
        
        # TF-IDF vectorizer for content similarity
        self.tfidf_vectorizer = TfidfVectorizer(
            max_features=5000,
            stop_words='english' if self.config.remove_stopwords else None,
            ngram_range=(1, 2),
            min_df=1,  # Allow single occurrence
            max_df=0.95
        )
        self.tfidf_fitted = False
        
        logger.info("Content deduplication service initialized")
    
    async def process_articles(self, articles: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], List[DuplicateMatch]]:
        """
        Process articles for deduplication.
        
        Args:
            articles: List of article dictionaries
            
        Returns:
            Tuple of (unique_articles, duplicate_matches)
        """
        start_time = datetime.now()
        
        logger.info(f"Processing {len(articles)} articles for deduplication")
        
        # Create fingerprints for new articles
        new_fingerprints = []
        for article in articles:
            fingerprint = await self._create_fingerprint(article)
            if fingerprint:
                new_fingerprints.append(fingerprint)
        
        # Find duplicates
        duplicate_matches = await self._find_duplicates(new_fingerprints)
        
        # Filter unique articles
        duplicate_ids = set()

        # Create a lookup for all fingerprints (existing + new)
        all_fingerprints_lookup = dict(self.fingerprints)
        for fp in new_fingerprints:
            all_fingerprints_lookup[fp.article_id] = fp

        for match in duplicate_matches:
            # Keep the older article (assuming it's more authoritative)
            fp1 = all_fingerprints_lookup.get(match.article_id_1)
            fp2 = all_fingerprints_lookup.get(match.article_id_2)

            if fp1 and fp2:
                if fp1.created_at <= fp2.created_at:
                    duplicate_ids.add(match.article_id_2)
                else:
                    duplicate_ids.add(match.article_id_1)
        
        unique_articles = [
            article for article in articles 
            if article.get('id') not in duplicate_ids
        ]
        
        # Update fingerprints storage
        for fingerprint in new_fingerprints:
            if fingerprint.article_id not in duplicate_ids:
                self.fingerprints[fingerprint.article_id] = fingerprint
        
        # Clean up old fingerprints
        await self._cleanup_old_fingerprints()
        
        # Update statistics
        self.stats.total_articles_processed += len(articles)
        self.stats.fingerprints_created += len(new_fingerprints)
        self.stats.fingerprints_in_memory = len(self.fingerprints)
        self.stats.processing_time_seconds = (datetime.now() - start_time).total_seconds()
        self.stats.last_deduplication = datetime.now(timezone.utc)
        
        logger.info(f"Deduplication completed: {len(unique_articles)}/{len(articles)} unique articles")
        
        return unique_articles, duplicate_matches
    
    async def _create_fingerprint(self, article: Dict[str, Any]) -> Optional[ContentFingerprint]:
        """Create content fingerprint for an article."""
        try:
            article_id = article.get('id')
            title = article.get('title', '')
            content = article.get('content', '') or article.get('description', '')
            
            if not article_id or len(content) < self.config.min_content_length:
                return None
            
            # Preprocess content
            processed_content = self._preprocess_content(content)
            processed_title = self._preprocess_content(title)
            
            # Generate hashes
            exact_hash = hashlib.md5(processed_content.encode()).hexdigest()
            fuzzy_hash = self._generate_fuzzy_hash(processed_content)
            title_hash = hashlib.md5(processed_title.encode()).hexdigest()
            
            # Tokenize content
            content_tokens = processed_content.split()
            
            fingerprint = ContentFingerprint(
                article_id=article_id,
                exact_hash=exact_hash,
                fuzzy_hash=fuzzy_hash,
                title_hash=title_hash,
                content_tokens=content_tokens
            )
            
            return fingerprint
            
        except Exception as e:
            logger.error(f"Failed to create fingerprint for article {article.get('id')}: {e}")
            return None
    
    def _preprocess_content(self, content: str) -> str:
        """Preprocess content for fingerprinting."""
        if not content:
            return ""
        
        # Normalize whitespace
        if self.config.normalize_whitespace:
            content = re.sub(r'\s+', ' ', content.strip())
        
        # Convert to lowercase
        content = content.lower()
        
        # Remove special characters and punctuation
        content = re.sub(r'[^\w\s]', '', content)

        return content.strip()
    
    def _generate_fuzzy_hash(self, content: str) -> str:
        """Generate fuzzy hash for near-duplicate detection."""
        # Simple fuzzy hash based on word frequency
        words = content.split()
        word_freq = {}
        
        for word in words:
            if len(word) > 3:  # Only consider words longer than 3 characters
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # Get top frequent words
        top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:20]
        fuzzy_content = ' '.join([word for word, _ in top_words])
        
        return hashlib.md5(fuzzy_content.encode()).hexdigest()
    
    async def _find_duplicates(self, new_fingerprints: List[ContentFingerprint]) -> List[DuplicateMatch]:
        """Find duplicates using multiple similarity methods."""
        duplicate_matches = []
        
        # Compare new fingerprints against existing ones and each other
        all_fingerprints = list(self.fingerprints.values()) + new_fingerprints

        for i, fp1 in enumerate(new_fingerprints):
            # Compare against existing fingerprints
            for fp2 in self.fingerprints.values():
                if fp1.article_id != fp2.article_id:
                    matches = await self._compare_fingerprints(fp1, fp2)
                    duplicate_matches.extend(matches)

            # Compare against other new fingerprints
            for j, fp2 in enumerate(new_fingerprints[i+1:], i+1):
                if fp1.article_id != fp2.article_id:
                    matches = await self._compare_fingerprints(fp1, fp2)
                    duplicate_matches.extend(matches)
        
        # Update duplicate type statistics
        for match in duplicate_matches:
            if match.duplicate_type == DuplicateType.EXACT_DUPLICATE:
                self.stats.exact_duplicates_found += 1
            elif match.duplicate_type == DuplicateType.NEAR_DUPLICATE:
                self.stats.fuzzy_duplicates_found += 1
            elif match.duplicate_type == DuplicateType.SEMANTIC_DUPLICATE:
                self.stats.semantic_duplicates_found += 1
            elif match.duplicate_type == DuplicateType.TITLE_DUPLICATE:
                self.stats.title_duplicates_found += 1
        
        return duplicate_matches
    
    async def _compare_fingerprints(self, fp1: ContentFingerprint, fp2: ContentFingerprint) -> List[DuplicateMatch]:
        """Compare two fingerprints using multiple methods."""
        matches = []
        self.stats.similarity_comparisons += 1
        
        # Exact hash matching
        if self.config.enable_exact_matching and fp1.exact_hash == fp2.exact_hash:
            match = DuplicateMatch(
                article_id_1=fp1.article_id,
                article_id_2=fp2.article_id,
                similarity_score=1.0,
                method=SimilarityMethod.EXACT_HASH,
                duplicate_type=DuplicateType.EXACT_DUPLICATE,
                confidence=1.0,
                metadata={"hash": fp1.exact_hash}
            )
            matches.append(match)
        
        # Fuzzy hash matching
        if self.config.enable_fuzzy_matching:
            fuzzy_similarity = self._calculate_fuzzy_similarity(fp1.fuzzy_hash, fp2.fuzzy_hash)
            if fuzzy_similarity >= self.config.fuzzy_threshold:
                match = DuplicateMatch(
                    article_id_1=fp1.article_id,
                    article_id_2=fp2.article_id,
                    similarity_score=fuzzy_similarity,
                    method=SimilarityMethod.FUZZY_HASH,
                    duplicate_type=DuplicateType.NEAR_DUPLICATE,
                    confidence=fuzzy_similarity,
                    metadata={"fuzzy_score": fuzzy_similarity}
                )
                matches.append(match)
        
        # Title similarity
        if fp1.title_hash == fp2.title_hash:
            match = DuplicateMatch(
                article_id_1=fp1.article_id,
                article_id_2=fp2.article_id,
                similarity_score=1.0,
                method=SimilarityMethod.TITLE_SIMILARITY,
                duplicate_type=DuplicateType.TITLE_DUPLICATE,
                confidence=0.8,  # Lower confidence for title-only matches
                metadata={"title_hash": fp1.title_hash}
            )
            matches.append(match)
        
        # TF-IDF cosine similarity
        if self.config.enable_tfidf_matching:
            tfidf_similarity = await self._calculate_tfidf_similarity(fp1, fp2)
            if tfidf_similarity >= self.config.tfidf_threshold:
                match = DuplicateMatch(
                    article_id_1=fp1.article_id,
                    article_id_2=fp2.article_id,
                    similarity_score=tfidf_similarity,
                    method=SimilarityMethod.TFIDF_COSINE,
                    duplicate_type=DuplicateType.SEMANTIC_DUPLICATE,
                    confidence=tfidf_similarity,
                    metadata={"tfidf_score": tfidf_similarity}
                )
                matches.append(match)
        
        return matches
    
    def _calculate_fuzzy_similarity(self, hash1: str, hash2: str) -> float:
        """Calculate similarity between fuzzy hashes."""
        if hash1 == hash2:
            return 1.0
        
        # Simple character-based similarity
        common_chars = sum(1 for c1, c2 in zip(hash1, hash2) if c1 == c2)
        return common_chars / max(len(hash1), len(hash2))
    
    async def _calculate_tfidf_similarity(self, fp1: ContentFingerprint, fp2: ContentFingerprint) -> float:
        """Calculate TF-IDF cosine similarity between fingerprints."""
        try:
            # Combine content tokens
            content1 = ' '.join(fp1.content_tokens)
            content2 = ' '.join(fp2.content_tokens)
            
            if not content1 or not content2:
                return 0.0
            
            # Fit vectorizer if not already fitted
            if not self.tfidf_fitted:
                all_content = [' '.join(fp.content_tokens) for fp in self.fingerprints.values()]
                all_content.extend([content1, content2])
                
                if len(all_content) >= 2:
                    self.tfidf_vectorizer.fit(all_content)
                    self.tfidf_fitted = True
                else:
                    return 0.0
            
            # Transform content to TF-IDF vectors
            vectors = self.tfidf_vectorizer.transform([content1, content2])
            
            # Calculate cosine similarity
            similarity_matrix = cosine_similarity(vectors)
            return float(similarity_matrix[0, 1])
            
        except Exception as e:
            logger.warning(f"TF-IDF similarity calculation failed: {e}")
            return 0.0
    
    async def _cleanup_old_fingerprints(self):
        """Clean up old fingerprints to manage memory usage."""
        if len(self.fingerprints) <= self.config.max_fingerprints_memory:
            return
        
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=self.config.fingerprint_ttl_hours)
        
        # Remove old fingerprints
        old_ids = [
            fp_id for fp_id, fp in self.fingerprints.items()
            if fp.created_at < cutoff_time
        ]
        
        for fp_id in old_ids:
            del self.fingerprints[fp_id]
        
        logger.info(f"Cleaned up {len(old_ids)} old fingerprints")
    
    def get_stats(self) -> DeduplicationStats:
        """Get deduplication statistics."""
        return self.stats
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get service health status."""
        return {
            "service": "content_deduplication",
            "status": "healthy",
            "fingerprints_in_memory": len(self.fingerprints),
            "total_articles_processed": self.stats.total_articles_processed,
            "duplicate_detection_rate": (
                (self.stats.exact_duplicates_found + self.stats.fuzzy_duplicates_found + 
                 self.stats.tfidf_duplicates_found + self.stats.semantic_duplicates_found) /
                max(self.stats.total_articles_processed, 1)
            ),
            "last_deduplication": self.stats.last_deduplication.isoformat() if self.stats.last_deduplication else None
        }
