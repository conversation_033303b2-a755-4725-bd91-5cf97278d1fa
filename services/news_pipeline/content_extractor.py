"""
Content Extractor

Multi-strategy content extraction with fallback mechanisms for robust
article content extraction from various news sources.
"""

import asyncio
import hashlib
import logging
import re
from datetime import datetime
from typing import List, Optional, Dict, Any, Set
from urllib.parse import urlparse

import aiohttp
from bs4 import BeautifulSoup
from pydantic import BaseModel, Field

# Additional extractors
try:
    from readability import Document
    READABILITY_AVAILABLE = True
except ImportError:
    READABILITY_AVAILABLE = False
    logging.warning("readability-lxml not available. ReadabilityExtractor will be disabled.")

try:
    from newspaper import Article
    NEWSPAPER_AVAILABLE = True
except ImportError:
    NEWSPAPER_AVAILABLE = False
    logging.warning("newspaper3k not available. NewspaperExtractor will be disabled.")

try:
    from geopy.geocoders import Nominatim
    from geopy.exc import GeocoderTimedOut, GeocoderServiceError
    GEOPY_AVAILABLE = True
except ImportError:
    GEOPY_AVAILABLE = False
    logging.warning("geopy not available. Geographic location extraction will be disabled.")

logger = logging.getLogger(__name__)


class GeographicLocation(BaseModel):
    """Geographic location information."""
    name: str
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    country: Optional[str] = None
    region: Optional[str] = None
    confidence: float = Field(ge=0.0, le=1.0, default=0.0)


class ExtractedContent(BaseModel):
    """Extracted article content with metadata."""
    title: str
    content: str
    summary: Optional[str] = None
    author: Optional[str] = None
    published_date: Optional[datetime] = None
    language: Optional[str] = None
    word_count: int = 0
    extraction_method: str
    confidence_score: float = Field(ge=0.0, le=1.0)
    content_hash: str
    images: List[str] = Field(default_factory=list)
    links: List[str] = Field(default_factory=list)
    tags: List[str] = Field(default_factory=list)
    locations: List[GeographicLocation] = Field(default_factory=list)


class ContentExtractionError(Exception):
    """Content extraction specific error."""
    pass


class BaseExtractor:
    """Base class for content extractors."""
    
    def __init__(self, name: str):
        self.name = name
    
    async def extract(self, url: str, html: str) -> Optional[ExtractedContent]:
        """Extract content from HTML. Override in subclasses."""
        raise NotImplementedError
    
    def _calculate_content_hash(self, content: str) -> str:
        """Calculate SHA256 hash of content for deduplication."""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content."""
        if not text:
            return ""
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove common unwanted patterns
        text = re.sub(r'Advertisement\s*', '', text, flags=re.IGNORECASE)
        text = re.sub(r'Subscribe\s+to\s+.*', '', text, flags=re.IGNORECASE)
        text = re.sub(r'Follow\s+us\s+on\s+.*', '', text, flags=re.IGNORECASE)
        
        return text.strip()
    
    def _extract_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """Extract image URLs from soup."""
        images = []
        for img in soup.find_all('img'):
            src = img.get('src') or img.get('data-src')
            if src:
                if src.startswith('//'):
                    src = 'https:' + src
                elif src.startswith('/'):
                    parsed_base = urlparse(base_url)
                    src = f"{parsed_base.scheme}://{parsed_base.netloc}{src}"
                elif not src.startswith('http'):
                    continue
                images.append(src)
        return images[:10]  # Limit to 10 images
    
    def _extract_links(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """Extract internal links from soup."""
        links = []
        base_domain = urlparse(base_url).netloc
        
        for link in soup.find_all('a', href=True):
            href = link['href']
            if href.startswith('/'):
                parsed_base = urlparse(base_url)
                href = f"{parsed_base.scheme}://{parsed_base.netloc}{href}"
            
            if href.startswith('http') and base_domain in href:
                links.append(href)
        
        return list(set(links))[:20]  # Limit to 20 unique links


class BeautifulSoupExtractor(BaseExtractor):
    """BeautifulSoup-based content extractor."""
    
    def __init__(self):
        super().__init__("BeautifulSoup")
    
    async def extract(self, url: str, html: str) -> Optional[ExtractedContent]:
        """Extract content using BeautifulSoup with heuristics."""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # Remove unwanted elements
            for element in soup(['script', 'style', 'nav', 'header', 'footer', 'aside']):
                element.decompose()
            
            # Extract title
            title = self._extract_title(soup)
            if not title:
                return None
            
            # Extract main content
            content = self._extract_main_content(soup)
            if not content or len(content) < 100:
                return None
            
            # Extract metadata
            author = self._extract_author(soup)
            published_date = self._extract_published_date(soup)
            language = self._extract_language(soup)
            
            # Extract media
            images = self._extract_images(soup, url)
            links = self._extract_links(soup, url)
            
            # Calculate metrics
            word_count = len(content.split())
            content_hash = self._calculate_content_hash(content)
            confidence_score = self._calculate_confidence(title, content, author)
            
            return ExtractedContent(
                title=title,
                content=content,
                author=author,
                published_date=published_date,
                language=language,
                word_count=word_count,
                extraction_method=self.name,
                confidence_score=confidence_score,
                content_hash=content_hash,
                images=images,
                links=links
            )
        
        except Exception as e:
            logger.error(f"BeautifulSoup extraction failed for {url}: {str(e)}")
            return None
    
    def _extract_title(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract article title."""
        # Try various title selectors
        selectors = [
            'h1.entry-title',
            'h1.post-title',
            'h1.article-title',
            'h1[class*="title"]',
            '.headline h1',
            'article h1',
            'h1'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                title = self._clean_text(element.get_text())
                if title and len(title) > 10:
                    return title
        
        # Fallback to page title
        title_tag = soup.find('title')
        if title_tag:
            title = self._clean_text(title_tag.get_text())
            # Remove site name from title
            title = re.sub(r'\s*[-|]\s*.*$', '', title)
            return title
        
        return None
    
    def _extract_main_content(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract main article content."""
        # Try various content selectors
        selectors = [
            'article .entry-content',
            'article .post-content',
            'article .content',
            '.article-body',
            '.post-body',
            '.entry-content',
            '.content-body',
            'article',
            '.main-content'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                # Remove unwanted sub-elements
                for unwanted in element(['script', 'style', 'aside', '.advertisement']):
                    unwanted.decompose()
                
                content = self._clean_text(element.get_text())
                if content and len(content) > 200:
                    return content
        
        # Fallback: find largest text block
        paragraphs = soup.find_all('p')
        if paragraphs:
            content = ' '.join([self._clean_text(p.get_text()) for p in paragraphs])
            if len(content) > 200:
                return content
        
        return None
    
    def _extract_author(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract article author."""
        selectors = [
            '.author-name',
            '.byline',
            '.author',
            '[rel="author"]',
            '.post-author',
            '.article-author'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                author = self._clean_text(element.get_text())
                if author and len(author) < 100:
                    return author
        
        return None
    
    def _extract_published_date(self, soup: BeautifulSoup) -> Optional[datetime]:
        """Extract published date."""
        # Try meta tags first
        meta_selectors = [
            'meta[property="article:published_time"]',
            'meta[name="publishdate"]',
            'meta[name="date"]',
            'meta[itemprop="datePublished"]'
        ]
        
        for selector in meta_selectors:
            element = soup.select_one(selector)
            if element:
                content = element.get('content')
                if content:
                    try:
                        return datetime.fromisoformat(content.replace('Z', '+00:00'))
                    except:
                        continue
        
        # Try time elements
        time_element = soup.find('time')
        if time_element:
            datetime_attr = time_element.get('datetime')
            if datetime_attr:
                try:
                    return datetime.fromisoformat(datetime_attr.replace('Z', '+00:00'))
                except:
                    pass
        
        return None
    
    def _extract_language(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract content language."""
        html_tag = soup.find('html')
        if html_tag:
            lang = html_tag.get('lang')
            if lang:
                return lang[:2]  # Return just the language code
        
        return None
    
    def _calculate_confidence(self, title: str, content: str, author: Optional[str]) -> float:
        """Calculate extraction confidence score."""
        score = 0.0
        
        # Title quality
        if title and len(title) > 10:
            score += 0.3
        
        # Content quality
        if content:
            if len(content) > 500:
                score += 0.4
            elif len(content) > 200:
                score += 0.2
        
        # Author presence
        if author:
            score += 0.1
        
        # Content structure indicators
        if content and ('.' in content and len(content.split('.')) > 3):
            score += 0.2
        
        return min(score, 1.0)


class ReadabilityExtractor(BaseExtractor):
    """Readability-based content extractor."""

    def __init__(self):
        super().__init__("Readability")

    async def extract(self, url: str, html: str) -> Optional[ExtractedContent]:
        """Extract content using readability-lxml."""
        if not READABILITY_AVAILABLE:
            return None

        try:
            doc = Document(html)

            # Extract title and content
            title = self._clean_text(doc.title())
            content = self._clean_text(doc.summary())

            if not title or not content or len(content) < 100:
                return None

            # Parse with BeautifulSoup for additional metadata
            soup = BeautifulSoup(content, 'html.parser')

            # Extract text content
            text_content = self._clean_text(soup.get_text())
            if len(text_content) < 100:
                return None

            # Calculate metrics
            word_count = len(text_content.split())
            content_hash = self._calculate_content_hash(text_content)
            confidence_score = self._calculate_readability_confidence(title, text_content)

            # Extract images and links from the cleaned content
            images = self._extract_images(soup, url)
            links = self._extract_links(soup, url)

            return ExtractedContent(
                title=title,
                content=text_content,
                word_count=word_count,
                extraction_method=self.name,
                confidence_score=confidence_score,
                content_hash=content_hash,
                images=images,
                links=links
            )

        except Exception as e:
            logger.error(f"Readability extraction failed for {url}: {str(e)}")
            return None

    def _calculate_readability_confidence(self, title: str, content: str) -> float:
        """Calculate confidence score for readability extraction."""
        score = 0.0

        # Title quality
        if title and len(title) > 10:
            score += 0.3

        # Content quality - readability usually produces cleaner content
        if content:
            if len(content) > 800:
                score += 0.5
            elif len(content) > 300:
                score += 0.3

        # Content structure
        if content and ('.' in content and len(content.split('.')) > 5):
            score += 0.2

        return min(score, 1.0)


class NewspaperExtractor(BaseExtractor):
    """Newspaper3k-based content extractor."""

    def __init__(self):
        super().__init__("Newspaper")

    async def extract(self, url: str, html: str) -> Optional[ExtractedContent]:
        """Extract content using newspaper3k."""
        if not NEWSPAPER_AVAILABLE:
            return None

        try:
            article = Article(url)
            article.set_html(html)
            article.parse()

            # Extract basic content
            title = self._clean_text(article.title)
            content = self._clean_text(article.text)
            summary = self._clean_text(article.summary) if hasattr(article, 'summary') else None

            if not title or not content or len(content) < 100:
                return None

            # Extract metadata
            authors = article.authors if article.authors else []
            author = authors[0] if authors else None
            published_date = article.publish_date

            # Calculate metrics
            word_count = len(content.split())
            content_hash = self._calculate_content_hash(content)
            confidence_score = self._calculate_newspaper_confidence(title, content, author, published_date)

            # Extract media
            images = list(article.images) if article.images else []

            return ExtractedContent(
                title=title,
                content=content,
                summary=summary,
                author=author,
                published_date=published_date,
                word_count=word_count,
                extraction_method=self.name,
                confidence_score=confidence_score,
                content_hash=content_hash,
                images=images[:10]  # Limit to 10 images
            )

        except Exception as e:
            logger.error(f"Newspaper extraction failed for {url}: {str(e)}")
            return None

    def _calculate_newspaper_confidence(self, title: str, content: str, author: Optional[str], published_date: Optional[datetime]) -> float:
        """Calculate confidence score for newspaper extraction."""
        score = 0.0

        # Title quality
        if title and len(title) > 10:
            score += 0.2

        # Content quality
        if content:
            if len(content) > 500:
                score += 0.3
            elif len(content) > 200:
                score += 0.2

        # Metadata presence
        if author:
            score += 0.2
        if published_date:
            score += 0.2

        # Content structure
        if content and ('.' in content and len(content.split('.')) > 3):
            score += 0.1

        return min(score, 1.0)


class GeographicExtractor:
    """Geographic location extractor."""

    def __init__(self):
        self.geocoder = Nominatim(user_agent="StarPower/1.0") if GEOPY_AVAILABLE else None
        # Common location patterns
        self.location_patterns = [
            r'\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*),\s*([A-Z]{2})\b',  # City, State
            r'\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*),\s*([A-Z][a-z]+)\b',  # City, Country
            r'\bin\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b',  # in Location
            r'\bfrom\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b',  # from Location
        ]

    async def extract_locations(self, text: str) -> List[GeographicLocation]:
        """Extract geographic locations from text."""
        if not GEOPY_AVAILABLE or not self.geocoder:
            return []

        locations = []
        found_names = set()

        try:
            # Extract potential location names using patterns
            for pattern in self.location_patterns:
                matches = re.finditer(pattern, text)
                for match in matches:
                    location_name = match.group(1).strip()
                    if len(location_name) > 2 and location_name not in found_names:
                        found_names.add(location_name)

            # Geocode found locations
            for location_name in list(found_names)[:5]:  # Limit to 5 locations to avoid rate limits
                try:
                    location_data = await self._geocode_location(location_name)
                    if location_data:
                        locations.append(location_data)
                except Exception as e:
                    logger.warning(f"Failed to geocode {location_name}: {str(e)}")
                    continue

        except Exception as e:
            logger.error(f"Geographic extraction failed: {str(e)}")

        return locations

    async def _geocode_location(self, location_name: str) -> Optional[GeographicLocation]:
        """Geocode a location name."""
        try:
            # Use asyncio to run the blocking geocoder in a thread
            location = await asyncio.get_event_loop().run_in_executor(
                None, self.geocoder.geocode, location_name
            )

            if location:
                # Extract country and region from address
                address_parts = location.address.split(', ') if location.address else []
                country = address_parts[-1] if address_parts else None
                region = address_parts[-2] if len(address_parts) > 1 else None

                return GeographicLocation(
                    name=location_name,
                    latitude=location.latitude,
                    longitude=location.longitude,
                    country=country,
                    region=region,
                    confidence=0.8  # High confidence for successful geocoding
                )

        except (GeocoderTimedOut, GeocoderServiceError) as e:
            logger.warning(f"Geocoding service error for {location_name}: {str(e)}")
        except Exception as e:
            logger.error(f"Geocoding failed for {location_name}: {str(e)}")

        return None


class ContentExtractor:
    """Multi-strategy content extractor with fallback mechanisms."""
    
    def __init__(self, timeout: int = 30, enable_geographic_extraction: bool = True):
        """
        Initialize content extractor.

        Args:
            timeout: Request timeout in seconds
            enable_geographic_extraction: Whether to extract geographic locations
        """
        self.timeout = timeout
        self.enable_geographic_extraction = enable_geographic_extraction

        # Initialize extractors in order of preference
        self.extractors = []

        # Add newspaper extractor first (usually most accurate)
        if NEWSPAPER_AVAILABLE:
            self.extractors.append(NewspaperExtractor())

        # Add readability extractor second
        if READABILITY_AVAILABLE:
            self.extractors.append(ReadabilityExtractor())

        # Add BeautifulSoup as fallback
        self.extractors.append(BeautifulSoupExtractor())

        # Initialize geographic extractor
        self.geo_extractor = GeographicExtractor() if enable_geographic_extraction else None

        self.session: Optional[aiohttp.ClientSession] = None
        self._content_cache: Dict[str, str] = {}  # Simple in-memory cache

        logger.info(f"ContentExtractor initialized with {len(self.extractors)} extractors: "
                   f"{[e.name for e in self.extractors]}")
        if self.geo_extractor:
            logger.info("Geographic location extraction enabled")
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout),
            headers={'User-Agent': 'Mozilla/5.0 (compatible; StarPower/1.0; +https://starpower.ai)'}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def extract_content(self, url: str) -> Optional[ExtractedContent]:
        """
        Extract content from URL using multiple strategies.

        Args:
            url: URL to extract content from

        Returns:
            Extracted content or None if extraction fails
        """
        try:
            # Fetch HTML content
            html = await self._fetch_html(url)
            if not html:
                return None

            # Try each extractor until one succeeds
            best_result = None
            best_confidence = 0.0

            for extractor in self.extractors:
                try:
                    result = await extractor.extract(url, html)
                    if result and result.confidence_score > best_confidence:
                        best_result = result
                        best_confidence = result.confidence_score

                        # If we get a high-confidence result, use it immediately
                        if result.confidence_score > 0.8:
                            logger.info(f"High-confidence extraction from {url} using {extractor.name}")
                            break

                except Exception as e:
                    logger.warning(f"Extractor {extractor.name} failed for {url}: {str(e)}")
                    continue

            # If we have a result, enhance it with geographic locations
            if best_result and best_result.confidence_score > 0.3:
                if self.geo_extractor and self.enable_geographic_extraction:
                    try:
                        locations = await self.geo_extractor.extract_locations(best_result.content)
                        best_result.locations = locations
                        logger.debug(f"Extracted {len(locations)} geographic locations from {url}")
                    except Exception as e:
                        logger.warning(f"Geographic extraction failed for {url}: {str(e)}")

                logger.info(f"Successfully extracted content from {url} using {best_result.extraction_method} "
                           f"(confidence: {best_result.confidence_score:.2f})")
                return best_result

            logger.warning(f"All extractors failed or produced low-confidence results for {url}")
            return None

        except Exception as e:
            logger.error(f"Content extraction failed for {url}: {str(e)}")
            return None
    
    async def _fetch_html(self, url: str) -> Optional[str]:
        """Fetch HTML content from URL."""
        # Check cache first
        if url in self._content_cache:
            return self._content_cache[url]
        
        try:
            if not self.session:
                raise Exception("Session not initialized. Use async context manager.")
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    
                    # Cache the content (simple in-memory cache)
                    if len(self._content_cache) < 100:  # Limit cache size
                        self._content_cache[url] = html
                    
                    return html
                else:
                    logger.warning(f"HTTP {response.status} for {url}")
                    return None
        
        except Exception as e:
            logger.error(f"Failed to fetch {url}: {str(e)}")
            return None
    
    def is_duplicate_content(self, content_hash: str, existing_hashes: Set[str]) -> bool:
        """Check if content is duplicate based on hash."""
        return content_hash in existing_hashes
    
    async def extract_content_batch(self, urls: List[str], max_concurrent: int = 5) -> List[Optional[ExtractedContent]]:
        """
        Extract content from multiple URLs concurrently.

        Args:
            urls: List of URLs to extract content from
            max_concurrent: Maximum number of concurrent extractions

        Returns:
            List of extracted content (None for failed extractions)
        """
        semaphore = asyncio.Semaphore(max_concurrent)

        async def extract_with_semaphore(url: str) -> Optional[ExtractedContent]:
            async with semaphore:
                return await self.extract_content(url)

        logger.info(f"Starting batch extraction for {len(urls)} URLs with max_concurrent={max_concurrent}")

        tasks = [extract_with_semaphore(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Convert exceptions to None
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Batch extraction failed for {urls[i]}: {str(result)}")
                processed_results.append(None)
            else:
                processed_results.append(result)

        successful = sum(1 for r in processed_results if r is not None)
        logger.info(f"Batch extraction completed: {successful}/{len(urls)} successful")

        return processed_results

    def get_extractor_stats(self) -> Dict[str, Any]:
        """Get statistics about available extractors."""
        return {
            "available_extractors": [e.name for e in self.extractors],
            "total_extractors": len(self.extractors),
            "geographic_extraction_enabled": self.enable_geographic_extraction,
            "cache_size": len(self._content_cache),
            "dependencies": {
                "readability": READABILITY_AVAILABLE,
                "newspaper": NEWSPAPER_AVAILABLE,
                "geopy": GEOPY_AVAILABLE
            }
        }

    def clear_cache(self):
        """Clear the content cache."""
        self._content_cache.clear()
        logger.info("Content cache cleared")
