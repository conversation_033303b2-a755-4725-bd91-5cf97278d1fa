"""
GNews API Client

Professional GNews API integration with circuit breaker, rate limiting,
and comprehensive error handling for reliable news article fetching.
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum

import aiohttp
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class GNewsLanguage(str, Enum):
    """Supported GNews languages."""
    ENGLISH = "en"
    SPANISH = "es"
    FRENCH = "fr"
    GERMAN = "de"
    ITALIAN = "it"
    PORTUGUESE = "pt"


class GNewsCountry(str, Enum):
    """Supported GNews countries."""
    US = "US"
    UK = "GB"
    CANADA = "CA"
    AUSTRALIA = "AU"
    GERMANY = "DE"
    FRANCE = "FR"
    SPAIN = "ES"
    ITALY = "IT"


@dataclass
class RateLimiter:
    """Simple rate limiter for API calls."""
    calls: int
    period: int  # seconds
    
    def __post_init__(self):
        self.call_times = []
    
    async def acquire(self):
        """Wait if necessary to respect rate limits."""
        now = time.time()
        
        # Remove old calls outside the period
        self.call_times = [t for t in self.call_times if now - t < self.period]
        
        # If we're at the limit, wait
        if len(self.call_times) >= self.calls:
            sleep_time = self.period - (now - self.call_times[0])
            if sleep_time > 0:
                logger.info(f"Rate limit reached, sleeping for {sleep_time:.2f} seconds")
                await asyncio.sleep(sleep_time)
        
        # Record this call
        self.call_times.append(now)


@dataclass
class CircuitBreaker:
    """Circuit breaker for API resilience."""
    failure_threshold: int = 5
    recovery_timeout: int = 60
    
    def __post_init__(self):
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def can_execute(self) -> bool:
        """Check if we can execute a request."""
        if self.state == "CLOSED":
            return True
        elif self.state == "OPEN":
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = "HALF_OPEN"
                return True
            return False
        else:  # HALF_OPEN
            return True
    
    def record_success(self):
        """Record a successful request."""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def record_failure(self):
        """Record a failed request."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"
            logger.warning(f"Circuit breaker opened after {self.failure_count} failures")


class GNewsConfig(BaseModel):
    """GNews API configuration."""
    api_key: Optional[str] = Field(None, description="GNews API key")
    base_url: str = Field("https://gnews.io/api/v4", description="GNews API base URL")
    default_language: GNewsLanguage = Field(GNewsLanguage.ENGLISH, description="Default language")
    default_country: GNewsCountry = Field(GNewsCountry.US, description="Default country")
    max_articles: int = Field(10, ge=1, le=100, description="Maximum articles per request")
    timeout: int = Field(30, ge=5, le=120, description="Request timeout in seconds")
    retry_attempts: int = Field(3, ge=1, le=5, description="Number of retry attempts")
    retry_delay: float = Field(1.0, ge=0.1, le=10.0, description="Delay between retries")


class GNewsArticle(BaseModel):
    """GNews article data model."""
    title: str
    description: str
    content: str
    url: str
    image: Optional[str] = None
    publishedAt: str
    source: Dict[str, str]


class GNewsResponse(BaseModel):
    """GNews API response model."""
    totalArticles: int
    articles: List[GNewsArticle]


class GNewsClient:
    """Professional GNews API client with resilience features."""
    
    def __init__(self, config: GNewsConfig):
        """
        Initialize GNews client.
        
        Args:
            config: GNews configuration
        """
        self.config = config
        self.rate_limiter = RateLimiter(calls=100, period=3600)  # 100 calls per hour
        self.circuit_breaker = CircuitBreaker()
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config.timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def search_articles(
        self,
        query: str,
        language: Optional[GNewsLanguage] = None,
        country: Optional[GNewsCountry] = None,
        max_articles: Optional[int] = None,
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None
    ) -> List[GNewsArticle]:
        """
        Search for articles using GNews API.
        
        Args:
            query: Search query
            language: Language filter
            country: Country filter
            max_articles: Maximum number of articles
            from_date: Start date filter
            to_date: End date filter
            
        Returns:
            List of articles
            
        Raises:
            Exception: If API request fails after retries
        """
        if not self.circuit_breaker.can_execute():
            raise Exception("Circuit breaker is open, service unavailable")
        
        # Wait for rate limiter
        await self.rate_limiter.acquire()
        
        # Build request parameters
        params = {
            "q": query,
            "lang": (language or self.config.default_language).value,
            "country": (country or self.config.default_country).value,
            "max": min(max_articles or self.config.max_articles, 100),
            "token": self.config.api_key
        }
        
        # Add date filters if provided
        if from_date:
            params["from"] = from_date.strftime("%Y-%m-%dT%H:%M:%SZ")
        if to_date:
            params["to"] = to_date.strftime("%Y-%m-%dT%H:%M:%SZ")
        
        # Make request with retries
        for attempt in range(self.config.retry_attempts):
            try:
                if not self.session:
                    raise Exception("Client session not initialized. Use async context manager.")
                
                url = f"{self.config.base_url}/search"
                
                async with self.session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        gnews_response = GNewsResponse(**data)
                        
                        self.circuit_breaker.record_success()
                        logger.info(f"Successfully fetched {len(gnews_response.articles)} articles for query: {query}")
                        
                        return gnews_response.articles
                    
                    elif response.status == 429:
                        # Rate limited
                        logger.warning(f"Rate limited by GNews API (attempt {attempt + 1})")
                        if attempt < self.config.retry_attempts - 1:
                            await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
                            continue
                    
                    else:
                        error_text = await response.text()
                        logger.error(f"GNews API error {response.status}: {error_text}")
                        raise Exception(f"GNews API error: {response.status}")
            
            except asyncio.TimeoutError:
                logger.warning(f"Request timeout (attempt {attempt + 1})")
                if attempt < self.config.retry_attempts - 1:
                    await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
                    continue
                raise Exception("Request timeout after retries")
            
            except Exception as e:
                logger.error(f"Request failed (attempt {attempt + 1}): {str(e)}")
                if attempt < self.config.retry_attempts - 1:
                    await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
                    continue
                
                self.circuit_breaker.record_failure()
                raise
        
        raise Exception("All retry attempts failed")
    
    async def get_top_headlines(
        self,
        category: Optional[str] = None,
        language: Optional[GNewsLanguage] = None,
        country: Optional[GNewsCountry] = None,
        max_articles: Optional[int] = None
    ) -> List[GNewsArticle]:
        """
        Get top headlines.
        
        Args:
            category: News category (business, entertainment, general, health, science, sports, technology)
            language: Language filter
            country: Country filter
            max_articles: Maximum number of articles
            
        Returns:
            List of top headline articles
        """
        if not self.circuit_breaker.can_execute():
            raise Exception("Circuit breaker is open, service unavailable")
        
        await self.rate_limiter.acquire()
        
        params = {
            "lang": (language or self.config.default_language).value,
            "country": (country or self.config.default_country).value,
            "max": min(max_articles or self.config.max_articles, 100),
            "token": self.config.api_key
        }
        
        if category:
            params["category"] = category
        
        for attempt in range(self.config.retry_attempts):
            try:
                if not self.session:
                    raise Exception("Client session not initialized. Use async context manager.")
                
                url = f"{self.config.base_url}/top-headlines"
                
                async with self.session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        gnews_response = GNewsResponse(**data)
                        
                        self.circuit_breaker.record_success()
                        logger.info(f"Successfully fetched {len(gnews_response.articles)} top headlines")
                        
                        return gnews_response.articles
                    
                    else:
                        error_text = await response.text()
                        logger.error(f"GNews API error {response.status}: {error_text}")
                        raise Exception(f"GNews API error: {response.status}")
            
            except Exception as e:
                logger.error(f"Request failed (attempt {attempt + 1}): {str(e)}")
                if attempt < self.config.retry_attempts - 1:
                    await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
                    continue
                
                self.circuit_breaker.record_failure()
                raise
        
        raise Exception("All retry attempts failed")
    
    def get_circuit_breaker_status(self) -> Dict[str, Any]:
        """Get circuit breaker status for monitoring."""
        return {
            "state": self.circuit_breaker.state,
            "failure_count": self.circuit_breaker.failure_count,
            "last_failure_time": self.circuit_breaker.last_failure_time
        }
    
    def get_rate_limiter_status(self) -> Dict[str, Any]:
        """Get rate limiter status for monitoring."""
        now = time.time()
        recent_calls = [t for t in self.rate_limiter.call_times if now - t < self.rate_limiter.period]
        
        return {
            "calls_in_period": len(recent_calls),
            "max_calls": self.rate_limiter.calls,
            "period_seconds": self.rate_limiter.period,
            "remaining_calls": max(0, self.rate_limiter.calls - len(recent_calls))
        }
