"""
Multi-Source News Client

Modern news aggregation client supporting multiple news APIs with intelligent
source selection, content deduplication, and quality filtering.
"""

import asyncio
import logging
import hashlib
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any, Set, Union
from enum import Enum
from dataclasses import dataclass, field

from pydantic import BaseModel, Field

from .gnews_client import GNewsClient, GNewsConfig, GNewsArticle
from .content_extractor import ContentExtractor, ExtractedContent

logger = logging.getLogger(__name__)


class NewsSource(str, Enum):
    """Supported news sources."""
    GNEWS = "gnews"
    NEWSAPI = "newsapi"
    RSS_FEEDS = "rss_feeds"
    REDDIT = "reddit"


class ContentQuality(str, Enum):
    """Content quality levels."""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    SPAM = "spam"


@dataclass
class NewsSourceConfig:
    """Configuration for a news source."""
    source: NewsSource
    enabled: bool = True
    api_key: Optional[str] = None
    rate_limit: int = 100  # requests per hour
    priority: int = 1  # 1 = highest priority
    quality_threshold: float = 0.7
    timeout: int = 30


@dataclass
class QualityMetrics:
    """Content quality assessment metrics."""
    content_length: int = 0
    readability_score: float = 0.0
    source_credibility: float = 0.0
    freshness_score: float = 0.0
    engagement_score: float = 0.0
    overall_quality: float = 0.0
    quality_level: ContentQuality = ContentQuality.MEDIUM


class UnifiedNewsArticle(BaseModel):
    """Unified news article format across all sources."""
    id: str = Field(..., description="Unique article identifier")
    title: str = Field(..., description="Article title")
    description: Optional[str] = Field(None, description="Article description/summary")
    content: Optional[str] = Field(None, description="Full article content")
    url: str = Field(..., description="Article URL")
    source_name: str = Field(..., description="News source name")
    source_type: NewsSource = Field(..., description="Source type")
    published_at: datetime = Field(..., description="Publication timestamp")
    author: Optional[str] = Field(None, description="Article author")
    image_url: Optional[str] = Field(None, description="Article image URL")
    
    # Quality and processing metadata
    quality_metrics: Optional[QualityMetrics] = Field(None, description="Quality assessment")
    content_hash: Optional[str] = Field(None, description="Content hash for deduplication")
    extracted_content: Optional[ExtractedContent] = Field(None, description="Extracted content")
    
    # Geographic and entity data
    locations: List[str] = Field(default_factory=list, description="Mentioned locations")
    entities: List[str] = Field(default_factory=list, description="Named entities")
    keywords: List[str] = Field(default_factory=list, description="Content keywords")


class MultiSourceNewsConfig(BaseModel):
    """Configuration for multi-source news client."""
    sources: List[NewsSourceConfig] = Field(default_factory=list)
    enable_content_extraction: bool = Field(True, description="Enable full content extraction")
    enable_quality_filtering: bool = Field(True, description="Enable quality filtering")
    enable_deduplication: bool = Field(True, description="Enable content deduplication")
    min_quality_score: float = Field(0.5, ge=0.0, le=1.0, description="Minimum quality score")
    max_articles_per_source: int = Field(50, ge=1, le=200, description="Max articles per source")
    content_extraction_timeout: int = Field(30, ge=5, le=120, description="Content extraction timeout")
    deduplication_similarity_threshold: float = Field(0.85, ge=0.5, le=1.0, description="Deduplication threshold")


class NewsAggregationStats(BaseModel):
    """Statistics for news aggregation operations."""
    total_articles_fetched: int = 0
    articles_by_source: Dict[str, int] = Field(default_factory=dict)
    articles_filtered_quality: int = 0
    articles_filtered_duplicates: int = 0
    articles_processed: int = 0
    average_quality_score: float = 0.0
    processing_time_seconds: float = 0.0
    last_aggregation: Optional[datetime] = None


class ContentQualityAssessor:
    """Assesses content quality using multiple metrics."""
    
    def __init__(self):
        """Initialize quality assessor."""
        self.spam_keywords = {
            'click here', 'buy now', 'limited time', 'act fast', 'free money',
            'get rich quick', 'miracle cure', 'lose weight fast', 'make money fast'
        }
        
        self.credible_domains = {
            'reuters.com', 'ap.org', 'bbc.com', 'cnn.com', 'nytimes.com',
            'washingtonpost.com', 'theguardian.com', 'wsj.com', 'npr.org',
            'abcnews.go.com', 'cbsnews.com', 'nbcnews.com', 'usatoday.com'
        }
    
    def assess_quality(self, article: UnifiedNewsArticle) -> QualityMetrics:
        """Assess article quality using multiple metrics."""
        metrics = QualityMetrics()
        
        # Content length assessment
        content = article.content or article.description or ""
        metrics.content_length = len(content)
        length_score = min(1.0, len(content) / 1000)  # Normalize to 1000 chars
        
        # Readability assessment (simplified)
        metrics.readability_score = self._assess_readability(content)
        
        # Source credibility
        metrics.source_credibility = self._assess_source_credibility(article.url, article.source_name)
        
        # Freshness score
        metrics.freshness_score = self._assess_freshness(article.published_at)
        
        # Engagement score (simplified - based on title and description quality)
        metrics.engagement_score = self._assess_engagement(article.title, article.description)
        
        # Calculate overall quality (weighted average)
        weights = {
            'length': 0.15,
            'readability': 0.25,
            'credibility': 0.30,
            'freshness': 0.15,
            'engagement': 0.15
        }
        
        metrics.overall_quality = (
            length_score * weights['length'] +
            metrics.readability_score * weights['readability'] +
            metrics.source_credibility * weights['credibility'] +
            metrics.freshness_score * weights['freshness'] +
            metrics.engagement_score * weights['engagement']
        )
        
        # Determine quality level
        if metrics.overall_quality >= 0.8:
            metrics.quality_level = ContentQuality.HIGH
        elif metrics.overall_quality >= 0.6:
            metrics.quality_level = ContentQuality.MEDIUM
        elif metrics.overall_quality >= 0.3:
            metrics.quality_level = ContentQuality.LOW
        else:
            metrics.quality_level = ContentQuality.SPAM
        
        return metrics
    
    def _assess_readability(self, content: str) -> float:
        """Assess content readability (simplified)."""
        if not content:
            return 0.0
        
        # Simple readability metrics
        sentences = content.count('.') + content.count('!') + content.count('?')
        words = len(content.split())
        
        if sentences == 0 or words == 0:
            return 0.0
        
        avg_sentence_length = words / sentences
        
        # Penalize very short or very long sentences
        if avg_sentence_length < 5 or avg_sentence_length > 30:
            return 0.4
        
        # Check for spam keywords
        content_lower = content.lower()
        spam_count = sum(1 for keyword in self.spam_keywords if keyword in content_lower)
        spam_penalty = min(0.5, spam_count * 0.1)
        
        base_score = 0.8 if 10 <= avg_sentence_length <= 20 else 0.6
        return max(0.0, base_score - spam_penalty)
    
    def _assess_source_credibility(self, url: str, source_name: str) -> float:
        """Assess source credibility."""
        from urllib.parse import urlparse
        
        try:
            domain = urlparse(url).netloc.lower()
            
            # Remove www. prefix
            if domain.startswith('www.'):
                domain = domain[4:]
            
            # Check against credible domains
            if domain in self.credible_domains:
                return 0.9
            
            # Check for common news indicators
            news_indicators = ['news', 'times', 'post', 'herald', 'tribune', 'journal']
            if any(indicator in domain for indicator in news_indicators):
                return 0.7
            
            # Check for suspicious indicators
            suspicious_indicators = ['blog', 'wordpress', 'tumblr', 'medium']
            if any(indicator in domain for indicator in suspicious_indicators):
                return 0.4
            
            return 0.6  # Default for unknown sources
            
        except Exception:
            return 0.5
    
    def _assess_freshness(self, published_at: datetime) -> float:
        """Assess content freshness."""
        try:
            now = datetime.now(timezone.utc)
            if published_at.tzinfo is None:
                published_at = published_at.replace(tzinfo=timezone.utc)
            
            age_hours = (now - published_at).total_seconds() / 3600
            
            # Fresher content gets higher scores
            if age_hours <= 1:
                return 1.0
            elif age_hours <= 6:
                return 0.9
            elif age_hours <= 24:
                return 0.8
            elif age_hours <= 72:
                return 0.6
            elif age_hours <= 168:  # 1 week
                return 0.4
            else:
                return 0.2
                
        except Exception:
            return 0.5
    
    def _assess_engagement(self, title: str, description: Optional[str]) -> float:
        """Assess potential engagement (simplified)."""
        if not title:
            return 0.0
        
        score = 0.5  # Base score
        
        # Title length assessment
        title_words = len(title.split())
        if 5 <= title_words <= 15:
            score += 0.2
        
        # Check for engaging elements
        engaging_words = ['breaking', 'exclusive', 'revealed', 'shocking', 'amazing']
        if any(word in title.lower() for word in engaging_words):
            score += 0.1
        
        # Check for clickbait (penalty)
        clickbait_words = ['you won\'t believe', 'shocking truth', 'doctors hate']
        if any(phrase in title.lower() for phrase in clickbait_words):
            score -= 0.3
        
        # Description quality
        if description and len(description) > 50:
            score += 0.2
        
        return max(0.0, min(1.0, score))


class MultiSourceNewsClient:
    """
    Modern multi-source news client with intelligent aggregation,
    quality filtering, and content deduplication.
    """
    
    def __init__(self, config: MultiSourceNewsConfig):
        """
        Initialize multi-source news client.
        
        Args:
            config: Multi-source news configuration
        """
        self.config = config
        self.stats = NewsAggregationStats()
        self.quality_assessor = ContentQualityAssessor()
        self.content_extractor = ContentExtractor()
        
        # Initialize source clients
        self.source_clients = {}
        self._initialize_source_clients()
        
        # Deduplication tracking
        self.processed_hashes: Set[str] = set()
        
        logger.info(f"Initialized multi-source news client with {len(self.source_clients)} sources")
    
    def _initialize_source_clients(self):
        """Initialize clients for enabled news sources."""
        for source_config in self.config.sources:
            if not source_config.enabled:
                continue
            
            try:
                if source_config.source == NewsSource.GNEWS:
                    gnews_config = GNewsConfig(
                        api_key=source_config.api_key,
                        timeout=source_config.timeout
                    )
                    self.source_clients[NewsSource.GNEWS] = GNewsClient(gnews_config)
                    logger.info("Initialized GNews client")
                
                # Additional source clients would be initialized here
                # elif source_config.source == NewsSource.NEWSAPI:
                #     self.source_clients[NewsSource.NEWSAPI] = NewsAPIClient(...)
                
            except Exception as e:
                logger.error(f"Failed to initialize {source_config.source} client: {e}")
    
    async def aggregate_news(self, query: str, max_articles: Optional[int] = None) -> List[UnifiedNewsArticle]:
        """
        Aggregate news from multiple sources with quality filtering and deduplication.
        
        Args:
            query: Search query
            max_articles: Maximum number of articles to return
            
        Returns:
            List of unified news articles
        """
        start_time = datetime.now()
        max_articles = max_articles or self.config.max_articles_per_source
        
        logger.info(f"Starting news aggregation for query: '{query}'")
        
        # Fetch from all sources
        all_articles = []
        for source, client in self.source_clients.items():
            try:
                source_articles = await self._fetch_from_source(source, client, query, max_articles)
                all_articles.extend(source_articles)
                self.stats.articles_by_source[source.value] = len(source_articles)
                
            except Exception as e:
                logger.error(f"Failed to fetch from {source}: {e}")
        
        self.stats.total_articles_fetched = len(all_articles)
        
        # Apply quality filtering
        if self.config.enable_quality_filtering:
            all_articles = await self._apply_quality_filtering(all_articles)
        
        # Apply deduplication
        if self.config.enable_deduplication:
            all_articles = await self._apply_deduplication(all_articles)
        
        # Sort by quality and freshness
        all_articles.sort(
            key=lambda a: (
                a.quality_metrics.overall_quality if a.quality_metrics else 0.5,
                a.published_at
            ),
            reverse=True
        )
        
        # Limit results
        if max_articles:
            all_articles = all_articles[:max_articles]
        
        # Update statistics
        self.stats.articles_processed = len(all_articles)
        self.stats.processing_time_seconds = (datetime.now() - start_time).total_seconds()
        self.stats.last_aggregation = datetime.now(timezone.utc)
        
        if all_articles:
            avg_quality = sum(
                a.quality_metrics.overall_quality if a.quality_metrics else 0.5 
                for a in all_articles
            ) / len(all_articles)
            self.stats.average_quality_score = avg_quality
        
        logger.info(f"News aggregation completed: {len(all_articles)} articles processed")
        return all_articles
    
    async def _fetch_from_source(
        self, 
        source: NewsSource, 
        client: Any, 
        query: str, 
        max_articles: int
    ) -> List[UnifiedNewsArticle]:
        """Fetch articles from a specific source."""
        articles = []
        
        try:
            if source == NewsSource.GNEWS:
                gnews_articles = await client.search_articles(query, max_articles)
                for gnews_article in gnews_articles:
                    unified_article = self._convert_gnews_article(gnews_article)
                    articles.append(unified_article)
            
            # Additional source conversions would be added here
            
        except Exception as e:
            logger.error(f"Error fetching from {source}: {e}")
        
        return articles
    
    def _convert_gnews_article(self, gnews_article: GNewsArticle) -> UnifiedNewsArticle:
        """Convert GNews article to unified format."""
        from dateutil.parser import parse

        # Parse the published date
        try:
            published_at = parse(gnews_article.publishedAt)
        except Exception:
            published_at = datetime.now(timezone.utc)

        # Extract source name
        source_name = gnews_article.source.get("name", "Unknown") if isinstance(gnews_article.source, dict) else str(gnews_article.source)

        return UnifiedNewsArticle(
            id=f"gnews_{hashlib.md5(gnews_article.url.encode()).hexdigest()[:12]}",
            title=gnews_article.title,
            description=gnews_article.description,
            content=gnews_article.content,
            url=gnews_article.url,
            source_name=source_name,
            source_type=NewsSource.GNEWS,
            published_at=published_at,
            image_url=gnews_article.image
        )
    
    async def _apply_quality_filtering(self, articles: List[UnifiedNewsArticle]) -> List[UnifiedNewsArticle]:
        """Apply quality filtering to articles."""
        filtered_articles = []
        
        for article in articles:
            # Assess quality
            quality_metrics = self.quality_assessor.assess_quality(article)
            article.quality_metrics = quality_metrics
            
            # Filter by quality threshold
            if quality_metrics.overall_quality >= self.config.min_quality_score:
                filtered_articles.append(article)
            else:
                self.stats.articles_filtered_quality += 1
        
        logger.info(f"Quality filtering: {len(filtered_articles)}/{len(articles)} articles passed")
        return filtered_articles
    
    async def _apply_deduplication(self, articles: List[UnifiedNewsArticle]) -> List[UnifiedNewsArticle]:
        """Apply content deduplication."""
        deduplicated_articles = []
        
        for article in articles:
            # Generate content hash
            content_for_hash = f"{article.title} {article.description or ''}"
            content_hash = hashlib.md5(content_for_hash.encode()).hexdigest()
            article.content_hash = content_hash
            
            # Check for duplicates
            if content_hash not in self.processed_hashes:
                self.processed_hashes.add(content_hash)
                deduplicated_articles.append(article)
            else:
                self.stats.articles_filtered_duplicates += 1
        
        logger.info(f"Deduplication: {len(deduplicated_articles)}/{len(articles)} articles unique")
        return deduplicated_articles
    
    def get_stats(self) -> NewsAggregationStats:
        """Get aggregation statistics."""
        return self.stats
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get service health status."""
        return {
            "service": "multi_source_news_client",
            "status": "healthy",
            "sources_enabled": len(self.source_clients),
            "last_aggregation": self.stats.last_aggregation.isoformat() if self.stats.last_aggregation else None,
            "articles_processed": self.stats.articles_processed,
            "average_quality_score": self.stats.average_quality_score
        }
