"""
News Ingestion Service

Orchestrates news article fetching, content extraction, and database storage
with comprehensive error handling, rate limiting, and monitoring.
"""

import asyncio
import logging
import os
from datetime import datetime, timedelta, timezone
from typing import List, Optional, Dict, Any, Set
from uuid import uuid4

from pydantic import BaseModel, Field

from .gnews_client import GNewsClient, GNewsConfig, GNewsArticle
from .content_extractor import ContentExtractor, ExtractedContent
from ..database import ArticleData, ArticleDBService, ProcessingStatus

logger = logging.getLogger(__name__)


class NewsIngestionConfig(BaseModel):
    """Configuration for news ingestion service."""
    gnews_api_key: Optional[str] = Field(None, description="GNews API key")
    max_articles_per_batch: int = Field(50, ge=1, le=100, description="Max articles per batch")
    content_extraction_timeout: int = Field(30, ge=5, le=120, description="Content extraction timeout")
    batch_processing_delay: float = Field(1.0, ge=0.1, le=10.0, description="Delay between article processing")
    enable_content_extraction: bool = Field(True, description="Enable full content extraction")
    enable_deduplication: bool = Field(True, description="Enable content deduplication")
    max_content_age_days: int = Field(7, ge=1, le=30, description="Maximum age of content to process")


class NewsIngestionStats(BaseModel):
    """Statistics for news ingestion operations."""
    articles_fetched: int = 0
    articles_processed: int = 0
    articles_stored: int = 0
    articles_skipped: int = 0
    extraction_failures: int = 0
    duplicate_articles: int = 0
    processing_time_seconds: float = 0.0
    last_run: Optional[datetime] = None


class NewsIngestionService:
    """
    News ingestion service with comprehensive article processing.
    
    Handles fetching articles from GNews, extracting full content,
    deduplication, and database storage with error handling.
    """
    
    def __init__(self, config: NewsIngestionConfig):
        """
        Initialize news ingestion service.
        
        Args:
            config: Service configuration
        """
        self.config = config
        self.stats = NewsIngestionStats()
        
        # Initialize GNews client
        gnews_config = GNewsConfig(
            api_key=config.gnews_api_key or os.getenv("GNEWS_API_KEY"),
            max_articles=config.max_articles_per_batch
        )
        self.gnews_client = GNewsClient(gnews_config)
        
        # Initialize content extractor
        self.content_extractor = ContentExtractor(timeout=config.content_extraction_timeout)
        
        # Initialize database service
        self.article_db = ArticleDBService()
        
        # Track processed content hashes for deduplication
        self._processed_hashes: Set[str] = set()
    
    async def fetch_and_process_articles(
        self,
        query: str,
        max_articles: Optional[int] = None,
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None
    ) -> NewsIngestionStats:
        """
        Fetch and process articles for a given query.
        
        Args:
            query: Search query for articles
            max_articles: Maximum number of articles to process
            from_date: Start date for article search
            to_date: End date for article search
            
        Returns:
            Processing statistics
        """
        start_time = datetime.now(timezone.utc)
        self.stats = NewsIngestionStats()  # Reset stats
        
        try:
            logger.info(f"Starting news ingestion for query: {query}")
            
            # Fetch articles from GNews
            async with self.gnews_client:
                gnews_articles = await self.gnews_client.search_articles(
                    query=query,
                    max_articles=max_articles or self.config.max_articles_per_batch,
                    from_date=from_date,
                    to_date=to_date
                )
            
            self.stats.articles_fetched = len(gnews_articles)
            logger.info(f"Fetched {len(gnews_articles)} articles from GNews")
            
            # Load existing content hashes for deduplication
            if self.config.enable_deduplication:
                await self._load_existing_hashes()
            
            # Process articles
            async with self.content_extractor:
                processed_articles = []
                
                for gnews_article in gnews_articles:
                    try:
                        article_data = await self._process_single_article(gnews_article)
                        if article_data:
                            processed_articles.append(article_data)
                            self.stats.articles_processed += 1
                        else:
                            self.stats.articles_skipped += 1
                        
                        # Add delay between processing
                        await asyncio.sleep(self.config.batch_processing_delay)
                    
                    except Exception as e:
                        logger.error(f"Failed to process article {gnews_article.url}: {str(e)}")
                        self.stats.extraction_failures += 1
                        continue
                
                # Store processed articles in database
                for article_data in processed_articles:
                    try:
                        await self.article_db.create_article(article_data)
                        self.stats.articles_stored += 1
                        logger.debug(f"Stored article: {article_data.title}")
                    
                    except Exception as e:
                        logger.error(f"Failed to store article {article_data.id}: {str(e)}")
                        continue
            
            # Update final stats
            end_time = datetime.now(timezone.utc)
            self.stats.processing_time_seconds = (end_time - start_time).total_seconds()
            self.stats.last_run = end_time
            
            logger.info(f"News ingestion completed. Processed: {self.stats.articles_processed}, "
                       f"Stored: {self.stats.articles_stored}, "
                       f"Time: {self.stats.processing_time_seconds:.2f}s")
            
            return self.stats
        
        except Exception as e:
            logger.error(f"News ingestion failed: {str(e)}")
            raise
    
    async def fetch_top_headlines(
        self,
        category: Optional[str] = None,
        max_articles: Optional[int] = None
    ) -> NewsIngestionStats:
        """
        Fetch and process top headlines.
        
        Args:
            category: News category filter
            max_articles: Maximum number of articles to process
            
        Returns:
            Processing statistics
        """
        start_time = datetime.now(timezone.utc)
        self.stats = NewsIngestionStats()
        
        try:
            logger.info(f"Fetching top headlines, category: {category}")
            
            # Fetch top headlines
            async with self.gnews_client:
                gnews_articles = await self.gnews_client.get_top_headlines(
                    category=category,
                    max_articles=max_articles or self.config.max_articles_per_batch
                )
            
            self.stats.articles_fetched = len(gnews_articles)
            
            # Load existing hashes for deduplication
            if self.config.enable_deduplication:
                await self._load_existing_hashes()
            
            # Process articles
            async with self.content_extractor:
                for gnews_article in gnews_articles:
                    try:
                        article_data = await self._process_single_article(gnews_article)
                        if article_data:
                            await self.article_db.create_article(article_data)
                            self.stats.articles_processed += 1
                            self.stats.articles_stored += 1
                        else:
                            self.stats.articles_skipped += 1
                        
                        await asyncio.sleep(self.config.batch_processing_delay)
                    
                    except Exception as e:
                        logger.error(f"Failed to process headline {gnews_article.url}: {str(e)}")
                        self.stats.extraction_failures += 1
                        continue
            
            # Update final stats
            end_time = datetime.now(timezone.utc)
            self.stats.processing_time_seconds = (end_time - start_time).total_seconds()
            self.stats.last_run = end_time
            
            return self.stats
        
        except Exception as e:
            logger.error(f"Top headlines ingestion failed: {str(e)}")
            raise

    async def ingest_news(self, query: str, max_articles: Optional[int] = None) -> List[ArticleData]:
        """
        Ingest news articles without storing to database.

        This method is used by the pipeline orchestrator to fetch articles
        that will be processed through the full pipeline.

        Args:
            query: Search query for articles
            max_articles: Maximum number of articles to fetch

        Returns:
            List of processed article data (not stored to database)
        """
        try:
            logger.info(f"Ingesting news for pipeline processing: query='{query}', max_articles={max_articles}")

            # Fetch articles from GNews
            async with self.gnews_client:
                gnews_articles = await self.gnews_client.search_articles(
                    query=query,
                    max_articles=max_articles or self.config.max_articles_per_batch
                )

            if not gnews_articles:
                logger.warning("No articles found for query")
                return []

            # Process articles without storing to database
            async with self.content_extractor:
                processed_articles = []

                for gnews_article in gnews_articles:
                    try:
                        article_data = await self._process_single_article(gnews_article)
                        if article_data:
                            processed_articles.append(article_data)

                        # Add delay between processing
                        await asyncio.sleep(self.config.batch_processing_delay)

                    except Exception as e:
                        logger.error(f"Failed to process article {gnews_article.url}: {str(e)}")
                        continue

            logger.info(f"Ingested {len(processed_articles)} articles for pipeline processing")
            return processed_articles

        except Exception as e:
            logger.error(f"News ingestion failed: {str(e)}")
            raise

    async def process_article_url(self, url: str) -> List[ArticleData]:
        """
        Process a single article from URL.

        Args:
            url: Article URL to process

        Returns:
            List containing the processed article data
        """
        try:
            logger.info(f"Processing single article from URL: {url}")

            # Create a mock GNews article for processing
            from .gnews_client import GNewsArticle
            mock_gnews_article = GNewsArticle(
                title="",  # Will be extracted
                description="",
                content="",
                url=url,
                image="",
                publishedAt="",
                source={"name": "Direct URL", "url": ""}
            )

            # Process the article
            async with self.content_extractor:
                article_data = await self._process_single_article(mock_gnews_article)

                if article_data:
                    return [article_data]
                else:
                    return []

        except Exception as e:
            logger.error(f"Failed to process article URL {url}: {str(e)}")
            raise
    
    async def _process_single_article(self, gnews_article: GNewsArticle) -> Optional[ArticleData]:
        """
        Process a single GNews article.
        
        Args:
            gnews_article: GNews article data
            
        Returns:
            Processed article data or None if processing fails
        """
        try:
            # Check if article is too old
            published_at = datetime.fromisoformat(gnews_article.publishedAt.replace('Z', '+00:00'))
            max_age = datetime.now(timezone.utc) - timedelta(days=self.config.max_content_age_days)
            
            if published_at < max_age:
                logger.debug(f"Skipping old article: {gnews_article.title}")
                return None
            
            # Extract full content if enabled
            extracted_content = None
            if self.config.enable_content_extraction:
                extracted_content = await self.content_extractor.extract_content(gnews_article.url)
            
            # Use extracted content or fallback to GNews description
            if extracted_content and extracted_content.confidence_score > 0.5:
                title = extracted_content.title
                content = extracted_content.content
                word_count = extracted_content.word_count
                extraction_confidence = extracted_content.confidence_score
                language = extracted_content.language
                content_hash = extracted_content.content_hash
            else:
                title = gnews_article.title
                content = gnews_article.description or gnews_article.content
                word_count = len(content.split()) if content else 0
                extraction_confidence = 0.3  # Low confidence for GNews-only content
                language = None
                content_hash = self.content_extractor.extractors[0]._calculate_content_hash(content)
            
            # Check for duplicates
            if self.config.enable_deduplication and content_hash in self._processed_hashes:
                logger.debug(f"Skipping duplicate article: {title}")
                self.stats.duplicate_articles += 1
                return None
            
            # Add to processed hashes
            self._processed_hashes.add(content_hash)
            
            # Create article data
            now = datetime.now(timezone.utc)
            article_data = ArticleData(
                id=str(uuid4()),
                title=title,
                content=content,
                url=gnews_article.url,
                published_at=published_at,
                source=gnews_article.source.get('name', 'Unknown'),
                processing_status=ProcessingStatus.PENDING,
                extraction_confidence=extraction_confidence,
                language=language,
                word_count=word_count,
                created_at=now,
                updated_at=now
            )
            
            return article_data
        
        except Exception as e:
            logger.error(f"Failed to process article {gnews_article.url}: {str(e)}")
            return None
    
    async def _load_existing_hashes(self):
        """Load existing content hashes for deduplication."""
        try:
            # Get recent articles to build hash set
            recent_cutoff = datetime.now(timezone.utc) - timedelta(days=self.config.max_content_age_days)
            
            # This would need to be implemented in ArticleDBService
            # For now, we'll start with an empty set
            self._processed_hashes = set()
            
            logger.debug(f"Loaded {len(self._processed_hashes)} existing content hashes")
        
        except Exception as e:
            logger.warning(f"Failed to load existing hashes: {str(e)}")
            self._processed_hashes = set()
    
    def get_stats(self) -> NewsIngestionStats:
        """Get current ingestion statistics."""
        return self.stats
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get service health status."""
        return {
            "service": "news_ingestion",
            "status": "healthy",
            "last_run": self.stats.last_run.isoformat() if self.stats.last_run else None,
            "articles_processed": self.stats.articles_processed,
            "gnews_circuit_breaker": self.gnews_client.get_circuit_breaker_status(),
            "gnews_rate_limiter": self.gnews_client.get_rate_limiter_status()
        }
