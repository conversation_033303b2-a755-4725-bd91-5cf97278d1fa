"""
Simple Pipeline Orchestrator

Coordinates the flow of data through the Star Power pipeline:
news → celebrity extraction → astrology analysis

Provides error handling, retry logic, status monitoring, and workflow coordination.
"""

import asyncio
import logging
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass, field
from uuid import uuid4

from .news_pipeline.news_ingestion_service import NewsIngestionService, NewsIngestionConfig
from .llm_processor.llm_orchestrator import LLMOrchestrator
from .llm_processor.celebrity_data_validator import CelebrityDataValidator
from .llm_processor.entity_resolver import EntityResolver
from .llm_processor.models import LLMConfig, LLMProvider, CelebrityIdentificationRequest
from .llm_processor.isolate_models import IsolateExtractionRequest
from .astrology_engine.ephemeris_calculator import EphemerisCalculator
from .astrology_engine.aspect_engine import AspectEngine
from .database.article_db_service import ArticleDBService
from .database.celebrity_db_service import CelebrityDBService
from .database.generic_entity_db_service import GenericEntityDBService
from .database.entity_graph_db_service import EntityGraphDBService
from .database.models import ArticleData, CelebrityData, ProcessingStatus
from .config import get_env_var, get_bool_env_var, get_int_env_var, get_float_env_var

logger = logging.getLogger(__name__)


class PipelineStage(Enum):
    """Pipeline processing stages."""
    NEWS_INGESTION = "news_ingestion"
    CELEBRITY_EXTRACTION = "celebrity_extraction"
    CELEBRITY_VALIDATION = "celebrity_validation"
    ISOLATE_EXTRACTION = "isolate_extraction"  # NEW: Entity extraction stage
    ASTROLOGY_ANALYSIS = "astrology_analysis"
    COMPLETED = "completed"
    FAILED = "failed"


class RetryPolicy(Enum):
    """Retry policy options."""
    NO_RETRY = "no_retry"
    LINEAR_BACKOFF = "linear_backoff"
    EXPONENTIAL_BACKOFF = "exponential_backoff"


@dataclass
class PipelineConfig:
    """Configuration for pipeline orchestration."""
    # News ingestion settings
    news_batch_size: int = 20
    news_processing_timeout: int = 120  # 2 minutes (max allowed by NewsIngestionConfig)

    # Celebrity extraction settings
    celebrity_extraction_timeout: int = 120  # 2 minutes
    celebrity_confidence_threshold: float = 0.7

    # Isolate extraction settings
    enable_isolate_extraction: bool = True
    isolate_extraction_timeout: int = 180  # 3 minutes (Perplexity can be slower)
    isolate_confidence_threshold: float = 0.7

    # Astrology analysis settings
    astrology_analysis_timeout: int = 60  # 1 minute

    # Retry settings
    max_retries: int = 3
    retry_policy: RetryPolicy = RetryPolicy.EXPONENTIAL_BACKOFF
    base_retry_delay: float = 1.0
    max_retry_delay: float = 60.0

    # Monitoring settings
    enable_detailed_logging: bool = True
    enable_performance_tracking: bool = True

    def __post_init__(self):
        """Load configuration from environment variables."""
        # Load Isolate extraction settings from environment
        self.enable_isolate_extraction = get_bool_env_var('ENABLE_ISOLATE_EXTRACTION', self.enable_isolate_extraction)
        self.isolate_extraction_timeout = get_int_env_var('ISOLATE_EXTRACTION_TIMEOUT', self.isolate_extraction_timeout)
        self.isolate_confidence_threshold = get_float_env_var('ISOLATE_CONFIDENCE_THRESHOLD', self.isolate_confidence_threshold)


@dataclass
class PipelineJob:
    """Represents a single pipeline job."""
    job_id: str = field(default_factory=lambda: str(uuid4()))
    stage: PipelineStage = PipelineStage.NEWS_INGESTION
    article_data: Optional[ArticleData] = None
    celebrities: List[CelebrityData] = field(default_factory=list)
    isolate_results: Optional[Dict[str, Any]] = None  # NEW: Isolate extraction results
    astrology_results: Optional[Dict[str, Any]] = None
    
    # Tracking
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # Error handling
    retry_count: int = 0
    last_error: Optional[str] = None
    error_history: List[str] = field(default_factory=list)
    
    # Performance
    stage_timings: Dict[str, float] = field(default_factory=dict)
    total_processing_time: Optional[float] = None


@dataclass
class PipelineStats:
    """Pipeline orchestration statistics."""
    jobs_processed: int = 0
    jobs_completed: int = 0
    jobs_failed: int = 0
    
    # Stage statistics
    news_ingestion_count: int = 0
    celebrity_extraction_count: int = 0
    astrology_analysis_count: int = 0
    
    # Performance metrics
    average_processing_time: float = 0.0
    total_processing_time: float = 0.0
    
    # Error tracking
    retry_count: int = 0
    error_count: int = 0
    
    # Timestamps
    last_job_started: Optional[datetime] = None
    last_job_completed: Optional[datetime] = None


class SimplePipelineOrchestrator:
    """
    Simple pipeline orchestrator for coordinating news → celebrity → astrology workflow.
    
    Provides error handling, retry logic, status monitoring, and workflow coordination
    between the different services in the Star Power pipeline.
    """
    
    def __init__(self, config: Optional[PipelineConfig] = None):
        """
        Initialize pipeline orchestrator.
        
        Args:
            config: Pipeline configuration
        """
        self.config = config or PipelineConfig()
        self.stats = PipelineStats()
        self.active_jobs: Dict[str, PipelineJob] = {}
        self.completed_jobs: List[PipelineJob] = []
        
        # Initialize services
        self._initialize_services()
        
        logger.info("Pipeline orchestrator initialized")
    
    def _initialize_services(self):
        """Initialize all pipeline services."""
        try:
            # News ingestion service
            news_config = NewsIngestionConfig(
                max_articles_per_batch=self.config.news_batch_size,
                content_extraction_timeout=self.config.news_processing_timeout
            )
            self.news_service = NewsIngestionService(news_config)
            
            # LLM orchestrator for celebrity extraction
            llm_config = LLMConfig(
                primary_provider=LLMProvider.OLLAMA,
                fallback_provider=LLMProvider.OPENAI,
                quality_threshold=self.config.celebrity_confidence_threshold
            )
            self.llm_orchestrator = LLMOrchestrator(llm_config)
            
            # Celebrity data validator
            self.celebrity_validator = CelebrityDataValidator()
            
            # Astrology services
            self.ephemeris_calculator = EphemerisCalculator()
            self.aspect_engine = AspectEngine()
            
            # Database services
            self.article_db = ArticleDBService()
            self.celebrity_db = CelebrityDBService()
            self.generic_entity_db = GenericEntityDBService()
            self.entity_graph_db = EntityGraphDBService()

            # Entity resolver
            self.entity_resolver = EntityResolver(
                celebrity_db_service=self.celebrity_db,
                article_db_service=self.article_db,
                generic_entity_db_service=self.generic_entity_db,
                entity_graph_db_service=self.entity_graph_db
            )
            
            logger.info("All pipeline services initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize pipeline services: {e}")
            raise
    
    async def process_news_batch(self, query: str = "celebrity news", max_articles: int = 20) -> List[str]:
        """
        Process a batch of news articles through the complete pipeline.
        
        Args:
            query: News search query
            max_articles: Maximum number of articles to process
            
        Returns:
            List of job IDs for tracking
        """
        logger.info(f"Starting news batch processing: query='{query}', max_articles={max_articles}")
        
        try:
            # Step 1: Ingest news articles
            articles = await self.news_service.ingest_news(query, max_articles)
            
            if not articles:
                logger.warning("No articles retrieved from news ingestion")
                return []
            
            # Step 2: Create pipeline jobs for each article
            job_ids = []
            for article in articles:
                job = PipelineJob(article_data=article)
                job.started_at = datetime.now(timezone.utc)
                self.active_jobs[job.job_id] = job
                job_ids.append(job.job_id)
            
            # Step 3: Process jobs through pipeline stages
            for job_id in job_ids:
                asyncio.create_task(self._process_pipeline_job(job_id))
            
            self.stats.jobs_processed += len(job_ids)
            self.stats.last_job_started = datetime.now(timezone.utc)
            
            logger.info(f"Created {len(job_ids)} pipeline jobs")
            return job_ids
            
        except Exception as e:
            logger.error(f"Failed to process news batch: {e}")
            raise
    
    async def _process_pipeline_job(self, job_id: str):
        """Process a single job through all pipeline stages."""
        job = self.active_jobs.get(job_id)
        if not job:
            logger.error(f"Job {job_id} not found")
            return
        
        try:
            # Stage 1: Celebrity Extraction
            await self._process_celebrity_extraction(job)

            # Stage 2: Celebrity Validation
            await self._process_celebrity_validation(job)

            # Stage 3: Isolate Extraction (if enabled)
            if self.config.enable_isolate_extraction:
                await self._process_isolate_extraction(job)
            else:
                logger.info(f"Isolate extraction disabled, skipping for job {job_id}")

            # Stage 4: Astrology Analysis
            await self._process_astrology_analysis(job)
            
            # Mark as completed
            job.stage = PipelineStage.COMPLETED
            job.completed_at = datetime.now(timezone.utc)
            job.total_processing_time = (job.completed_at - job.started_at).total_seconds()
            
            # Update statistics
            self.stats.jobs_completed += 1
            self.stats.total_processing_time += job.total_processing_time
            self.stats.average_processing_time = self.stats.total_processing_time / max(self.stats.jobs_completed, 1)
            self.stats.last_job_completed = job.completed_at
            
            # Move to completed jobs
            self.completed_jobs.append(job)
            del self.active_jobs[job_id]
            
            logger.info(f"Pipeline job {job_id} completed successfully in {job.total_processing_time:.2f}s")
            
        except Exception as e:
            await self._handle_job_error(job, str(e))
    
    async def _process_celebrity_extraction(self, job: PipelineJob):
        """Process celebrity extraction stage."""
        stage_start = time.time()
        job.stage = PipelineStage.CELEBRITY_EXTRACTION
        
        try:
            if not job.article_data:
                raise ValueError("No article data available for celebrity extraction")
            
            # Create celebrity identification request
            request = CelebrityIdentificationRequest(
                article_title=job.article_data.title,
                article_content=job.article_data.content or job.article_data.summary,
                known_entities=[]
            )
            
            # Process with timeout
            response = await asyncio.wait_for(
                self.llm_orchestrator.identify_celebrities(
                    article_title=request.article_title,
                    article_content=request.article_content
                ),
                timeout=self.config.celebrity_extraction_timeout
            )
            
            # Convert to CelebrityData objects
            celebrities = []
            for celebrity_match in response.celebrities:
                if celebrity_match.confidence >= self.config.celebrity_confidence_threshold:
                    celebrity_data = CelebrityData(
                        name=celebrity_match.name,
                        confidence_score=celebrity_match.confidence,
                        data_sources=[],
                        enhanced=False,
                        verified=False,
                        aliases=celebrity_match.aliases or []
                    )
                    celebrities.append(celebrity_data)
            
            job.celebrities = celebrities
            job.stage_timings['celebrity_extraction'] = time.time() - stage_start
            self.stats.celebrity_extraction_count += 1
            
            logger.info(f"Extracted {len(celebrities)} celebrities from article {job.article_data.title}")
            
        except asyncio.TimeoutError:
            raise Exception(f"Celebrity extraction timed out after {self.config.celebrity_extraction_timeout}s")
        except Exception as e:
            raise Exception(f"Celebrity extraction failed: {e}")
    
    async def _process_celebrity_validation(self, job: PipelineJob):
        """Process celebrity validation stage."""
        stage_start = time.time()
        job.stage = PipelineStage.CELEBRITY_VALIDATION
        
        try:
            if not job.celebrities:
                logger.info(f"No celebrities to validate for job {job.job_id}")
                job.stage_timings['celebrity_validation'] = time.time() - stage_start
                return
            
            # Validate and enhance each celebrity
            validated_celebrities = []
            for celebrity in job.celebrities:
                try:
                    # Create a mock celebrity match for validation
                    from .llm_processor.models import CelebrityMatch
                    celebrity_match = CelebrityMatch(
                        name=celebrity.name,
                        confidence=celebrity.confidence_score,
                        context="",
                        aliases=celebrity.aliases
                    )
                    
                    # Validate and enhance
                    enhanced_celebrity, final_confidence = await self.celebrity_validator.validate_and_enhance_celebrity(
                        celebrity_match,
                        job.article_data.content or job.article_data.summary or ""
                    )
                    
                    validated_celebrities.append(enhanced_celebrity)
                    
                except Exception as e:
                    logger.warning(f"Failed to validate celebrity {celebrity.name}: {e}")
                    # Keep original celebrity if validation fails
                    validated_celebrities.append(celebrity)
            
            job.celebrities = validated_celebrities
            job.stage_timings['celebrity_validation'] = time.time() - stage_start
            
            logger.info(f"Validated {len(validated_celebrities)} celebrities for job {job.job_id}")
            
        except Exception as e:
            raise Exception(f"Celebrity validation failed: {e}")

    async def _process_isolate_extraction(self, job: PipelineJob):
        """Process Isolate entity extraction stage."""
        stage_start = time.time()
        job.stage = PipelineStage.ISOLATE_EXTRACTION

        try:
            if not job.article_data:
                raise ValueError("No article data available for Isolate extraction")

            # Create Isolate extraction request
            request = IsolateExtractionRequest(
                article_text=job.article_data.content or job.article_data.summary or "",
                article_title=job.article_data.title,
                article_url=job.article_data.url,
                source=job.article_data.source
            )

            # Process with timeout
            response = await asyncio.wait_for(
                self.llm_orchestrator.extract_isolate_entities(request),
                timeout=self.config.isolate_extraction_timeout
            )

            # Store the entity graph using EntityResolver
            if (response.isolate_data.entities or response.isolate_data.relationships or
                response.isolate_data.actions or response.isolate_data.endeavors):
                graph_summary = await self.entity_resolver.store_isolate_graph(
                    article_id=job.article_data.id,
                    isolate_response=response.isolate_data
                )

                job.isolate_results = {
                    'entities_extracted': response.entities_extracted,
                    'relationships_extracted': response.relationships_extracted,
                    'actions_extracted': response.actions_extracted,
                    'endeavors_extracted': response.endeavors_extracted,
                    'confidence_score': response.confidence_score,
                    'graph_summary': graph_summary,
                    'processing_time_ms': response.processing_time_ms
                }

                logger.info(f"Isolate extraction completed for job {job.job_id}: "
                           f"{response.entities_extracted} entities, {response.relationships_extracted} relationships, "
                           f"{response.actions_extracted} actions, {response.endeavors_extracted} endeavors")
            else:
                job.isolate_results = {
                    'entities_extracted': 0,
                    'relationships_extracted': 0,
                    'actions_extracted': 0,
                    'endeavors_extracted': 0,
                    'confidence_score': 0.0,
                    'processing_time_ms': response.processing_time_ms
                }

                logger.info(f"No entities extracted for job {job.job_id}")

            job.stage_timings['isolate_extraction'] = time.time() - stage_start

        except asyncio.TimeoutError:
            raise Exception(f"Isolate extraction timed out after {self.config.isolate_extraction_timeout}s")
        except Exception as e:
            raise Exception(f"Isolate extraction failed: {e}")

    async def _process_astrology_analysis(self, job: PipelineJob):
        """Process astrology analysis stage."""
        stage_start = time.time()
        job.stage = PipelineStage.ASTROLOGY_ANALYSIS
        
        try:
            if not job.celebrities:
                logger.info(f"No celebrities for astrology analysis in job {job.job_id}")
                job.stage_timings['astrology_analysis'] = time.time() - stage_start
                return
            
            astrology_results = []
            
            for celebrity in job.celebrities:
                try:
                    # Only process celebrities with birth data
                    if not celebrity.birth_date:
                        logger.debug(f"Skipping astrology analysis for {celebrity.name} - no birth date")
                        continue
                    
                    # Calculate ephemeris
                    chart_data = await asyncio.wait_for(
                        self.ephemeris_calculator.calculate_chart(
                            birth_date=celebrity.birth_date,
                            birth_time=celebrity.birth_time,
                            latitude=celebrity.birth_latitude or 0.0,
                            longitude=celebrity.birth_longitude or 0.0
                        ),
                        timeout=self.config.astrology_analysis_timeout
                    )
                    
                    # Calculate aspects
                    aspects = self.aspect_engine.calculate_aspects(chart_data)
                    
                    astrology_result = {
                        'celebrity_name': celebrity.name,
                        'birth_date': celebrity.birth_date.isoformat() if celebrity.birth_date else None,
                        'chart_data': chart_data,
                        'aspects': aspects,
                        'analysis_timestamp': datetime.now(timezone.utc).isoformat()
                    }
                    
                    astrology_results.append(astrology_result)
                    
                except asyncio.TimeoutError:
                    logger.warning(f"Astrology analysis timed out for {celebrity.name}")
                except Exception as e:
                    logger.warning(f"Astrology analysis failed for {celebrity.name}: {e}")
            
            job.astrology_results = {
                'results': astrology_results,
                'total_celebrities': len(job.celebrities),
                'analyzed_celebrities': len(astrology_results)
            }
            
            job.stage_timings['astrology_analysis'] = time.time() - stage_start
            self.stats.astrology_analysis_count += len(astrology_results)
            
            logger.info(f"Completed astrology analysis for {len(astrology_results)} celebrities in job {job.job_id}")
            
        except Exception as e:
            raise Exception(f"Astrology analysis failed: {e}")
    
    async def _handle_job_error(self, job: PipelineJob, error_message: str):
        """Handle job processing errors with retry logic."""
        job.last_error = error_message
        job.error_history.append(f"{datetime.now(timezone.utc).isoformat()}: {error_message}")
        
        logger.error(f"Job {job.job_id} failed at stage {job.stage.value}: {error_message}")
        
        # Check if we should retry
        if job.retry_count < self.config.max_retries:
            job.retry_count += 1
            self.stats.retry_count += 1
            
            # Calculate retry delay
            delay = self._calculate_retry_delay(job.retry_count)
            
            logger.info(f"Retrying job {job.job_id} in {delay:.1f}s (attempt {job.retry_count}/{self.config.max_retries})")
            
            # Schedule retry
            await asyncio.sleep(delay)
            await self._process_pipeline_job(job.job_id)
        else:
            # Mark as failed
            job.stage = PipelineStage.FAILED
            job.completed_at = datetime.now(timezone.utc)
            
            self.stats.jobs_failed += 1
            self.stats.error_count += 1
            
            # Move to completed jobs
            self.completed_jobs.append(job)
            del self.active_jobs[job.job_id]
            
            logger.error(f"Job {job.job_id} failed permanently after {job.retry_count} retries")
    
    def _calculate_retry_delay(self, retry_count: int) -> float:
        """Calculate retry delay based on policy."""
        if self.config.retry_policy == RetryPolicy.NO_RETRY:
            return 0.0
        elif self.config.retry_policy == RetryPolicy.LINEAR_BACKOFF:
            delay = self.config.base_retry_delay * retry_count
        else:  # EXPONENTIAL_BACKOFF
            delay = self.config.base_retry_delay * (2 ** (retry_count - 1))
        
        return min(delay, self.config.max_retry_delay)
    
    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific job."""
        job = self.active_jobs.get(job_id)
        if not job:
            # Check completed jobs
            for completed_job in self.completed_jobs:
                if completed_job.job_id == job_id:
                    job = completed_job
                    break
        
        if not job:
            return None
        
        return {
            'job_id': job.job_id,
            'stage': job.stage.value,
            'created_at': job.created_at.isoformat(),
            'started_at': job.started_at.isoformat() if job.started_at else None,
            'completed_at': job.completed_at.isoformat() if job.completed_at else None,
            'retry_count': job.retry_count,
            'last_error': job.last_error,
            'celebrities_found': len(job.celebrities),
            'astrology_completed': job.astrology_results is not None,
            'stage_timings': job.stage_timings,
            'total_processing_time': job.total_processing_time
        }
    
    def get_pipeline_stats(self) -> Dict[str, Any]:
        """Get comprehensive pipeline statistics."""
        return {
            'jobs_processed': self.stats.jobs_processed,
            'jobs_completed': self.stats.jobs_completed,
            'jobs_failed': self.stats.jobs_failed,
            'jobs_active': len(self.active_jobs),
            'success_rate': self.stats.jobs_completed / max(self.stats.jobs_processed, 1),
            'average_processing_time': self.stats.average_processing_time,
            'total_processing_time': self.stats.total_processing_time,
            'retry_count': self.stats.retry_count,
            'error_count': self.stats.error_count,
            'stage_counts': {
                'news_ingestion': self.stats.news_ingestion_count,
                'celebrity_extraction': self.stats.celebrity_extraction_count,
                'astrology_analysis': self.stats.astrology_analysis_count
            },
            'last_job_started': self.stats.last_job_started.isoformat() if self.stats.last_job_started else None,
            'last_job_completed': self.stats.last_job_completed.isoformat() if self.stats.last_job_completed else None
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get pipeline health status."""
        return {
            'service': 'pipeline_orchestrator',
            'status': 'healthy',
            'active_jobs': len(self.active_jobs),
            'completed_jobs': len(self.completed_jobs),
            'error_rate': self.stats.error_count / max(self.stats.jobs_processed, 1),
            'services': {
                'news_service': 'healthy',
                'llm_orchestrator': 'healthy',
                'celebrity_validator': 'healthy',
                'astrology_engine': 'healthy'
            }
        }
