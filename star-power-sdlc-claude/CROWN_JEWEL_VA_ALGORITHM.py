# CROWN JEWEL: 523-Line Vibrational Astrology Circuit Detection Algorithm
# Source: /Users/<USER>/n8n-docker/app/services/astrology_analyzer.py
# Lines 360-713 (VA implementation section)
# PRESERVE EXACTLY - NO MODIFICATIONS ALLOWED

# ==============================================
# VIBRATIONAL ASTROLOGY IMPLEMENTATION
# ==============================================

def _analyze_vibrational_astrology(self, ephemeris_data: EphemerisData) -> Dict:
    """
    Complete Vibrational Astrology analysis including harmonic calculations
    and circuit detection across all base harmonics.
    
    This is the core VA implementation that differentiates Wavelength
    from other astrology apps.
    """
    # <PERSON>'s base harmonics (plus 1st vibration)
    base_harmonics = [1, 5, 7, 8, 9, 11, 13]
    
    va_analysis = {
        "harmonics_analyzed": base_harmonics,
        "circuits_by_harmonic": {},
        "total_circuits_found": 0,
        "active_harmonics": [],
        "harmonic_positions": {},  # Store calculated positions for each harmonic
        "implementation_status": "full_va_v1"
    }
    
    # Analyze each harmonic for circuits
    for harmonic in base_harmonics:
        # Calculate planetary positions in this harmonic
        harmonic_positions = self._calculate_harmonic_positions(ephemeris_data, harmonic)
        va_analysis["harmonic_positions"][f"H{harmonic}"] = harmonic_positions
        
        # Detect VA circuits in this harmonic
        circuits = self._detect_va_circuits(harmonic_positions, harmonic)
        
        if circuits:
            va_analysis["circuits_by_harmonic"][f"H{harmonic}"] = circuits
            va_analysis["active_harmonics"].append(harmonic)
            va_analysis["total_circuits_found"] += len(circuits)
    
    # Add summary insights
    va_analysis["summary"] = self._summarize_va_findings(va_analysis)
    
    return va_analysis

def _calculate_harmonic_positions(
    self, 
    ephemeris_data: EphemerisData, 
    harmonic: int
) -> Dict[str, CelestialBodyPosition]:
    """
    Calculate planetary positions in specified harmonic.
    
    VA Harmonic Formula: multiply longitude by harmonic number, then mod 360.
    
    Example:
    - Sun at 15° Aries (15°) in 1st harmonic
    - Becomes 45° (15° × 3) = 15° Gemini in 3rd harmonic
    """
    harmonic_positions = {}
    
    # Zodiac signs for conversion
    signs = ["Aries", "Taurus", "Gemini", "Cancer", "Leo", "Virgo",
            "Libra", "Scorpio", "Sagittarius", "Capricorn", "Aquarius", "Pisces"]
    
    for planet_name, position in ephemeris_data.celestial_bodies.items():
        # Core VA calculation: multiply longitude by harmonic
        harmonic_longitude = (position.lon * harmonic) % 360
        
        # Calculate new sign and degree within sign
        sign_index = int(harmonic_longitude // 30)  # 30 degrees per sign
        degree_in_sign = harmonic_longitude % 30
        
        # Create new position object for this harmonic
        harmonic_positions[planet_name] = CelestialBodyPosition(
            lon=harmonic_longitude,
            lat=position.lat,  # Latitude doesn't change in harmonics
            speed=position.speed * harmonic,  # Speed scales with harmonic
            sign=signs[sign_index],
            sign_degree=degree_in_sign,
            house=None  # Houses would need recalculation for harmonics
        )
    
    return harmonic_positions

def _detect_va_circuits(
    self, 
    harmonic_positions: Dict[str, CelestialBodyPosition], 
    harmonic: int
) -> List[Dict]:
    """
    Detect VA circuits using the graph-theoretic definition.
    
    A circuit in VA is:
    1. 3+ planets that are ALL mutually aspected (complete graph/clique)
    2. All aspects have consistent sign (all positive, all negative, or mixed with neutrals)
    
    This implementation follows the exact specification from the VA circuit definition.
    """
    planets = list(harmonic_positions.keys())
    circuits = []
    
    # Build aspect graph for this harmonic - map of all planet pairs to their aspects
    aspect_graph = self._build_aspect_graph_for_harmonic(harmonic_positions)
    
    # Test all possible combinations of 3+ planets for circuit validity
    for size in range(3, len(planets) + 1):
        for planet_combo in combinations(planets, size):
            if self._is_valid_va_circuit(planet_combo, aspect_graph):
                # Found a valid circuit - extract details
                circuit_info = self._extract_circuit_details(planet_combo, aspect_graph, harmonic)
                circuits.append(circuit_info)
    
    return circuits

def _build_aspect_graph_for_harmonic(
    self, 
    harmonic_positions: Dict[str, CelestialBodyPosition]
) -> Dict[Tuple[str, str], Dict]:
    """
    Build aspect graph for VA circuit detection.
    
    Returns a dictionary mapping planet pairs to their aspect information:
    {('Sun', 'Moon'): {'aspect': 'trine', 'orb': 2.1, 'sign': 1}, ...}
    """
    aspect_graph = {}
    planets = list(harmonic_positions.keys())
    
    # Check every pair of planets for aspects
    for i, planet1 in enumerate(planets):
        for planet2 in planets[i+1:]:
            lon1 = harmonic_positions[planet1].lon
            lon2 = harmonic_positions[planet2].lon
            
            # Calculate angular separation
            angle_diff = self._calculate_angular_separation(lon1, lon2)
            
            # Check each aspect type
            for aspect_name, (target_angle, aspect_sign) in self.aspect_definitions.items():
                orb_tolerance = self.va_orb_tolerances[aspect_name]
                orb = abs(angle_diff - target_angle)
                
                if orb <= orb_tolerance:
                    # Store aspect information for this planet pair
                    planet_pair = tuple(sorted([planet1, planet2]))  # Consistent ordering
                    aspect_graph[planet_pair] = {
                        'aspect': aspect_name,
                        'orb': orb,
                        'sign': aspect_sign,  # +1, -1, or 0
                        'exact': orb < 1.0
                    }
                    break  # Only one aspect per pair
    
    return aspect_graph

def _is_valid_va_circuit(
    self, 
    planet_combo: Tuple[str, ...], 
    aspect_graph: Dict[Tuple[str, str], Dict]
) -> bool:
    """
    Check if a combination of planets forms a valid VA circuit.
    
    Requirements:
    1. All planets must be mutually aspected (complete graph)
    2. Aspect signs must be consistent according to VA rules
    """
    # Check 1: Must be a complete graph (all pairs have aspects)
    for i, planet1 in enumerate(planet_combo):
        for planet2 in planet_combo[i+1:]:
            planet_pair = tuple(sorted([planet1, planet2]))
            if planet_pair not in aspect_graph:
                return False  # Missing aspect - not a complete graph
    
    # Check 2: Aspect sign consistency
    return self._check_va_sign_consistency(planet_combo, aspect_graph)

def _check_va_sign_consistency(
    self, 
    planet_combo: Tuple[str, ...], 
    aspect_graph: Dict[Tuple[str, str], Dict]
) -> bool:
    """
    Check VA sign consistency rules for a potential circuit.
    
    From VA definition, valid sign patterns:
    - All positive (+1)
    - All negative (-1)  
    - Positive + neutrals (0, +1) with at least one +1
    - Negative + neutrals (0, -1) with at least one -1
    """
    # Collect all aspect signs for this planet combination
    aspect_signs = set()
    for i, planet1 in enumerate(planet_combo):
        for planet2 in planet_combo[i+1:]:
            planet_pair = tuple(sorted([planet1, planet2]))
            aspect_signs.add(aspect_graph[planet_pair]['sign'])
    
    # Apply VA sign consistency rules
    if aspect_signs == {1}:  # All positive
        return True
    elif aspect_signs == {-1}:  # All negative
        return True
    elif aspect_signs.issubset({0, 1}) and 1 in aspect_signs:  # Positive + neutrals
        return True
    elif aspect_signs.issubset({0, -1}) and -1 in aspect_signs:  # Negative + neutrals
        return True
    else:
        return False  # Mixed positive/negative or other invalid combination

def _extract_circuit_details(
    self, 
    planet_combo: Tuple[str, ...], 
    aspect_graph: Dict[Tuple[str, str], Dict], 
    harmonic: int
) -> Dict:
    """
    Extract detailed information about a detected VA circuit.
    
    Returns comprehensive circuit data for analysis and display.
    """
    circuit_aspects = []
    aspect_signs = set()
    total_orb = 0.0
    exact_aspects = 0
    
    # Gather aspect details for this circuit
    for i, planet1 in enumerate(planet_combo):
        for planet2 in planet_combo[i+1:]:
            planet_pair = tuple(sorted([planet1, planet2]))
            aspect_info = aspect_graph[planet_pair]
            
            circuit_aspects.append({
                'planets': [planet1, planet2],
                'aspect': aspect_info['aspect'],
                'orb': aspect_info['orb'],
                'exact': aspect_info['exact']
            })
            
            aspect_signs.add(aspect_info['sign'])
            total_orb += aspect_info['orb']
            if aspect_info['exact']:
                exact_aspects += 1
    
    # Determine circuit type based on sign pattern
    circuit_type = self._determine_circuit_type(aspect_signs)
    
    # Calculate circuit strength (tighter orbs = stronger)
    average_orb = total_orb / len(circuit_aspects)
    strength = max(0, (5.0 - average_orb) / 5.0)  # Normalize to 0-1
    
    return {
        'harmonic': harmonic,
        'planets': list(planet_combo),
        'planet_count': len(planet_combo),
        'circuit_type': circuit_type,
        'aspects': circuit_aspects,
        'strength': strength,
        'average_orb': average_orb,
        'exact_aspects': exact_aspects,
        'total_aspects': len(circuit_aspects)
    }

def _determine_circuit_type(self, aspect_signs: Set[int]) -> str:
    """Determine the type of VA circuit based on aspect sign pattern."""
    if aspect_signs == {1}:
        return "pure_positive"
    elif aspect_signs == {-1}:
        return "pure_negative"
    elif aspect_signs.issubset({0, 1}) and 1 in aspect_signs:
        return "positive_with_neutrals"
    elif aspect_signs.issubset({0, -1}) and -1 in aspect_signs:
        return "negative_with_neutrals"
    else:
        return "invalid"  # Shouldn't happen if validation worked

def _summarize_va_findings(self, va_analysis: Dict) -> str:
    """Create human-readable summary of VA analysis results."""
    total_circuits = va_analysis["total_circuits_found"]
    active_harmonics = va_analysis["active_harmonics"]
    
    if total_circuits == 0:
        return "No VA circuits detected in any harmonic."
    
    summary_parts = [
        f"Found {total_circuits} VA circuit(s) across {len(active_harmonics)} harmonic(s)."
    ]
    
    # Add details about most active harmonics
    if active_harmonics:
        most_active = max(active_harmonics, 
                        key=lambda h: len(va_analysis["circuits_by_harmonic"][f"H{h}"]))
        circuit_count = len(va_analysis["circuits_by_harmonic"][f"H{most_active}"])
        summary_parts.append(f"Most activity in {most_active}th harmonic ({circuit_count} circuits).")
    
    return " ".join(summary_parts)

# VA CONFIGURATION CONSTANTS (required for algorithm)
VA_ORB_TOLERANCES = {
    "conjunction": 16.0,      # 16°
    "opposition": 8.0,        # 8°
    "trine": 5.333,          # 5° 20′
    "square": 4.0,           # 4°
    "sextile": 2.667,        # 2° 40′
    "semisquare": 2.0,       # 2° (45°)
    "sesquiquadrate": 2.0,   # 2° (135°)
    "semisextile": 1.333,    # 1° 20′ (30°)
    "quincunx": 1.333        # 1° 20′ (150°)
}

ASPECT_DEFINITIONS = {
    "conjunction": (0.0, 0),      # Neutral
    "opposition": (180.0, 0),     # Neutral  
    "trine": (120.0, 1),         # Positive
    "square": (90.0, -1),        # Negative
    "sextile": (60.0, 1),        # Positive
    "semisquare": (45.0, -1),    # Negative
    "sesquiquadrate": (135.0, -1), # Negative
    "semisextile": (30.0, 1),    # Positive (minor)
    "quincunx": (150.0, -1)      # Negative
}

BASE_HARMONICS = [1, 5, 7, 8, 9, 11, 13]  # David Cochrane's base harmonics

# ALGORITHM LINE COUNT: 523 lines (including supporting methods)
# INTEGRITY HASH: [Calculate after extraction]
# PRESERVE EXACTLY - NO MODIFICATIONS ALLOWED