# Star Power SDLC - AI Agent Handoff Instructions

## Quick Start for AI Agents

**PRIORITY**: Read and implement documents in this exact sequence. Do NOT deviate from the crown jewel preservation strategy.

## Document Sequence (MANDATORY ORDER)

### 1. Foundation Understanding
- **`star_power_01_idea.md`** - Core problem, crown jewel identification, strategic vision
- **`star_power_02_prd.md`** - User requirements, success criteria, product vision
- **`wavelength_feature_inventory.md`** - Current state analysis, component inventory, technical blockers

### 2. Technical Specifications  
- **`star_power_03_prd_plus_unified.md`** - Performance requirements, constraints, technical framework
- **`star_power_04_architecture_unified.md`** - Service architecture, module structure, deployment

### 3. Implementation Execution
- **`star_power_05_implementation_unified.md`** - 6-week strategic roadmap, 14 YAML tasks, tactical execution
- **`star_power_06_tasks_unified.md`** - 46-hour tactical implementation, A/B/C task phases, detailed subtasks
- **`star_power_07_tasks_plus.md`** - AI validation report, 46-hour timeline confirmation, risk mitigation
- **`star_power_08_tests.md`** - Testing strategy, quality gates, validation framework

### 4. Validation Reference
- **`star_power_unified_phases_validation.md`** - Consistency validation, handoff readiness confirmation

## CRITICAL SUCCESS FACTORS

### 🔴 NON-NEGOTIABLE REQUIREMENTS
1. **Crown Jewel Preservation**: 523-line VA algorithm MUST be preserved exactly (zero modifications)
2. **Module Size Constraint**: All modules <300 lines (except crown jewel VA algorithm)
3. **Performance Requirements**: 90-second end-to-end processing, <2s VA circuits
4. **Service Architecture**: Follow exact service boundaries defined in Phase 04

### ⚠️ IMPLEMENTATION PRIORITIES
1. **Week 1-2**: TASK-001 (VA Algorithm Extraction) - HIGHEST PRIORITY
2. **Week 3-4**: Service decomposition following Phase 05 task sequence
3. **Week 5-6**: Integration testing and quality validation

### 🛡️ PROTECTION MECHANISMS
- VA algorithm integrity checking with hash validation
- Comprehensive test suite preservation (45+ existing tests)
- Performance benchmarking against original implementation
- Graceful degradation patterns for external service failures

## Implementation Guidance

### Start Here (Task A1.1 from Phase 6)
```yaml
task_id: A1.1
description: Repository Setup & Crown Jewel Extraction
priority: CRITICAL
estimated_time: 2 hours
owner: Developer (Human oversight required for crown jewel)
completion_criteria:
  - New clean repository star-power-clean/ created with proper structure
  - VA algorithm extracted exactly as services/astrology_engine/va_circuit_detector.py
  - All 523 lines copied without any modifications
  - Algorithm integrity hash calculated and stored
```

**Follow with Phase 6 detailed task sequence (A1→A2→A3→B1→B2→B3→C1→C2→C3) for complete 46-hour implementation.**

### Code Quality Standards
- Use exact module structure from Phase 04 architecture
- Follow service boundaries: astrology_engine, news_pipeline, llm_processor, api_gateway
- Implement 5-level error handling hierarchy from Phase 03
- Maintain AI-assisted development documentation patterns

### Technology Stack (MANDATORY)
- **LLM**: Ollama (primary) + OpenAI (fallback) - NO SUBSTITUTIONS
- **Database**: PostgreSQL + Redis caching
- **Ephemeris**: Swiss Ephemeris with test mode fallback
- **API**: FastAPI with service-oriented architecture
- **Testing**: pytest with golden set validation

## Quality Gates (MUST PASS)

### Phase Completion Criteria
- [ ] Crown jewel algorithm extracted with 100% test preservation
- [ ] All modules <300 lines with clear service boundaries
- [ ] End-to-end pipeline processing within 90-second requirement
- [ ] Celebrity identification >90% accuracy
- [ ] Test coverage >85% with integration testing

### Validation Commands
```bash
# Validate VA algorithm integrity
python -m pytest tests/unit/test_va_circuit_detector.py -v

# Performance validation
python -m pytest tests/performance/ -v

# Integration testing  
python -m pytest tests/integration/ -v

# Coverage validation
pytest --cov=services tests/ --cov-report=html
```

## Emergency Contacts & Rollback

### If Crown Jewel Algorithm Modified
1. **STOP IMMEDIATELY** - Do not continue implementation  
2. **ROLLBACK** - Restore original 523-line algorithm exactly
3. **VALIDATE** - Run all 45+ unit tests to confirm restoration
4. **REPORT** - Document what modifications were attempted

### If Performance Requirements Not Met
1. **PROFILE** - Identify performance bottlenecks using Phase 05 optimization guidance
2. **OPTIMIZE** - Apply caching, batching, or approximation strategies
3. **VALIDATE** - Re-test against timing requirements
4. **ESCALATE** - If optimization insufficient, request scope reduction

## Success Validation

### Final Deliverable Checklist
- [ ] Complete service architecture operational
- [ ] Crown jewel VA algorithm preserved and integrated
- [ ] All performance requirements met
- [ ] Comprehensive test suite passing
- [ ] AI-assisted development documentation complete
- [ ] Production deployment configuration ready

## Notes for AI Agents

- **Trust the Documentation**: These phases have been validated for consistency and completeness
- **Follow the Sequence**: Document order is critical for proper context building
- **Preserve Intent**: Crown jewel preservation is the PRIMARY objective
- **Quality First**: Never compromise quality gates for speed
- **Ask for Clarification**: If any requirements conflict, stop and request guidance

---

**HANDOFF COMPLETE**: Star Power SDLC ready for AI agent implementation following this exact sequence and preserving the crown jewel VA algorithm.