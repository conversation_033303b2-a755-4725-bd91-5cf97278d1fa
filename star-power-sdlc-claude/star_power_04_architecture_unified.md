# Star Power Technology Extraction - Phase 4: System Architecture - Unified

## Document Metadata
**Phase**: 04-architecture-unified  
**Created**: 2025-07-30  
**Context**: Synthesized from Claude enterprise architecture + ChatGPT module definitions  
**Focus**: Comprehensive system architecture for extracted Star Power technology with practical module structure and AI-assisted development optimization

## Executive Summary

This unified architecture document defines the complete system design for extracting Star Power's sophisticated astrological technology into a clean, maintainable, AI-assisted development foundation. The architecture combines <PERSON>'s enterprise-grade service-oriented design with ChatGPT's practical module definitions, preserving the crown jewel Vibrational Astrology algorithm while decomposing the monolithic 1062-line main.py into focused, testable services.

## High-Level Architecture Overview (Claude Strategic Foundation)

### System Architecture Principles

1. **Service-Oriented Decomposition**: Break monolithic structure into focused services
2. **Crown Jewel Preservation**: Maintain sophisticated algorithms without modification
3. **AI-Assisted Development Ready**: Optimize for Claude Code/Augment AI collaboration
4. **Fault Tolerance**: Graceful degradation when external services fail
5. **Scalable Foundation**: Support growth from MVP to production system
6. **Module Size Constraint**: No module >300 lines (ChatGPT enhancement)

### Enterprise Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                        API Gateway Layer                        │
├─────────────────────────────────────────────────────────────────┤
│  Authentication │  Rate Limiting │  Request Routing │  Logging  │
└─────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                      Core Service Layer                         │
├──────────────────┬──────────────────┬──────────────────────────┤
│   News Pipeline  │  LLM Processor   │    Astrology Engine      │
│   Service        │  Service         │    Service               │
├──────────────────┼──────────────────┼──────────────────────────┤
│ • GNews Client   │ • OpenAI Client  │ • VA Circuit Detector    │
│ • Content Extract│ • Ollama Client  │ • Swiss Ephemeris Calc   │
│ • Actor Discovery│ • Response Parser│ • Natal Analysis Engine  │
│ • Geo Processing │ • Multi-LLM Logic│ • Mundane Framework      │
└──────────────────┴──────────────────┴──────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                     Data Layer Services                         │
├──────────────────┬──────────────────┬──────────────────────────┤
│  Celebrity DB    │   Cache Layer    │    Primary Database      │
│  Service         │   (Redis)        │    (PostgreSQL)          │
├──────────────────┼──────────────────┼──────────────────────────┤
│ • Name Matching  │ • Ephemeris Cache│ • Articles Storage       │
│ • Birth Data Val │ • LLM Response   │ • Celebrity Profiles     │
│ • Multi-Source   │ • Celebrity Data │ • Analysis Results       │
│ • Confidence Scor│ • Chart Cache    │ • User Sessions          │
└──────────────────┴──────────────────┴──────────────────────────┘
```

## Detailed Component Architecture (Hybrid Approach)

### API Gateway Layer

#### Core Gateway Components
```python
# api_gateway/routing.py (<300 lines)
class APIGateway:
    """Central API routing and middleware coordination"""
    
    def __init__(self):
        self.app = FastAPI(title="Star Power API")
        self.rate_limiter = RateLimiter()
        self.auth_handler = AuthenticationHandler()
        self.request_logger = RequestLogger()
    
    def setup_routes(self):
        """Configure all API routes with middleware"""
        # News processing endpoints
        self.app.include_router(news_router, prefix="/api/v1/news")
        # Analysis endpoints  
        self.app.include_router(analysis_router, prefix="/api/v1/analysis")
        # Celebrity endpoints
        self.app.include_router(celebrity_router, prefix="/api/v1/celebrities")
```

#### Gateway Features (Claude Enterprise Patterns)
- **Request Routing**: Intelligent service distribution
- **Rate Limiting**: API abuse protection (100 requests/minute per user)
- **Authentication**: JWT-based user sessions
- **Request Logging**: Comprehensive audit trail
- **Error Handling**: Standardized error responses
- **API Documentation**: Auto-generated OpenAPI specs

### Core Service Layer Architecture

#### 1. News Pipeline Service (ChatGPT Module Enhancement)

**Service Structure**:
```
services/news_pipeline/
├── gnews_client.py           # GNews API integration (<300 lines)
├── content_extractor.py      # Article content extraction (<300 lines)
├── actor_discovery.py        # Entity/celebrity identification (<300 lines)
├── geo_processor.py          # Location/timezone processing (<300 lines)
└── article_processor.py      # Pipeline orchestration (<300 lines)
```

**Core Module Definitions** (ChatGPT Tactical):
```python
# gnews_client.py - GNews API integration
class GNewsClient:
    """Professional GNews API client with circuit breaker"""
    
    def __init__(self, config: GNewsConfig):
        self.config = config
        self.circuit_breaker = CircuitBreaker()
        self.rate_limiter = RateLimiter(calls=100, period=3600)
    
    async def fetch_articles(self, query: str, **kwargs) -> List[Article]:
        """Fetch articles with comprehensive error handling"""
        pass

# content_extractor.py - Multi-strategy content extraction  
class ContentExtractor:
    """Robust content extraction with fallback strategies"""
    
    def __init__(self):
        self.extractors = [
            BeautifulSoupExtractor(),
            ReadabilityExtractor(),
            NewspaperExtractor()
        ]
    
    async def extract_content(self, url: str) -> ExtractedContent:
        """Extract content using multiple strategies"""
        pass
```

**Enterprise Features** (Claude Framework):
- **Circuit Breaker Pattern**: Prevent cascade failures
- **Multi-Strategy Extraction**: Fallback content extraction methods
- **Async Processing**: Non-blocking article processing
- **Error Recovery**: Graceful handling of news API failures
- **Caching Layer**: Redis integration for performance

#### 2. LLM Processor Service (Hybrid Implementation)

**Service Structure**:
```
services/llm_processor/
├── ollama_client.py          # Local LLM integration (<300 lines)
├── openai_client.py          # OpenAI API fallback (<300 lines)
├── response_processor.py     # Multi-format response parsing (<300 lines)
├── celebrity_enhancer.py     # Celebrity data enhancement (<300 lines)
└── llm_orchestrator.py       # Multi-model coordination (<300 lines)
```

**Multi-Model Strategy** (Claude Strategic + ChatGPT Tactical):
```python
# llm_orchestrator.py - Intelligent model selection
class LLMOrchestrator:
    """Multi-model LLM processing with quality assessment"""
    
    def __init__(self):
        self.models = {
            'primary': OllamaClient(model='dolphin-mixtral'),
            'fallback': OpenAIClient(model='gpt-4'),
            'validator': LocalValidationModel()
        }
        self.quality_assessor = ResponseQualityAssessor()
    
    async def process_with_fallback(self, text: str, task: str) -> LLMResponse:
        """Process with quality assessment and fallback"""
        # Primary attempt with quality scoring
        primary_response = await self.models['primary'].process(text, task)
        quality_score = self.quality_assessor.assess(primary_response)
        
        if quality_score >= self.config.quality_threshold:
            return primary_response
        
        # Fallback to secondary model
        return await self.models['fallback'].process(text, task)
```

**Response Processing** (ChatGPT Enhancement):
```python
# response_processor.py - Handle diverse LLM formats
class ResponseProcessor:
    """Parse multiple LLM response formats robustly"""
    
    def __init__(self):
        self.parsers = [
            ThinkingTagParser(),    # <think>...</think> tags
            JSONExtractor(),        # JSON in mixed content
            StructuredParser(),     # Structured text
            FallbackParser()        # Last resort extraction
        ]
    
    def process_response(self, raw: str, format: str) -> ParsedResponse:
        """Multi-strategy response parsing"""
        pass
```

#### 3. Astrology Engine Service (Crown Jewel Preservation)

**Service Structure** (Critical Crown Jewel Organization):
```
services/astrology_engine/
├── va_circuit_detector.py    # CROWN JEWEL: 523 lines exactly, no changes
├── ephemeris_calculator.py   # Swiss Ephemeris wrapper (<300 lines)
├── natal_analyzer.py         # Natal chart analysis (<300 lines)
├── mundane_analyzer.py       # World event astrology (<300 lines)
├── aspect_engine.py          # Planetary aspect detection (<300 lines)
└── chart_generator.py        # Chart data structure creation (<300 lines)
```

**Crown Jewel Protection** (Claude Strategic Priority):
```python
# va_circuit_detector.py - PRESERVE EXACTLY
"""
Vibrational Astrology Circuit Detection Algorithm
Crown Jewel Technology - DO NOT MODIFY

This file contains the 523-line sophisticated graph-theoretic
implementation for detecting vibrational astrology circuits.
- Multi-harmonic analysis across 7 base harmonics
- Complete clique analysis for circuit detection  
- No comparable open-source implementations exist
- Fully working with 45+ unit tests
"""
class VACircuitDetector:
    """523-line crown jewel algorithm - PRESERVE EXACTLY"""
    # Original algorithm preserved without any modifications
    pass
```

**Supporting Modules** (ChatGPT Module Enhancement):
```python
# aspect_engine.py - Configurable aspect detection
class AspectEngine:
    """Detect planetary aspects with configurable orb systems"""
    
    def __init__(self, config_path: str = "aspect_configs/placidus.json"):
        self.config = self._load_aspect_config(config_path)
        self.orb_calculator = OrbCalculator(self.config)
    
    def detect_aspects(self, chart_data: ChartData) -> List[Aspect]:
        """Detect aspects using configured orb system"""
        # Support for Placidus, Vibrational, Vedic configurations
        pass

# ephemeris_calculator.py - Swiss Ephemeris integration
class EphemerisCalculator:
    """Swiss Ephemeris wrapper with fallback modes"""
    
    def __init__(self):
        self.ephemeris_client = SwissEphemerisClient()
        self.test_mode = TestModeEphemeris()
        self.data_validator = EphemerisDataValidator()
    
    def calculate_positions(self, datetime: datetime, location: Location) -> Positions:
        """Calculate planetary positions with fallback"""
        pass
```

### Data Layer Architecture (Enterprise Foundation)

#### Database Service Organization
```
services/database/
├── celebrity_db_service.py   # Celebrity data management (<300 lines)
├── article_db_service.py     # News article storage (<300 lines)
├── analysis_db_service.py    # Analysis results storage (<300 lines)
├── cache_manager.py          # Redis cache coordination (<300 lines)
└── db_connection_manager.py  # Connection pooling (<300 lines)
```

#### Celebrity Database Service (Multi-Source Integration)
```python
# celebrity_db_service.py - Sophisticated celebrity management
class CelebrityDBService:
    """Multi-source celebrity data with validation"""
    
    def __init__(self):
        self.name_matcher = CelebrityNameMatcher()
        self.birth_data_validator = BirthDataValidator()
        self.data_sources = [
            AstroDatabank(),
            CelebrityBirthDatabase(),
            WikipediaExtractor()
        ]
    
    async def find_celebrity(self, name: str, context: str) -> CelebrityMatch:
        """Multi-stage celebrity matching with context validation"""
        # Fuzzy matching with cultural awareness
        # Multi-source birth data validation
        # Confidence scoring and ranking
        pass
```

## Component Flow Architecture (ChatGPT Visual Enhancement)

### High-Level Data Flow
```
┌─────────────────┐    ┌──────────────────┐    ┌────────────────┐
│   News Ingestion│ -> │ Entity Extractor │ -> │   Ephemeris    │
│ (Articles, RSS) │    │ (Actors, Dates)  │    │  (Chart Gen)   │
└─────────────────┘    └──────────────────┘    └────────────────┘
                                                        │
                                                ┌───────▼────────┐
                                                │ aspect_engine  │
                                                └───────┬────────┘
                                                        │
                                        ┌───────────────▼──────────────┐
                                        │ lookup_table + VA Circuits   │
                                        └───────────────┬──────────────┘
                                                        │
                                        ┌───────────────▼──────────────┐
                                        │ Astro Summary Generator      │
                                        └───────────────┬──────────────┘
                                                        │
                                                ┌───────▼────────┐
                                                │ Frontend UI    │
                                                └────────────────┘
```

### Detailed Service Interaction Flow
```
API Gateway
    │
    ├── POST /api/v1/news/process
    │   │
    │   ├── News Pipeline Service
    │   │   ├── GNews Client → External API
    │   │   ├── Content Extractor → Article Processing
    │   │   └── Actor Discovery → Entity Extraction
    │   │
    │   ├── LLM Processor Service  
    │   │   ├── Ollama Client → Local LLM
    │   │   ├── OpenAI Client → External API (fallback)
    │   │   └── Celebrity Enhancer → Data Enrichment
    │   │
    │   └── Astrology Engine Service
    │       ├── Ephemeris Calculator → Swiss Ephemeris
    │       ├── VA Circuit Detector → Crown Jewel Algorithm
    │       ├── Aspect Engine → Configurable Analysis
    │       └── Chart Generator → Structured Data
    │
    └── GET /api/v1/analysis/{id}
        │
        └── Database Service → Cached Results
```

## Configuration Architecture (Environment Management)

### Configuration Service Structure
```
shared/config/
├── base_config.py            # Base configuration patterns (<300 lines)
├── environment_config.py     # Environment-specific settings (<300 lines)
├── service_config.py         # Service-specific configuration (<300 lines)
└── secrets_manager.py        # Secret/credential management (<300 lines)
```

### Environment-Aware Configuration
```python
# environment_config.py - Multi-environment support
class EnvironmentConfig:
    """Environment-aware configuration management"""
    
    def __init__(self, environment: str = 'development'):
        self.environment = environment
        self.config = self._load_environment_config()
    
    @property
    def database_config(self) -> DatabaseConfig:
        return DatabaseConfig(
            url=self._get_database_url(),
            pool_size=self.config['database']['pool_size'],
            echo=self.environment == 'development'
        )
    
    @property 
    def ephemeris_config(self) -> EphemerisConfig:
        return EphemerisConfig(
            test_mode=self.config['ephemeris']['test_mode'],
            data_path=self._resolve_ephemeris_path(),
            fallback_enabled=True
        )
```

## API Architecture & Contracts (Practical Implementation)

### RESTful API Design
```python
# API endpoint definitions with clear contracts
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

news_router = APIRouter()

@news_router.post("/process", response_model=ProcessingResult)
async def process_news_article(article: ArticleInput) -> ProcessingResult:
    """
    Process news article through complete astrological analysis pipeline
    
    Args:
        article: News article data with URL or content
    
    Returns:
        ProcessingResult: Complete analysis with celebrity matches,
                         astrological insights, and UI rendering data
    
    Raises:
        HTTPException: 400 for invalid input, 503 for service unavailable
    """
    pass

@news_router.get("/analysis/{analysis_id}", response_model=AnalysisResult)  
async def get_analysis_result(analysis_id: str) -> AnalysisResult:
    """Retrieve completed analysis by ID"""
    pass
```

### Data Contract Models
```python
# Comprehensive data models for API contracts
class ArticleInput(BaseModel):
    """Input model for article processing"""
    url: Optional[str] = None  
    content: Optional[str] = None
    title: Optional[str] = None
    source: Optional[str] = None

class ProcessingResult(BaseModel):
    """Complete processing result"""
    analysis_id: str
    status: str  # 'processing', 'completed', 'failed'
    article_data: ArticleData
    celebrities: List[CelebrityMatch]
    charts: List[ChartData]
    va_circuits: List[VACircuitData]
    astrological_summary: str
    ui_highlights: List[UIHighlight]
    processing_time: float

class UIHighlight(BaseModel):
    """Frontend highlighting data"""
    text_span: str
    planet: str
    sign: str  
    house: int
    interpretation: str
    confidence: float
```

## Testing Architecture (Quality Assurance Framework)

### Test Service Organization
```
tests/
├── unit/
│   ├── test_va_circuit_detector.py    # Crown jewel tests (45+ existing)
│   ├── test_ephemeris_calculator.py   # Swiss Ephemeris accuracy
│   ├── test_llm_processor.py          # Multi-model LLM testing  
│   └── test_celebrity_matching.py     # Name matching accuracy
├── integration/
│   ├── test_news_pipeline.py          # End-to-end news processing
│   ├── test_analysis_pipeline.py      # Complete analysis workflow
│   └── test_api_contracts.py          # API endpoint validation
├── fixtures/
│   ├── sample_articles.json           # Test article data
│   ├── celebrity_test_data.json       # Known celebrity matches
│   ├── expected_charts.json           # Astrological test cases
│   └── va_circuit_examples.json       # Circuit detection examples
└── performance/
    ├── test_timing_requirements.py    # Performance validation
    ├── test_concurrent_processing.py  # Scalability testing
    └── test_memory_usage.py           # Resource utilization
```

### Golden Set Testing Framework (ChatGPT Enhancement)
```python
# Golden set validation for astrological accuracy
class GoldenSetValidator:
    """Validate against known-good astrological analyses"""
    
    def __init__(self):
        self.golden_articles = self._load_golden_articles()
        self.expected_results = self._load_expected_results()
    
    def validate_va_algorithm(self) -> ValidationResults:
        """Test VA algorithm against documented celebrity circuits"""
        results = ValidationResults()
        
        for celebrity in self.golden_articles['celebrities']:
            va_result = self.va_engine.analyze(celebrity['birth_data'])
            expected = celebrity['documented_circuits']
            
            accuracy = self._compare_circuits(va_result, expected)
            results.add_result(celebrity['name'], accuracy)
        
        return results
```

## Deployment Architecture (Production Readiness)

### Container Architecture
```yaml
# docker-compose.production.yml
version: '3.8'
services:
  api-gateway:
    build:
      context: .
      dockerfile: Dockerfile.api-gateway
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
    depends_on:
      - news-pipeline
      - llm-processor
      - astrology-engine
      - database
      - redis

  news-pipeline:
    build:
      context: .
      dockerfile: Dockerfile.news-pipeline
    environment:
      - GNEWS_API_KEY=${GNEWS_API_KEY}
    depends_on:
      - database

  llm-processor:
    build:
      context: .
      dockerfile: Dockerfile.llm-processor  
    environment:
      - OLLAMA_HOST=${OLLAMA_HOST}
      - OPENAI_API_KEY=${OPENAI_API_KEY}

  astrology-engine:
    build:
      context: .
      dockerfile: Dockerfile.astrology-engine
    volumes:
      - ephemeris_data:/usr/share/swisseph
    depends_on:
      - database
      - redis

  database:
    image: postgres:15
    environment:
      - POSTGRES_DB=starpower
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
  ephemeris_data:
```

## Monitoring & Observability Architecture

### Monitoring Service Structure
```python
# monitoring/observability_system.py
class ObservabilitySystem:
    """Comprehensive monitoring for production deployment"""
    
    def __init__(self):
        self.metrics_collector = PrometheusMetrics()
        self.log_aggregator = StructuredLogging()
        self.alert_manager = AlertManager()
        self.dashboard_generator = GrafanaDashboards()
    
    def setup_monitoring(self):
        """Configure complete monitoring stack"""
        # Service health metrics
        self.metrics_collector.track_service_health()
        
        # VA algorithm performance monitoring
        self.metrics_collector.track_va_performance()
        
        # Celebrity identification accuracy tracking
        self.metrics_collector.track_celebrity_accuracy()
        
        # API response time monitoring
        self.metrics_collector.track_api_performance()
```

## AI-Assisted Development Architecture (Framework Integration)

### Claude Code Integration Points
```python
# ai_development/claude_integration.py
class ClaudeCodeIntegration:
    """Optimize architecture for AI-assisted development"""
    
    def __init__(self):
        self.context_manager = AIContextManager()
        self.documentation_generator = DocumentationGenerator()
        self.code_analyzer = ArchitectureAnalyzer()
    
    def generate_development_context(self, service: str) -> DevelopmentContext:
        """Prepare optimal context for Claude Code assistance"""
        context = DevelopmentContext()
        
        # Add service architecture documentation
        context.add_service_architecture(service)
        
        # Add astrological domain knowledge
        context.add_domain_context(service)
        
        # Add testing strategies
        context.add_testing_context(service)
        
        return context
```

### Service Development Templates
```python
# Templates for AI-assisted service development
class ServiceTemplate:
    """Standardized service patterns for AI development"""
    
    @staticmethod
    def generate_service_skeleton(service_name: str) -> ServiceSkeleton:
        """Generate standardized service structure"""
        return ServiceSkeleton(
            name=service_name,
            max_lines_per_module=300,
            error_handling_pattern='circuit_breaker',
            logging_pattern='structured',
            testing_pattern='golden_set',
            configuration_pattern='environment_aware'
        )
```

## Architecture Validation & Success Criteria

### Architecture Quality Gates
- [ ] All modules <300 lines with clear single responsibility
- [ ] Service boundaries clearly defined with standardized interfaces
- [ ] Crown jewel VA algorithm preserved in isolated, protected module
- [ ] Fault tolerance patterns implemented across all services
- [ ] Configuration management environment-aware and AI-friendly
- [ ] Testing framework supports unit, integration, and golden set validation
- [ ] API contracts well-defined with comprehensive data models
- [ ] Deployment architecture production-ready with monitoring

### Performance Architecture Validation
- [ ] Service response times meet specified requirements (<200ms ephemeris, <2s VA circuits)
- [ ] Concurrent processing architecture supports 20+ simultaneous analyses
- [ ] Memory management architecture prevents resource leaks
- [ ] Caching architecture optimizes repeated calculations
- [ ] Error handling architecture enables graceful degradation

### AI-Assisted Development Validation
- [ ] Service documentation optimized for AI tool consumption
- [ ] Code structure enables independent service development by AI
- [ ] Domain knowledge sufficiently documented for AI comprehension
- [ ] Testing patterns support AI-driven test generation
- [ ] Configuration patterns enable AI-driven deployment automation

---

*Phase 4 Unified Architecture Complete: Enterprise-grade service architecture with practical module structure optimized for Crown Jewel preservation and AI-assisted development.*