#!/bin/bash

# ==========================================
# Frontend Synchronization Script
# ==========================================
# Pulls latest layout changes from star-power-fe repository
# while preserving local logic integration changes.
#
# USAGE: ./sync-frontend.sh
# ==========================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FE_REPO_URL="https://github.com/VerbalClaymore/star-power-fe.git"
FE_BRANCH="main"
FRONTEND_DIR="frontend-integration"
TEMP_CLONE_DIR="temp-fe-sync"

echo -e "${BLUE}🔄 Starting Frontend Synchronization...${NC}"

# Function to check if directory exists and is git repo
check_git_repo() {
    if [ ! -d "$1/.git" ]; then
        echo -e "${RED}❌ Error: $1 is not a git repository${NC}"
        exit 1
    fi
}

# Function to backup current state
backup_current_state() {
    echo -e "${YELLOW}📦 Creating backup of current state...${NC}"
    
    # Create backup with timestamp
    BACKUP_DIR="backups/frontend-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    if [ -d "$FRONTEND_DIR" ]; then
        cp -r "$FRONTEND_DIR" "$BACKUP_DIR/"
        echo -e "${GREEN}✅ Backup created: $BACKUP_DIR${NC}"
    fi
}

# Function to identify ownership
get_file_ownership() {
    local file="$1"
    
    if grep -q "OWNERSHIP: LAYOUT" "$file" 2>/dev/null; then
        echo "LAYOUT"
    elif grep -q "OWNERSHIP: LOGIC" "$file" 2>/dev/null; then
        echo "LOGIC"
    elif grep -q "OWNERSHIP: SHARED" "$file" 2>/dev/null; then
        echo "SHARED"
    else
        # Default rules based on file path
        case "$file" in
            */components/ui/*|*/components/ArticleCard.tsx|*/components/TopNavigation.tsx|*/components/BottomNavigation.tsx|*/components/PlanetIcon.tsx)
                echo "LAYOUT" ;;
            */services/*|*/hooks/useStarPowerApi.ts|server/*)
                echo "LOGIC" ;;
            */types/api.ts|shared/schema.ts|*/pages/home.tsx|*/pages/article.tsx|*/pages/actor-profile.tsx|*/pages/search.tsx)
                echo "SHARED" ;;
            tailwind.config.ts|postcss.config.js|components.json|*/index.css|*/lib/utils.ts)
                echo "LAYOUT" ;;
            package.json|tsconfig.json|vite.config.ts|*.env*)
                echo "SHARED" ;;
            *)
                echo "UNKNOWN" ;;
        esac
    fi
}

# Function to sync layout files
sync_layout_files() {
    echo -e "${BLUE}🎨 Syncing LAYOUT files from star-power-fe...${NC}"
    
    # Clone the frontend repo to temp directory
    if [ -d "$TEMP_CLONE_DIR" ]; then
        rm -rf "$TEMP_CLONE_DIR"
    fi
    
    git clone --depth 1 --branch "$FE_BRANCH" "$FE_REPO_URL" "$TEMP_CLONE_DIR"
    
    # Find all files in the temp clone
    find "$TEMP_CLONE_DIR" -type f -name "*.tsx" -o -name "*.ts" -o -name "*.css" -o -name "*.json" -o -name "*.js" | while read -r file; do
        # Get relative path
        rel_path="${file#$TEMP_CLONE_DIR/}"
        
        # Skip if it's a git file
        if [[ "$rel_path" == .git* ]]; then
            continue
        fi
        
        # Determine ownership
        ownership=$(get_file_ownership "$file")
        
        # Only sync LAYOUT files and some SHARED files
        if [ "$ownership" = "LAYOUT" ] || ( [ "$ownership" = "SHARED" ] && [[ "$rel_path" == *.json ]] ); then
            target_file="$FRONTEND_DIR/$rel_path"
            
            # Create directory if it doesn't exist
            mkdir -p "$(dirname "$target_file")"
            
            # Copy the file
            cp "$file" "$target_file"
            echo -e "${GREEN}📄 Updated: $rel_path ($ownership)${NC}"
        elif [ "$ownership" = "LOGIC" ]; then
            echo -e "${YELLOW}⏭️  Skipped: $rel_path (LOGIC - preserved)${NC}"
        elif [ "$ownership" = "SHARED" ]; then
            echo -e "${YELLOW}⚠️  Manual review needed: $rel_path (SHARED)${NC}"
        fi
    done
    
    # Cleanup temp directory
    rm -rf "$TEMP_CLONE_DIR"
}

# Function to verify sync integrity
verify_sync() {
    echo -e "${BLUE}🔍 Verifying sync integrity...${NC}"
    
    # Check that critical LOGIC files weren't overwritten
    local logic_files=(
        "$FRONTEND_DIR/client/src/services/starPowerApi.ts"
        "$FRONTEND_DIR/client/src/hooks/useStarPowerApi.ts"
    )
    
    for file in "${logic_files[@]}"; do
        if [ -f "$file" ] && grep -q "OWNERSHIP: LOGIC" "$file"; then
            echo -e "${GREEN}✅ Protected: $(basename "$file")${NC}"
        else
            echo -e "${RED}⚠️  Warning: $(basename "$file") may have been overwritten${NC}"
        fi
    done
    
    # Check that LAYOUT files have proper ownership headers
    find "$FRONTEND_DIR/client/src/components" -name "*.tsx" | head -3 | while read -r file; do
        if grep -q "OWNERSHIP:" "$file"; then
            echo -e "${GREEN}✅ Header found: $(basename "$file")${NC}"
        else
            echo -e "${YELLOW}⚠️  Missing header: $(basename "$file")${NC}"
        fi
    done
}

# Main execution
main() {
    echo -e "${BLUE}=================================${NC}"
    echo -e "${BLUE}Frontend Sync - Star Power Project${NC}"
    echo -e "${BLUE}=================================${NC}"
    
    # Verify we're in the right directory
    if [ ! -d ".git" ]; then
        echo -e "${RED}❌ Error: Must be run from the root of the star-power repository${NC}"
        exit 1
    fi
    
    # Check if frontend directory exists
    if [ ! -d "$FRONTEND_DIR" ]; then
        echo -e "${RED}❌ Error: $FRONTEND_DIR directory not found${NC}"
        exit 1
    fi
    
    # Create backups directory if it doesn't exist
    mkdir -p backups
    
    # Execute sync process
    backup_current_state
    sync_layout_files
    verify_sync
    
    echo -e "${BLUE}=================================${NC}"
    echo -e "${GREEN}✅ Frontend sync completed successfully!${NC}"
    echo -e "${BLUE}=================================${NC}"
    echo
    echo -e "${YELLOW}Next steps:${NC}"
    echo -e "1. Review any SHARED files that need manual attention"
    echo -e "2. Test the application: cd $FRONTEND_DIR && npm run dev"
    echo -e "3. Commit changes if everything looks good"
    echo
}

# Run main function
main "$@"