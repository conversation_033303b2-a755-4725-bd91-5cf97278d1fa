# aspect_engine.py – Core Specification

## Purpose
Compute angular relationships (aspects) between planetary positions in one or more charts.
Supports multiple astrological schools (Placidus, Vibrational, Vedic, etc.) through configurable aspect definitions.

## Responsibilities
- `calculate_aspects(chart_data)`: Returns a list of aspects between all planet pairs.
- `is_aspect(angle, type, orb_tolerance)`: Check if a given angle matches an aspect definition.
- `configure_aspects(custom_config)`: Load custom aspect rules for different traditions.

## Input Example
```json
{
  "planet_positions": {
    "sun": 134.0,
    "moon": 220.5,
    "mars": 135.0
  },
  "aspect_definitions": {
    "conjunction": 0,
    "square": 90,
    "trine": 120
  },
  "orb": 6
}
```

## Output Example
```json
[
  { "pair": ["sun", "mars"], "type": "trine", "angle": 1.0 },
  { "pair": ["moon", "mars"], "type": "square", "angle": 0.5 }
]
```
