#!/usr/bin/env python3
"""
API Gateway Test Script

Quick test to verify the API Gateway can start up and basic endpoints work.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_api_gateway_startup():
    """Test that the API Gateway can start up without errors."""
    print("🚀 Testing API Gateway Startup...")
    
    try:
        # Import the FastAPI app
        from services.api_gateway.main import app
        print("✅ API Gateway app imported successfully")
        
        # Test that we can access the app
        print(f"✅ App title: {app.title}")
        print(f"✅ App version: {app.version}")
        
        # Check routes are registered
        routes = [route.path for route in app.routes]
        expected_routes = [
            "/health",
            "/api/articles",
            "/api/celebrities", 
            "/api/astrology/analyze",
            "/api/pipeline/process"
        ]
        
        for expected_route in expected_routes:
            if any(expected_route in route for route in routes):
                print(f"✅ Route registered: {expected_route}")
            else:
                print(f"❌ Route missing: {expected_route}")
        
        print("\n📋 All registered routes:")
        for route in routes:
            if hasattr(route, 'methods'):
                methods = getattr(route, 'methods', set())
                print(f"  {route.path} - {methods}")
        
        return True
        
    except Exception as e:
        print(f"❌ API Gateway startup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_service_imports():
    """Test that all service dependencies can be imported."""
    print("\n🔧 Testing Service Imports...")
    
    services_to_test = [
        ("ArticleDBService", "services.database.article_db_service"),
        ("CelebrityDBService", "services.database.celebrity_db_service"),
        ("NewsIngestionService", "services.news_pipeline.news_ingestion_service"),
        ("LLMOrchestrator", "services.llm_processor.llm_orchestrator"),
        ("EphemerisCalculator", "services.astrology_engine.ephemeris_calculator"),
        ("AspectEngine", "services.astrology_engine.aspect_engine"),
        ("VACircuitDetector", "services.astrology_engine.va_circuit_detector"),
        ("SimplePipelineOrchestrator", "services.pipeline_orchestrator"),
    ]
    
    success_count = 0
    
    for service_name, module_path in services_to_test:
        try:
            module = __import__(module_path, fromlist=[service_name])
            service_class = getattr(module, service_name)
            print(f"✅ {service_name} imported successfully")
            success_count += 1
        except Exception as e:
            print(f"❌ {service_name} import failed: {e}")
    
    print(f"\n📊 Service Import Results: {success_count}/{len(services_to_test)} successful")
    return success_count == len(services_to_test)

async def test_model_imports():
    """Test that all Pydantic models can be imported."""
    print("\n📝 Testing Model Imports...")
    
    try:
        from services.api_gateway.models import (
            ArticleProcessRequest, ArticleResponse, ArticleListResponse,
            CelebrityResponse, CelebrityListResponse, CelebrityIdentifyRequest,
            AstrologyAnalyzeRequest, AstrologyResponse, ChartResponse,
            PipelineProcessRequest, PipelineStatusResponse,
            ErrorResponse, HealthResponse
        )
        print("✅ All API Gateway models imported successfully")
        
        # Test model instantiation
        error_response = ErrorResponse(
            error="test_error",
            message="Test error message",
            details={"test": "data"}
        )
        print(f"✅ ErrorResponse model works: {error_response.error}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all API Gateway tests."""
    print("🌟 API GATEWAY INTEGRATION TEST")
    print("=" * 50)
    
    tests = [
        ("Service Imports", test_service_imports),
        ("Model Imports", test_model_imports),
        ("API Gateway Startup", test_api_gateway_startup),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        print("-" * 30)
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! API Gateway is ready for use.")
        return True
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed. Check the output above.")
        return False

if __name__ == "__main__":
    asyncio.run(main())
