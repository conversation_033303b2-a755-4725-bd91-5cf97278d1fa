"""
Integration Tests for Ephemeris System

Tests for complete ephemeris system integration including data management,
calculation service, and integration with VA algorithm.
"""

import pytest
from datetime import datetime
import time

from services.astrology_engine import (
    EphemerisCalculator, EphemerisDataManager, EphemerisConfig,
    ChartCalculationRequest, Location, VACircuitDetector,
    create_test_ephemeris, create_circuit_test_ephemeris,
    VA_DEFAULT_BODIES
)


class TestEphemerisSystemIntegration:
    """Test complete ephemeris system integration."""
    
    def test_end_to_end_chart_calculation(self):
        """Test complete end-to-end chart calculation workflow."""
        # Initialize components
        data_manager = EphemerisDataManager()
        calculator = EphemerisCalculator()
        
        # Verify system status
        status = data_manager.ensure_ephemeris_availability()
        assert status.status in ['production', 'test_mode']
        
        # Create calculation request
        request = ChartCalculationRequest(
            datetime=datetime(2000, 1, 1, 12, 0, 0),
            location=Location(
                latitude=40.7128,
                longitude=-74.0060,
                timezone="America/New_York",
                name="New York City"
            ),
            celestial_bodies=VA_DEFAULT_BODIES
        )
        
        # Perform calculation
        chart = calculator.calculate_chart(request)
        
        # Verify results
        assert len(chart.celestial_bodies) == len(VA_DEFAULT_BODIES)
        assert chart.calculation_time == request.datetime
        assert chart.location.name == "New York City"
        
        # Verify all requested bodies are present
        for body_name in VA_DEFAULT_BODIES:
            assert body_name in chart.celestial_bodies
            position = chart.celestial_bodies[body_name]
            assert 0.0 <= position.lon <= 360.0
            assert position.sign is not None
            assert 0.0 <= position.sign_degree <= 30.0
    
    def test_ephemeris_va_algorithm_integration(self):
        """Test integration between ephemeris calculator and VA algorithm."""
        calculator = EphemerisCalculator()
        detector = VACircuitDetector()
        
        # Create chart with sufficient planets for circuit detection
        request = ChartCalculationRequest(
            datetime=datetime(2000, 6, 15, 12, 0, 0),  # Mid-year for varied positions
            location=Location(latitude=51.5074, longitude=-0.1278, timezone="Europe/London"),
            celestial_bodies=['Sun', 'Moon', 'Mercury', 'Venus', 'Mars', 'Jupiter', 'Saturn', 'Uranus']
        )
        
        chart = calculator.calculate_chart(request)
        
        # Run VA analysis
        va_result = detector._analyze_vibrational_astrology(chart)
        
        # Verify VA analysis results
        assert 'total_circuits_found' in va_result
        assert 'harmonics_analyzed' in va_result
        assert 'summary' in va_result
        assert len(va_result['harmonics_analyzed']) == 7
        
        # Verify performance
        assert va_result['total_circuits_found'] >= 0
    
    def test_multiple_location_calculations(self):
        """Test calculations for multiple geographic locations."""
        calculator = EphemerisCalculator()
        test_date = datetime(2000, 1, 1, 12, 0, 0)
        
        locations = [
            Location(latitude=40.7128, longitude=-74.0060, timezone="America/New_York", name="New York"),
            Location(latitude=51.5074, longitude=-0.1278, timezone="Europe/London", name="London"),
            Location(latitude=35.6762, longitude=139.6503, timezone="Asia/Tokyo", name="Tokyo"),
            Location(latitude=-33.8688, longitude=151.2093, timezone="Australia/Sydney", name="Sydney")
        ]
        
        charts = []
        for location in locations:
            request = ChartCalculationRequest(
                datetime=test_date,
                location=location,
                celestial_bodies=['Sun', 'Moon', 'Mercury', 'Venus', 'Mars']
            )
            chart = calculator.calculate_chart(request)
            charts.append(chart)
        
        # Verify all calculations succeeded
        assert len(charts) == 4
        
        # Verify planetary positions are consistent (same time, different locations)
        # Planetary positions should be very similar (within small tolerance for time zones)
        sun_positions = [chart.celestial_bodies['Sun'].lon for chart in charts]
        for i in range(1, len(sun_positions)):
            assert abs(sun_positions[i] - sun_positions[0]) < 1.0  # Within 1 degree
    
    def test_time_series_calculations(self):
        """Test calculations across a time series."""
        calculator = EphemerisCalculator()
        location = Location(latitude=0.0, longitude=0.0, timezone="UTC", name="Greenwich")
        
        # Calculate positions for first day of each month in 2000
        charts = []
        for month in range(1, 13):
            request = ChartCalculationRequest(
                datetime=datetime(2000, month, 1, 12, 0, 0),
                location=location,
                celestial_bodies=['Sun', 'Moon', 'Mercury']
            )
            chart = calculator.calculate_chart(request)
            charts.append(chart)
        
        # Verify all calculations succeeded
        assert len(charts) == 12
        
        # Verify Sun progression through zodiac
        sun_positions = [chart.celestial_bodies['Sun'].lon for chart in charts]
        
        # Sun should progress approximately 30 degrees per month
        for i in range(1, len(sun_positions)):
            # Handle year wrap-around
            diff = (sun_positions[i] - sun_positions[i-1]) % 360
            assert 25.0 < diff < 35.0  # Approximately 30 degrees ± 5
    
    def test_performance_under_load(self):
        """Test system performance under concurrent load."""
        calculator = EphemerisCalculator()
        
        # Prepare multiple calculation requests
        requests = []
        for i in range(10):
            request = ChartCalculationRequest(
                datetime=datetime(2000, 1, 1 + i, 12, 0, 0),
                location=Location(
                    latitude=40.0 + i,
                    longitude=-74.0 + i,
                    timezone="UTC"
                ),
                celestial_bodies=['Sun', 'Moon', 'Mercury', 'Venus', 'Mars']
            )
            requests.append(request)
        
        # Perform calculations and measure time
        start_time = time.time()
        charts = []
        for request in requests:
            chart = calculator.calculate_chart(request)
            charts.append(chart)
        total_time = time.time() - start_time
        
        # Verify all calculations succeeded
        assert len(charts) == 10
        
        # Verify performance (should average < 200ms per chart)
        average_time = total_time / len(charts)
        assert average_time < 0.2
        
        # Verify results quality
        for chart in charts:
            assert len(chart.celestial_bodies) == 5
    
    def test_error_handling_and_recovery(self):
        """Test error handling and graceful recovery."""
        # Test with invalid location
        calculator = EphemerisCalculator()
        
        with pytest.raises(ValueError):
            invalid_location = Location(
                latitude=91.0,  # Invalid latitude
                longitude=0.0,
                timezone="UTC"
            )
        
        # Test with valid but extreme location
        extreme_request = ChartCalculationRequest(
            datetime=datetime(2000, 1, 1, 12, 0, 0),
            location=Location(latitude=89.9, longitude=179.9, timezone="UTC"),
            celestial_bodies=['Sun', 'Moon']
        )
        
        chart = calculator.calculate_chart(extreme_request)
        assert len(chart.celestial_bodies) == 2
    
    def test_data_consistency_validation(self):
        """Test data consistency across different calculation methods."""
        calculator = EphemerisCalculator()
        
        # Calculate same chart using different approaches
        base_request = ChartCalculationRequest(
            datetime=datetime(2000, 1, 1, 12, 0, 0),
            location=Location(latitude=40.7128, longitude=-74.0060, timezone="America/New_York"),
            celestial_bodies=['Sun', 'Moon', 'Mercury']
        )
        
        # Method 1: Single calculation
        chart1 = calculator.calculate_chart(base_request)
        
        # Method 2: Multiple individual calculations
        chart2 = calculator.calculate_chart(base_request)
        
        # Results should be identical
        for body_name in ['Sun', 'Moon', 'Mercury']:
            pos1 = chart1.celestial_bodies[body_name]
            pos2 = chart2.celestial_bodies[body_name]
            
            assert abs(pos1.lon - pos2.lon) < 0.001  # Within 0.001 degrees
            assert abs(pos1.speed - pos2.speed) < 0.001
    
    def test_test_mode_fallback_integration(self):
        """Test complete system operation in test mode."""
        # Force test mode
        config = EphemerisConfig(test_mode=True)
        calculator = EphemerisCalculator(config)
        detector = VACircuitDetector()
        
        # Verify test mode is active
        assert calculator.is_test_mode() is True
        
        # Perform full workflow in test mode
        request = ChartCalculationRequest(
            datetime=datetime(2000, 1, 1, 12, 0, 0),
            location=Location(latitude=40.7128, longitude=-74.0060, timezone="America/New_York"),
            celestial_bodies=VA_DEFAULT_BODIES
        )
        
        chart = calculator.calculate_chart(request)
        va_result = detector._analyze_vibrational_astrology(chart)
        
        # Verify complete workflow succeeds in test mode
        assert len(chart.celestial_bodies) == len(VA_DEFAULT_BODIES)
        assert va_result['total_circuits_found'] >= 0
        assert len(va_result['harmonics_analyzed']) == 7
    
    def test_circuit_detection_with_ephemeris(self):
        """Test circuit detection using ephemeris-calculated positions."""
        calculator = EphemerisCalculator()
        detector = VACircuitDetector()
        
        # Use circuit test ephemeris for known circuit formation
        circuit_ephemeris = create_circuit_test_ephemeris()
        
        # Run VA analysis on circuit-designed ephemeris
        va_result = detector._analyze_vibrational_astrology(circuit_ephemeris)
        
        # Should detect circuits in the designed configuration
        assert va_result['total_circuits_found'] > 0
        
        # Compare with regular ephemeris calculation
        regular_request = ChartCalculationRequest(
            datetime=datetime(2000, 1, 1, 12, 0, 0),
            location=Location(latitude=0.0, longitude=0.0, timezone="UTC"),
            celestial_bodies=list(circuit_ephemeris.celestial_bodies.keys())
        )
        
        regular_chart = calculator.calculate_chart(regular_request)
        regular_result = detector._analyze_vibrational_astrology(regular_chart)
        
        # Both should complete successfully
        assert regular_result['total_circuits_found'] >= 0
    
    def test_system_status_monitoring(self):
        """Test system status monitoring and reporting."""
        calculator = EphemerisCalculator()
        data_manager = EphemerisDataManager()
        
        # Get comprehensive status
        calc_status = calculator.get_status_info()
        ephemeris_status = data_manager.ensure_ephemeris_availability()
        
        # Verify status information completeness
        assert 'ephemeris_status' in calc_status
        assert 'test_mode' in calc_status
        assert 'swiss_ephemeris_available' in calc_status
        assert 'calculations_performed' in calc_status
        
        # Verify ephemeris status
        assert ephemeris_status.status in ['production', 'test_mode', 'error']
        
        # Perform calculation to update stats
        request = ChartCalculationRequest(
            datetime=datetime(2000, 1, 1, 12, 0, 0),
            location=Location(latitude=0.0, longitude=0.0, timezone="UTC"),
            celestial_bodies=['Sun', 'Moon']
        )
        calculator.calculate_chart(request)
        
        # Verify stats updated
        updated_status = calculator.get_status_info()
        assert updated_status['calculations_performed'] > calc_status['calculations_performed']
