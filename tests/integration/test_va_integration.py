# VA Algorithm Integration Test Suite
"""
Integration tests for the crown jewel VA algorithm within the larger system.

These tests validate the VA algorithm's integration with other services
and ensure end-to-end functionality works correctly.
"""

import pytest
import json
from pathlib import Path
from typing import Dict, Any

from services.astrology_engine.va_circuit_detector import (
    VACircuitDetector, 
    EphemerisData, 
    CelestialBodyPosition
)


class TestVAGoldenSetIntegration:
    """Integration tests using golden set data."""
    
    def setup_method(self):
        """Set up integration test fixtures."""
        self.detector = VACircuitDetector()
        self.golden_set_path = Path("golden_set")
    
    def load_golden_set_chart(self, chart_name: str = "example_chart.json") -> Dict[str, Any]:
        """Load chart data from golden set."""
        chart_path = self.golden_set_path / "charts" / chart_name
        
        if not chart_path.exists():
            pytest.skip(f"Golden set chart not found: {chart_path}")
        
        with open(chart_path, 'r') as f:
            return json.load(f)
    
    def convert_chart_to_ephemeris(self, chart_data: Dict[str, Any]) -> EphemerisData:
        """Convert golden set chart data to ephemeris format."""
        ephemeris = EphemerisData()
        ephemeris.celestial_bodies = {}
        
        placements = chart_data.get("placements", {})
        
        for planet_name, placement in placements.items():
            # Convert sign and degree to longitude
            sign_name = placement.get("sign", "Aries")
            degree = placement.get("degree", 0.0)
            
            # Sign to longitude conversion
            sign_to_longitude = {
                "Aries": 0, "Taurus": 30, "Gemini": 60, "Cancer": 90,
                "Leo": 120, "Virgo": 150, "Libra": 180, "Scorpio": 210,
                "Sagittarius": 240, "Capricorn": 270, "Aquarius": 300, "Pisces": 330
            }
            
            base_longitude = sign_to_longitude.get(sign_name, 0)
            longitude = base_longitude + degree
            
            ephemeris.celestial_bodies[planet_name.title()] = CelestialBodyPosition(
                lon=longitude, lat=0.0, speed=1.0
            )
        
        # Add minimum required planets if not present
        required_planets = ["Sun", "Moon", "Mercury"]
        for planet in required_planets:
            if planet not in ephemeris.celestial_bodies:
                # Add with default position
                ephemeris.celestial_bodies[planet] = CelestialBodyPosition(
                    lon=0.0, lat=0.0, speed=1.0
                )
        
        return ephemeris
    
    def test_golden_set_chart_analysis(self):
        """Test VA analysis with golden set chart data."""
        chart_data = self.load_golden_set_chart()
        ephemeris = self.convert_chart_to_ephemeris(chart_data)
        
        # Run VA analysis
        result = self.detector._analyze_vibrational_astrology(ephemeris)
        
        # Validate results structure
        assert "total_circuits_found" in result
        assert "harmonics_analyzed" in result
        assert "active_harmonics" in result
        assert "circuits_by_harmonic" in result
        assert "summary" in result
        
        # Validate harmonics analyzed
        assert result["harmonics_analyzed"] == [1, 5, 7, 8, 9, 11, 13]
        
        # Validate circuit data structure
        for harmonic_key, circuits in result["circuits_by_harmonic"].items():
            assert harmonic_key.startswith("H")
            assert isinstance(circuits, list)
            
            for circuit in circuits:
                assert "harmonic" in circuit
                assert "planets" in circuit
                assert "circuit_type" in circuit
                assert "strength" in circuit
                assert len(circuit["planets"]) >= 3
    
    def test_celebrity_chart_processing(self):
        """Test VA analysis with celebrity chart data."""
        # Create Taylor Swift chart data (from golden set example)
        taylor_swift_ephemeris = EphemerisData()
        taylor_swift_ephemeris.celestial_bodies = {
            "Sun": CelestialBodyPosition(lon=261.4, lat=0.0, speed=1.0),  # 21.4° Sagittarius
            "Venus": CelestialBodyPosition(lon=315.2, lat=0.0, speed=1.2),  # 15.2° Aquarius
            "Moon": CelestialBodyPosition(lon=180.0, lat=0.0, speed=13.0),  # Add for minimum
            "Mercury": CelestialBodyPosition(lon=270.0, lat=0.0, speed=1.5),  # Add for minimum
        }
        
        # Run analysis
        result = self.detector._analyze_vibrational_astrology(taylor_swift_ephemeris)
        
        # Should complete without errors
        assert result["total_circuits_found"] >= 0
        assert "implementation_status" in result
        assert result["implementation_status"] == "full_va_v1"
    
    def test_multiple_chart_batch_processing(self):
        """Test batch processing of multiple charts."""
        # Create multiple test charts
        test_charts = [
            {
                "name": "Chart1",
                "ephemeris": self.create_test_ephemeris(0)
            },
            {
                "name": "Chart2", 
                "ephemeris": self.create_test_ephemeris(120)
            },
            {
                "name": "Chart3",
                "ephemeris": self.create_test_ephemeris(240)
            }
        ]
        
        results = []
        
        # Process each chart
        for chart in test_charts:
            result = self.detector._analyze_vibrational_astrology(chart["ephemeris"])
            results.append({
                "name": chart["name"],
                "result": result
            })
        
        # Validate all results
        assert len(results) == 3
        
        for chart_result in results:
            result = chart_result["result"]
            assert "total_circuits_found" in result
            assert result["harmonics_analyzed"] == [1, 5, 7, 8, 9, 11, 13]
    
    def create_test_ephemeris(self, offset: float = 0.0) -> EphemerisData:
        """Create test ephemeris with specified offset."""
        ephemeris = EphemerisData()
        ephemeris.celestial_bodies = {
            "Sun": CelestialBodyPosition(lon=(0.0 + offset) % 360, lat=0.0, speed=1.0),
            "Moon": CelestialBodyPosition(lon=(120.0 + offset) % 360, lat=0.0, speed=13.0),
            "Mercury": CelestialBodyPosition(lon=(240.0 + offset) % 360, lat=0.0, speed=1.5),
            "Venus": CelestialBodyPosition(lon=(60.0 + offset) % 360, lat=0.0, speed=1.2),
        }
        return ephemeris


class TestVAServiceIntegration:
    """Integration tests for VA algorithm within service architecture."""
    
    def setup_method(self):
        """Set up service integration test fixtures."""
        self.detector = VACircuitDetector()
    
    def test_va_algorithm_service_interface(self):
        """Test VA algorithm as a service component."""
        # Test service initialization
        assert hasattr(self.detector, '_analyze_vibrational_astrology')
        assert hasattr(self.detector, 'get_algorithm_info')
        assert hasattr(self.detector, 'validate_input_data')
        
        # Test service metadata
        info = self.detector.get_algorithm_info()
        assert info["algorithm_name"] == "Vibrational Astrology Circuit Detector"
        assert info["integrity_status"] == "PROTECTED"
    
    def test_va_error_handling_integration(self):
        """Test error handling in service integration context."""
        # Test with invalid input
        with pytest.raises(ValueError):
            self.detector.validate_input_data(None)
        
        # Test with insufficient data
        minimal_ephemeris = EphemerisData()
        minimal_ephemeris.celestial_bodies = {
            "Sun": CelestialBodyPosition(lon=0.0, lat=0.0, speed=1.0)
        }
        
        with pytest.raises(ValueError):
            self.detector.validate_input_data(minimal_ephemeris)
    
    def test_va_performance_monitoring_integration(self):
        """Test performance monitoring integration."""
        ephemeris = self.create_standard_ephemeris()
        
        # Get performance metrics
        metrics = self.detector.get_performance_metrics()
        
        assert "target_performance" in metrics
        assert "typical_runtime" in metrics
        assert "scaling_limits" in metrics
        
        # Validate scaling limits
        limits = metrics["scaling_limits"]
        assert limits["max_planets"] == 15
        assert limits["max_harmonics"] == 13
    
    def test_va_debugging_integration(self):
        """Test debugging interface integration."""
        ephemeris = self.create_standard_ephemeris()
        
        # Get debugging information
        debug_info = self.detector.get_debugging_info(ephemeris, harmonic=5)
        
        assert "algorithm_version" in debug_info
        assert "input_validation" in debug_info
        assert "configuration_status" in debug_info
        assert "harmonic_analysis" in debug_info
        
        # Validate debugging data
        input_info = debug_info["input_validation"]
        assert input_info["data_present"] == True
        assert input_info["celestial_bodies_count"] > 0
    
    def create_standard_ephemeris(self) -> EphemerisData:
        """Create standard ephemeris for testing."""
        ephemeris = EphemerisData()
        ephemeris.celestial_bodies = {
            "Sun": CelestialBodyPosition(lon=0.0, lat=0.0, speed=1.0),
            "Moon": CelestialBodyPosition(lon=45.0, lat=0.0, speed=13.0),
            "Mercury": CelestialBodyPosition(lon=90.0, lat=0.0, speed=1.5),
            "Venus": CelestialBodyPosition(lon=135.0, lat=0.0, speed=1.2),
            "Mars": CelestialBodyPosition(lon=180.0, lat=0.0, speed=0.5),
        }
        return ephemeris


class TestVADataFlowIntegration:
    """Integration tests for VA algorithm data flow."""
    
    def setup_method(self):
        """Set up data flow test fixtures."""
        self.detector = VACircuitDetector()
    
    def test_ephemeris_to_va_analysis_flow(self):
        """Test complete data flow from ephemeris to VA analysis."""
        # Create input data
        ephemeris = EphemerisData()
        ephemeris.celestial_bodies = {
            "Sun": CelestialBodyPosition(lon=0.0, lat=0.0, speed=1.0),
            "Moon": CelestialBodyPosition(lon=120.0, lat=0.0, speed=13.0),
            "Mercury": CelestialBodyPosition(lon=240.0, lat=0.0, speed=1.5),
        }
        
        # Validate input
        assert self.detector.validate_input_data(ephemeris) == True
        
        # Process through VA algorithm
        result = self.detector._analyze_vibrational_astrology(ephemeris)
        
        # Validate output structure
        assert isinstance(result, dict)
        assert "total_circuits_found" in result
        assert "harmonics_analyzed" in result
        assert "summary" in result
        
        # Validate data types
        assert isinstance(result["total_circuits_found"], int)
        assert isinstance(result["harmonics_analyzed"], list)
        assert isinstance(result["summary"], str)
    
    def test_harmonic_analysis_data_flow(self):
        """Test data flow through harmonic analysis."""
        ephemeris = self.create_test_ephemeris()
        
        # Test each harmonic individually
        for harmonic in [1, 5, 7, 8, 9, 11, 13]:
            # Calculate harmonic positions
            positions = self.detector._calculate_harmonic_positions(ephemeris, harmonic)
            
            # Validate position data
            assert isinstance(positions, dict)
            assert len(positions) == len(ephemeris.celestial_bodies)
            
            for planet_name, position in positions.items():
                assert isinstance(position, CelestialBodyPosition)
                assert 0 <= position.lon <= 360
                assert position.speed > 0
            
            # Detect circuits in this harmonic
            circuits = self.detector._detect_va_circuits(positions, harmonic)
            
            # Validate circuit data
            assert isinstance(circuits, list)
            for circuit in circuits:
                assert "harmonic" in circuit
                assert "planets" in circuit
                assert "circuit_type" in circuit
                assert circuit["harmonic"] == harmonic
    
    def test_circuit_quality_analysis_flow(self):
        """Test data flow through circuit quality analysis."""
        ephemeris = self.create_circuit_ephemeris()
        
        # Run VA analysis to get circuits
        result = self.detector._analyze_vibrational_astrology(ephemeris)
        
        # Analyze quality of found circuits
        if result["total_circuits_found"] > 0:
            for harmonic_key, circuits in result["circuits_by_harmonic"].items():
                for circuit in circuits:
                    quality = self.detector.analyze_circuit_quality(circuit)
                    
                    # Validate quality analysis structure
                    assert "quality_score" in quality
                    assert "quality_level" in quality
                    assert "metrics" in quality
                    assert "interpretation" in quality
                    
                    # Validate quality data
                    assert isinstance(quality["quality_score"], (int, float))
                    assert quality["quality_level"] in ["EXCEPTIONAL", "STRONG", "MODERATE", "WEAK", "MINIMAL"]
                    assert isinstance(quality["interpretation"], str)
    
    def create_test_ephemeris(self) -> EphemerisData:
        """Create test ephemeris."""
        ephemeris = EphemerisData()
        ephemeris.celestial_bodies = {
            "Sun": CelestialBodyPosition(lon=0.0, lat=0.0, speed=1.0),
            "Moon": CelestialBodyPosition(lon=45.0, lat=0.0, speed=13.0),
            "Mercury": CelestialBodyPosition(lon=90.0, lat=0.0, speed=1.5),
            "Venus": CelestialBodyPosition(lon=135.0, lat=0.0, speed=1.2),
            "Mars": CelestialBodyPosition(lon=180.0, lat=0.0, speed=0.5),
        }
        return ephemeris
    
    def create_circuit_ephemeris(self) -> EphemerisData:
        """Create ephemeris designed to produce circuits."""
        ephemeris = EphemerisData()
        ephemeris.celestial_bodies = {
            "Sun": CelestialBodyPosition(lon=0.0, lat=0.0, speed=1.0),
            "Moon": CelestialBodyPosition(lon=120.0, lat=0.0, speed=13.0),  # Trine
            "Mercury": CelestialBodyPosition(lon=240.0, lat=0.0, speed=1.5),  # Trine to both
        }
        return ephemeris


class TestVASystemIntegration:
    """System-level integration tests for VA algorithm."""
    
    def setup_method(self):
        """Set up system integration test fixtures."""
        self.detector = VACircuitDetector()
    
    def test_va_algorithm_integrity_in_system(self):
        """Test that VA algorithm maintains integrity within system."""
        # Test algorithm initialization
        detector = VACircuitDetector()
        
        # Verify integrity validation works
        try:
            detector._validate_algorithm_integrity()
        except ValueError as e:
            pytest.fail(f"Algorithm integrity validation failed: {e}")
        
        # Test algorithm info
        info = detector.get_algorithm_info()
        assert info["integrity_status"] == "PROTECTED"
        assert info["line_count"] == 523
    
    def test_va_end_to_end_processing(self):
        """Test complete end-to-end VA processing."""
        # Create realistic chart data
        ephemeris = EphemerisData()
        ephemeris.celestial_bodies = {
            "Sun": CelestialBodyPosition(lon=15.5, lat=0.0, speed=1.0),
            "Moon": CelestialBodyPosition(lon=135.7, lat=0.0, speed=13.2),
            "Mercury": CelestialBodyPosition(lon=25.3, lat=0.0, speed=1.4),
            "Venus": CelestialBodyPosition(lon=345.8, lat=0.0, speed=1.1),
            "Mars": CelestialBodyPosition(lon=195.2, lat=0.0, speed=0.6),
            "Jupiter": CelestialBodyPosition(lon=75.9, lat=0.0, speed=0.08),
            "Saturn": CelestialBodyPosition(lon=285.4, lat=0.0, speed=0.03),
        }
        
        # Run complete analysis
        result = self.detector._analyze_vibrational_astrology(ephemeris)
        
        # Validate complete result structure
        expected_keys = [
            "harmonics_analyzed", "circuits_by_harmonic", "total_circuits_found",
            "active_harmonics", "harmonic_positions", "implementation_status", "summary"
        ]
        
        for key in expected_keys:
            assert key in result, f"Missing key in result: {key}"
        
        # Validate harmonic positions were calculated
        assert len(result["harmonic_positions"]) == 7  # 7 harmonics
        
        for harmonic_key, positions in result["harmonic_positions"].items():
            assert harmonic_key.startswith("H")
            assert len(positions) == 7  # 7 planets
        
        # Validate summary
        assert isinstance(result["summary"], str)
        assert len(result["summary"]) > 0
    
    def test_va_system_error_recovery(self):
        """Test VA algorithm error recovery in system context."""
        # Test recovery from invalid input
        try:
            self.detector.validate_input_data(None)
            pytest.fail("Should have raised ValueError")
        except ValueError:
            pass  # Expected
        
        # Test that detector still works after error
        valid_ephemeris = EphemerisData()
        valid_ephemeris.celestial_bodies = {
            "Sun": CelestialBodyPosition(lon=0.0, lat=0.0, speed=1.0),
            "Moon": CelestialBodyPosition(lon=120.0, lat=0.0, speed=13.0),
            "Mercury": CelestialBodyPosition(lon=240.0, lat=0.0, speed=1.5),
        }
        
        # Should work normally after error
        result = self.detector._analyze_vibrational_astrology(valid_ephemeris)
        assert "total_circuits_found" in result
