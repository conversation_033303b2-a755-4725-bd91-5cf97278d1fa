# VA Algorithm Performance Test Suite
"""
Performance validation tests for the crown jewel VA algorithm.

These tests ensure the VA algorithm meets the <2s performance requirement
and validate scalability under various load conditions.
"""

import pytest
import time
import psutil
import os
from typing import List, Dict

from services.astrology_engine.va_circuit_detector import (
    VACircuitDetector, 
    EphemerisData, 
    CelestialBodyPosition
)


class TestVAPerformanceBaseline:
    """Baseline performance tests for VA algorithm."""
    
    def setup_method(self):
        """Set up performance test fixtures."""
        self.detector = VACircuitDetector()
    
    def create_test_ephemeris(self, planet_count: int) -> EphemerisData:
        """Create test ephemeris with specified number of planets."""
        ephemeris = EphemerisData()
        ephemeris.celestial_bodies = {}
        
        planet_names = [
            "Sun", "Moon", "Mercury", "Venus", "Mars", "Jupiter", "Saturn",
            "Uranus", "Neptune", "Pluto", "Chiron", "North Node", "South Node",
            "Lilith", "Ceres"
        ]
        
        for i in range(min(planet_count, len(planet_names))):
            name = planet_names[i]
            # Distribute planets evenly around zodiac
            longitude = (i * 360.0 / planet_count) % 360.0
            ephemeris.celestial_bodies[name] = CelestialBodyPosition(
                lon=longitude, lat=0.0, speed=1.0
            )
        
        return ephemeris
    
    def test_minimal_configuration_performance(self):
        """Test performance with minimal 3-planet configuration."""
        ephemeris = self.create_test_ephemeris(3)
        
        start_time = time.time()
        result = self.detector._analyze_vibrational_astrology(ephemeris)
        execution_time = time.time() - start_time
        
        # Should be very fast with minimal planets
        assert execution_time < 0.1, f"Minimal config too slow: {execution_time:.3f}s"
        assert result["total_circuits_found"] >= 0
    
    def test_standard_configuration_performance(self):
        """Test performance with standard 10-planet configuration."""
        ephemeris = self.create_test_ephemeris(10)
        
        start_time = time.time()
        result = self.detector._analyze_vibrational_astrology(ephemeris)
        execution_time = time.time() - start_time
        
        # Should meet <2s requirement
        assert execution_time < 2.0, f"Standard config exceeds target: {execution_time:.3f}s"
        assert result["total_circuits_found"] >= 0
    
    def test_maximum_configuration_performance(self):
        """Test performance with maximum 15-planet configuration."""
        ephemeris = self.create_test_ephemeris(15)
        
        start_time = time.time()
        result = self.detector._analyze_vibrational_astrology(ephemeris)
        execution_time = time.time() - start_time
        
        # Should still meet <2s requirement at maximum scale
        assert execution_time < 2.0, f"Maximum config exceeds target: {execution_time:.3f}s"
        assert result["total_circuits_found"] >= 0
    
    def test_harmonic_calculation_performance(self):
        """Test performance of individual harmonic calculations."""
        ephemeris = self.create_test_ephemeris(10)
        
        # Test each base harmonic
        for harmonic in [1, 5, 7, 8, 9, 11, 13]:
            start_time = time.time()
            positions = self.detector._calculate_harmonic_positions(ephemeris, harmonic)
            execution_time = time.time() - start_time
            
            # Each harmonic calculation should be very fast
            assert execution_time < 0.01, f"Harmonic {harmonic} calculation too slow: {execution_time:.4f}s"
            assert len(positions) == 10
    
    def test_circuit_detection_performance(self):
        """Test performance of circuit detection for single harmonic."""
        ephemeris = self.create_test_ephemeris(10)
        harmonic_positions = self.detector._calculate_harmonic_positions(ephemeris, 1)
        
        start_time = time.time()
        circuits = self.detector._detect_va_circuits(harmonic_positions, 1)
        execution_time = time.time() - start_time
        
        # Circuit detection should be fast
        assert execution_time < 0.5, f"Circuit detection too slow: {execution_time:.3f}s"
        assert isinstance(circuits, list)


class TestVAMemoryUsage:
    """Memory usage validation tests."""
    
    def setup_method(self):
        """Set up memory test fixtures."""
        self.detector = VACircuitDetector()
        self.process = psutil.Process(os.getpid())
    
    def get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB."""
        return self.process.memory_info().rss / 1024 / 1024
    
    def test_memory_usage_baseline(self):
        """Test baseline memory usage for VA analysis."""
        ephemeris = self.create_test_ephemeris(10)
        
        memory_before = self.get_memory_usage_mb()
        result = self.detector._analyze_vibrational_astrology(ephemeris)
        memory_after = self.get_memory_usage_mb()
        
        memory_increase = memory_after - memory_before
        
        # Should use less than 50MB additional memory
        assert memory_increase < 50, f"Memory usage too high: {memory_increase:.1f}MB"
        assert result["total_circuits_found"] >= 0
    
    def test_memory_cleanup_after_analysis(self):
        """Test that memory is properly cleaned up after analysis."""
        ephemeris = self.create_test_ephemeris(10)
        
        memory_baseline = self.get_memory_usage_mb()
        
        # Run multiple analyses
        for _ in range(5):
            self.detector._analyze_vibrational_astrology(ephemeris)
        
        memory_final = self.get_memory_usage_mb()
        memory_increase = memory_final - memory_baseline
        
        # Memory should not accumulate significantly
        assert memory_increase < 100, f"Memory leak detected: {memory_increase:.1f}MB increase"
    
    def create_test_ephemeris(self, planet_count: int) -> EphemerisData:
        """Create test ephemeris with specified number of planets."""
        ephemeris = EphemerisData()
        ephemeris.celestial_bodies = {}
        
        planet_names = [
            "Sun", "Moon", "Mercury", "Venus", "Mars", "Jupiter", "Saturn",
            "Uranus", "Neptune", "Pluto", "Chiron", "North Node", "South Node",
            "Lilith", "Ceres"
        ]
        
        for i in range(min(planet_count, len(planet_names))):
            name = planet_names[i]
            longitude = (i * 360.0 / planet_count) % 360.0
            ephemeris.celestial_bodies[name] = CelestialBodyPosition(
                lon=longitude, lat=0.0, speed=1.0
            )
        
        return ephemeris


class TestVAScalabilityLimits:
    """Test algorithm behavior at scaling limits."""
    
    def setup_method(self):
        """Set up scalability test fixtures."""
        self.detector = VACircuitDetector()
    
    def test_planet_count_scaling(self):
        """Test performance scaling with increasing planet count."""
        execution_times = []
        
        for planet_count in [3, 5, 7, 10, 12, 15]:
            ephemeris = self.create_test_ephemeris(planet_count)
            
            start_time = time.time()
            result = self.detector._analyze_vibrational_astrology(ephemeris)
            execution_time = time.time() - start_time
            
            execution_times.append((planet_count, execution_time))
            
            # All configurations should meet performance target
            assert execution_time < 2.0, f"{planet_count} planets exceeds target: {execution_time:.3f}s"
        
        # Log scaling behavior for analysis
        print("\nPlanet Count Scaling:")
        for count, time_taken in execution_times:
            print(f"  {count:2d} planets: {time_taken:.3f}s")
    
    def test_harmonic_complexity_scaling(self):
        """Test performance with different harmonic combinations."""
        ephemeris = self.create_test_ephemeris(10)
        
        # Test individual harmonics
        harmonic_times = []
        for harmonic in [1, 5, 7, 8, 9, 11, 13]:
            positions = self.detector._calculate_harmonic_positions(ephemeris, harmonic)
            
            start_time = time.time()
            circuits = self.detector._detect_va_circuits(positions, harmonic)
            execution_time = time.time() - start_time
            
            harmonic_times.append((harmonic, execution_time))
            
            # Each harmonic should be fast
            assert execution_time < 0.5, f"Harmonic {harmonic} too slow: {execution_time:.3f}s"
        
        # Log harmonic performance
        print("\nHarmonic Performance:")
        for harmonic, time_taken in harmonic_times:
            print(f"  Harmonic {harmonic:2d}: {time_taken:.4f}s")
    
    def test_circuit_complexity_scaling(self):
        """Test performance with varying circuit complexity."""
        # Create configurations with different circuit potential
        test_configs = [
            ("minimal_circuits", self.create_minimal_circuit_ephemeris()),
            ("moderate_circuits", self.create_moderate_circuit_ephemeris()),
            ("complex_circuits", self.create_complex_circuit_ephemeris())
        ]
        
        for config_name, ephemeris in test_configs:
            start_time = time.time()
            result = self.detector._analyze_vibrational_astrology(ephemeris)
            execution_time = time.time() - start_time
            
            # All configurations should meet performance target
            assert execution_time < 2.0, f"{config_name} exceeds target: {execution_time:.3f}s"
            
            print(f"\n{config_name}: {execution_time:.3f}s, {result['total_circuits_found']} circuits")
    
    def create_test_ephemeris(self, planet_count: int) -> EphemerisData:
        """Create test ephemeris with specified number of planets."""
        ephemeris = EphemerisData()
        ephemeris.celestial_bodies = {}
        
        planet_names = [
            "Sun", "Moon", "Mercury", "Venus", "Mars", "Jupiter", "Saturn",
            "Uranus", "Neptune", "Pluto", "Chiron", "North Node", "South Node",
            "Lilith", "Ceres"
        ]
        
        for i in range(min(planet_count, len(planet_names))):
            name = planet_names[i]
            longitude = (i * 360.0 / planet_count) % 360.0
            ephemeris.celestial_bodies[name] = CelestialBodyPosition(
                lon=longitude, lat=0.0, speed=1.0
            )
        
        return ephemeris
    
    def create_minimal_circuit_ephemeris(self) -> EphemerisData:
        """Create ephemeris with minimal circuit potential."""
        ephemeris = EphemerisData()
        ephemeris.celestial_bodies = {
            "Sun": CelestialBodyPosition(lon=0.0, lat=0.0, speed=1.0),
            "Moon": CelestialBodyPosition(lon=90.0, lat=0.0, speed=13.0),  # Square
            "Mercury": CelestialBodyPosition(lon=180.0, lat=0.0, speed=1.5),  # Opposition to Sun
        }
        return ephemeris
    
    def create_moderate_circuit_ephemeris(self) -> EphemerisData:
        """Create ephemeris with moderate circuit potential."""
        ephemeris = EphemerisData()
        ephemeris.celestial_bodies = {
            "Sun": CelestialBodyPosition(lon=0.0, lat=0.0, speed=1.0),
            "Moon": CelestialBodyPosition(lon=120.0, lat=0.0, speed=13.0),  # Trine
            "Mercury": CelestialBodyPosition(lon=240.0, lat=0.0, speed=1.5),  # Trine to both
            "Venus": CelestialBodyPosition(lon=60.0, lat=0.0, speed=1.2),  # Sextiles
            "Mars": CelestialBodyPosition(lon=300.0, lat=0.0, speed=0.5),  # Sextiles
        }
        return ephemeris
    
    def create_complex_circuit_ephemeris(self) -> EphemerisData:
        """Create ephemeris with high circuit potential."""
        ephemeris = EphemerisData()
        ephemeris.celestial_bodies = {}
        
        # Create grand trine with additional planets
        base_positions = [0, 120, 240]  # Grand trine
        additional_positions = [60, 180, 300]  # Additional aspects
        
        planet_names = ["Sun", "Moon", "Mercury", "Venus", "Mars", "Jupiter"]
        all_positions = base_positions + additional_positions
        
        for i, name in enumerate(planet_names):
            ephemeris.celestial_bodies[name] = CelestialBodyPosition(
                lon=all_positions[i], lat=0.0, speed=1.0
            )
        
        return ephemeris


class TestVAPerformanceRegression:
    """Regression testing for VA algorithm performance."""
    
    def setup_method(self):
        """Set up regression test fixtures."""
        self.detector = VACircuitDetector()
        
        # Standard benchmark configuration
        self.benchmark_ephemeris = EphemerisData()
        self.benchmark_ephemeris.celestial_bodies = {
            "Sun": CelestialBodyPosition(lon=0.0, lat=0.0, speed=1.0),
            "Moon": CelestialBodyPosition(lon=45.0, lat=0.0, speed=13.0),
            "Mercury": CelestialBodyPosition(lon=90.0, lat=0.0, speed=1.5),
            "Venus": CelestialBodyPosition(lon=135.0, lat=0.0, speed=1.2),
            "Mars": CelestialBodyPosition(lon=180.0, lat=0.0, speed=0.5),
            "Jupiter": CelestialBodyPosition(lon=225.0, lat=0.0, speed=0.08),
            "Saturn": CelestialBodyPosition(lon=270.0, lat=0.0, speed=0.03),
            "Uranus": CelestialBodyPosition(lon=315.0, lat=0.0, speed=0.01),
            "Neptune": CelestialBodyPosition(lon=30.0, lat=0.0, speed=0.006),
            "Pluto": CelestialBodyPosition(lon=60.0, lat=0.0, speed=0.004)
        }
    
    def test_performance_baseline_establishment(self):
        """Establish performance baseline for regression detection."""
        execution_times = []
        
        # Run multiple iterations to establish baseline
        for _ in range(10):
            start_time = time.time()
            result = self.detector._analyze_vibrational_astrology(self.benchmark_ephemeris)
            execution_time = time.time() - start_time
            execution_times.append(execution_time)
        
        # Calculate statistics
        avg_time = sum(execution_times) / len(execution_times)
        max_time = max(execution_times)
        min_time = min(execution_times)
        
        # All runs should meet performance target
        assert max_time < 2.0, f"Performance regression detected: {max_time:.3f}s > 2.0s"
        
        # Log baseline metrics
        print(f"\nPerformance Baseline:")
        print(f"  Average: {avg_time:.3f}s")
        print(f"  Range: {min_time:.3f}s - {max_time:.3f}s")
        print(f"  Circuits found: {result['total_circuits_found']}")
    
    def test_consistent_results_validation(self):
        """Test that algorithm produces consistent results."""
        results = []
        
        # Run analysis multiple times
        for _ in range(5):
            result = self.detector._analyze_vibrational_astrology(self.benchmark_ephemeris)
            results.append(result)
        
        # Results should be identical
        first_result = results[0]
        for result in results[1:]:
            assert result["total_circuits_found"] == first_result["total_circuits_found"]
            assert result["active_harmonics"] == first_result["active_harmonics"]
            assert result["harmonics_analyzed"] == first_result["harmonics_analyzed"]
    
    def test_performance_under_load(self):
        """Test performance under concurrent load."""
        import threading
        import queue
        
        results_queue = queue.Queue()
        
        def run_analysis():
            start_time = time.time()
            result = self.detector._analyze_vibrational_astrology(self.benchmark_ephemeris)
            execution_time = time.time() - start_time
            results_queue.put((execution_time, result))
        
        # Run 5 concurrent analyses
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=run_analysis)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Collect results
        execution_times = []
        while not results_queue.empty():
            execution_time, result = results_queue.get()
            execution_times.append(execution_time)
            assert result["total_circuits_found"] >= 0
        
        # All concurrent runs should meet performance target
        max_concurrent_time = max(execution_times)
        assert max_concurrent_time < 2.0, f"Concurrent performance regression: {max_concurrent_time:.3f}s"
        
        print(f"\nConcurrent Performance:")
        print(f"  Max time: {max_concurrent_time:.3f}s")
        print(f"  Avg time: {sum(execution_times)/len(execution_times):.3f}s")


# Benchmark fixtures for pytest-benchmark
@pytest.fixture
def standard_ephemeris():
    """Standard ephemeris configuration for benchmarking."""
    ephemeris = EphemerisData()
    ephemeris.celestial_bodies = {
        "Sun": CelestialBodyPosition(lon=0.0, lat=0.0, speed=1.0),
        "Moon": CelestialBodyPosition(lon=45.0, lat=0.0, speed=13.0),
        "Mercury": CelestialBodyPosition(lon=90.0, lat=0.0, speed=1.5),
        "Venus": CelestialBodyPosition(lon=135.0, lat=0.0, speed=1.2),
        "Mars": CelestialBodyPosition(lon=180.0, lat=0.0, speed=0.5),
        "Jupiter": CelestialBodyPosition(lon=225.0, lat=0.0, speed=0.08),
        "Saturn": CelestialBodyPosition(lon=270.0, lat=0.0, speed=0.03),
        "Uranus": CelestialBodyPosition(lon=315.0, lat=0.0, speed=0.01),
        "Neptune": CelestialBodyPosition(lon=30.0, lat=0.0, speed=0.006),
        "Pluto": CelestialBodyPosition(lon=60.0, lat=0.0, speed=0.004)
    }
    return ephemeris


def test_benchmark_full_analysis(standard_ephemeris):
    """Benchmark complete VA analysis."""
    detector = VACircuitDetector()

    import time
    start_time = time.time()
    result = detector._analyze_vibrational_astrology(standard_ephemeris)
    execution_time = time.time() - start_time

    # Validate benchmark results
    assert result["total_circuits_found"] >= 0
    assert len(result["harmonics_analyzed"]) == 7
    assert execution_time < 2.0  # Performance target

    print(f"Full analysis benchmark: {execution_time:.3f}s")


def test_benchmark_harmonic_calculation(standard_ephemeris):
    """Benchmark harmonic position calculation."""
    detector = VACircuitDetector()

    import time
    start_time = time.time()
    result = detector._calculate_harmonic_positions(standard_ephemeris, 5)
    execution_time = time.time() - start_time

    # Validate benchmark results
    assert len(result) == 10  # 10 planets
    assert all(0 <= pos.lon <= 360 for pos in result.values())
    assert execution_time < 0.01  # Should be very fast

    print(f"Harmonic calculation benchmark: {execution_time:.4f}s")


def test_benchmark_circuit_detection(standard_ephemeris):
    """Benchmark circuit detection for single harmonic."""
    detector = VACircuitDetector()
    harmonic_positions = detector._calculate_harmonic_positions(standard_ephemeris, 1)

    import time
    start_time = time.time()
    result = detector._detect_va_circuits(harmonic_positions, 1)
    execution_time = time.time() - start_time

    # Validate benchmark results
    assert isinstance(result, list)
    assert all("planets" in circuit for circuit in result)
    assert execution_time < 0.5  # Should be fast

    print(f"Circuit detection benchmark: {execution_time:.4f}s")
