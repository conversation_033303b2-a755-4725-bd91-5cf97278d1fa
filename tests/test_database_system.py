"""
Database System Tests

Comprehensive tests for the database architecture including:
- Connection management
- Data models validation
- CRUD operations
- Cache functionality
- Migration system
"""

import pytest
import asyncio
import os
from datetime import datetime, timedelta
from uuid import uuid4

# Set test environment before importing database modules
os.environ["ENVIRONMENT"] = "testing"

from services.database import (
    DatabaseConfig, RedisConfig, ArticleData, CelebrityData, ProcessingStatus,
    DataSource, ConfidenceLevel, ConnectionManager, ArticleDBService,
    CelebrityDBService, CacheManager, get_cache_manager, run_all_migrations
)
from services.database.config import get_database_config, get_redis_config


class TestDatabaseModels:
    """Test Pydantic data models."""
    
    def test_article_data_creation(self):
        """Test ArticleData model creation and validation."""
        article = ArticleData(
            title="Test Article",
            content="This is test content for the article.",
            url="https://example.com/test-article",
            published_at=datetime.utcnow(),
            source="test_source"
        )
        
        assert article.title == "Test Article"
        assert article.processing_status == ProcessingStatus.PENDING
        assert isinstance(article.entities, list)
        assert len(article.entities) == 0
        assert article.id is not None
    
    def test_celebrity_data_creation(self):
        """Test CelebrityData model creation and validation."""
        celebrity = CelebrityData(
            name="Test Celebrity",
            birth_date=datetime(1990, 1, 1),
            birth_time="12:00",
            birth_location="New York, NY",
            birth_latitude=40.7128,
            birth_longitude=-74.0060,
            confidence_score=0.95
        )
        
        assert celebrity.name == "Test Celebrity"
        assert celebrity.confidence_score == 0.95
        assert celebrity.verified is False
        assert celebrity.enhanced is False
        assert isinstance(celebrity.data_sources, list)
    
    def test_article_data_validation(self):
        """Test ArticleData validation rules."""
        # Test invalid URL
        with pytest.raises(ValueError):
            ArticleData(
                title="Test",
                content="Content",
                url="invalid-url",
                published_at=datetime.utcnow(),
                source="test"
            )
        
        # Test empty title
        with pytest.raises(ValueError):
            ArticleData(
                title="",
                content="Content",
                url="https://example.com",
                published_at=datetime.utcnow(),
                source="test"
            )
    
    def test_celebrity_data_validation(self):
        """Test CelebrityData validation rules."""
        # Test future birth date
        with pytest.raises(ValueError):
            CelebrityData(
                name="Test Celebrity",
                birth_date=datetime.utcnow() + timedelta(days=1),
                confidence_score=0.5
            )
        
        # Test invalid confidence score
        with pytest.raises(ValueError):
            CelebrityData(
                name="Test Celebrity",
                confidence_score=1.5  # > 1.0
            )


class TestDatabaseConfiguration:
    """Test database configuration management."""
    
    def test_testing_database_config(self):
        """Test testing environment database configuration."""
        config = get_database_config("testing")
        
        assert isinstance(config, DatabaseConfig)
        assert config.pool_size >= 1
        assert config.echo is False  # No SQL logging in tests
        assert config.retry_attempts >= 1
    
    def test_testing_redis_config(self):
        """Test testing environment Redis configuration."""
        config = get_redis_config("testing")
        
        assert isinstance(config, RedisConfig)
        assert config.url.startswith("redis://")
        assert "test" in config.key_prefix
        assert config.default_ttl > 0
    
    def test_development_database_config(self):
        """Test development environment database configuration."""
        # Clear any cached config and explicitly request development
        from services.database.config import DatabaseConfigManager
        config_manager = DatabaseConfigManager("development")
        config = config_manager.get_database_config()

        assert isinstance(config, DatabaseConfig)
        assert config.echo is True  # SQL logging enabled in development
        assert config.pool_size > 1


@pytest.mark.asyncio
class TestConnectionManager:
    """Test database connection management."""
    
    async def test_connection_manager_initialization(self):
        """Test connection manager initialization."""
        db_config = get_database_config("testing")
        redis_config = get_redis_config("testing")
        
        connection_manager = ConnectionManager(db_config, redis_config)
        
        # Test initialization
        success = await connection_manager.initialize()
        
        # Note: This might fail if Redis/PostgreSQL not available in test environment
        # In that case, we test the configuration is correct
        assert isinstance(success, bool)
        
        if success:
            # Test health status
            health = await connection_manager.get_health_status()
            assert "overall_status" in health
            assert "database" in health
            assert "redis" in health
            
            # Clean up
            await connection_manager.close()
    
    async def test_database_session_context_manager(self):
        """Test database session context manager."""
        db_config = get_database_config("testing")
        redis_config = get_redis_config("testing")
        
        connection_manager = ConnectionManager(db_config, redis_config)
        
        try:
            success = await connection_manager.initialize()
            
            if success:
                # Test session context manager
                async with connection_manager.database.get_session() as session:
                    assert session is not None
                    # Session should be automatically closed after context
        
        except Exception as e:
            # Expected if database not available in test environment
            pytest.skip(f"Database not available for testing: {e}")
        
        finally:
            await connection_manager.close()


@pytest.mark.asyncio
class TestCacheManager:
    """Test Redis cache management."""
    
    async def test_cache_manager_basic_operations(self):
        """Test basic cache operations."""
        cache_manager = get_cache_manager()
        
        # Test data
        test_key = "test_key"
        test_value = {"message": "Hello, World!", "timestamp": datetime.utcnow().isoformat()}
        
        try:
            # Test set operation
            success = await cache_manager.set(test_key, test_value, ttl=60)
            
            if success:
                # Test get operation
                retrieved_value = await cache_manager.get(test_key)
                assert retrieved_value == test_value
                
                # Test exists operation
                exists = await cache_manager.exists(test_key)
                assert exists is True
                
                # Test delete operation
                deleted = await cache_manager.delete(test_key)
                assert deleted is True
                
                # Verify deletion
                retrieved_after_delete = await cache_manager.get(test_key)
                assert retrieved_after_delete is None
        
        except Exception as e:
            # Expected if Redis not available in test environment
            pytest.skip(f"Redis not available for testing: {e}")
    
    async def test_cache_manager_get_or_set(self):
        """Test cache get_or_set functionality."""
        cache_manager = get_cache_manager()
        
        test_key = "test_get_or_set"
        
        def factory_function():
            return {"generated": True, "timestamp": datetime.utcnow().isoformat()}
        
        try:
            # First call should generate value
            value1 = await cache_manager.get_or_set(test_key, factory_function, ttl=60)
            assert value1["generated"] is True
            
            # Second call should return cached value
            value2 = await cache_manager.get_or_set(test_key, factory_function, ttl=60)
            assert value2 == value1  # Should be identical (cached)
            
            # Clean up
            await cache_manager.delete(test_key)
        
        except Exception as e:
            pytest.skip(f"Redis not available for testing: {e}")
    
    def test_cache_manager_stats(self):
        """Test cache statistics tracking."""
        cache_manager = get_cache_manager()
        
        # Reset stats
        cache_manager.reset_stats()
        
        # Check initial stats
        assert cache_manager.stats.hits == 0
        assert cache_manager.stats.misses == 0
        assert cache_manager.stats.sets == 0
        assert cache_manager.stats.hit_rate == 0.0


@pytest.mark.asyncio
class TestDatabaseServices:
    """Test database service operations."""
    
    async def test_article_service_creation(self):
        """Test article service instantiation."""
        article_service = ArticleDBService()
        assert article_service is not None
        assert article_service.cache_ttl > 0
        assert article_service.cache_prefix == "article:"
    
    async def test_celebrity_service_creation(self):
        """Test celebrity service instantiation."""
        celebrity_service = CelebrityDBService()
        assert celebrity_service is not None
        assert celebrity_service.cache_ttl > 0
        assert celebrity_service.cache_prefix == "celebrity:"
        assert celebrity_service.name_similarity_threshold > 0
    
    def test_celebrity_name_similarity(self):
        """Test celebrity name similarity calculation."""
        celebrity_service = CelebrityDBService()
        
        # Test data
        from difflib import SequenceMatcher
        
        name1 = "Robert Downey Jr."
        name2 = "Robert Downey Junior"
        
        similarity = SequenceMatcher(None, name1.lower(), name2.lower()).ratio()
        
        # Should be high similarity
        assert similarity > 0.7
        
        # Test completely different names
        name3 = "Tom Hanks"
        similarity2 = SequenceMatcher(None, name1.lower(), name3.lower()).ratio()
        
        # Should be low similarity
        assert similarity2 < 0.3


class TestDataModelSerialization:
    """Test data model serialization and deserialization."""
    
    def test_article_json_serialization(self):
        """Test ArticleData JSON serialization."""
        article = ArticleData(
            title="Test Article",
            content="Test content",
            url="https://example.com/test",
            published_at=datetime.utcnow(),
            source="test_source",
            entities=["entity1", "entity2"],
            celebrity_mentions=["celebrity1"]
        )
        
        # Test JSON serialization
        json_data = article.json()
        assert isinstance(json_data, str)
        
        # Test deserialization
        article_copy = ArticleData.parse_raw(json_data)
        assert article_copy.title == article.title
        assert article_copy.entities == article.entities
        assert article_copy.celebrity_mentions == article.celebrity_mentions
    
    def test_celebrity_json_serialization(self):
        """Test CelebrityData JSON serialization."""
        celebrity = CelebrityData(
            name="Test Celebrity",
            birth_date=datetime(1990, 1, 1),
            confidence_score=0.95,
            data_sources=[DataSource.MANUAL, DataSource.WIKIPEDIA],
            aliases=["Alias 1", "Alias 2"]
        )
        
        # Test JSON serialization
        json_data = celebrity.json()
        assert isinstance(json_data, str)
        
        # Test deserialization
        celebrity_copy = CelebrityData.parse_raw(json_data)
        assert celebrity_copy.name == celebrity.name
        assert celebrity_copy.confidence_score == celebrity.confidence_score
        assert len(celebrity_copy.data_sources) == 2
        assert celebrity_copy.aliases == celebrity.aliases


class TestDatabaseIntegration:
    """Integration tests for the complete database system."""
    
    def test_database_package_imports(self):
        """Test that all database package components can be imported."""
        # Test model imports
        from services.database import (
            ArticleData, CelebrityData, ProcessingStatus, DataSource
        )
        
        # Test service imports
        from services.database import (
            ArticleDBService, CelebrityDBService, CacheManager
        )
        
        # Test connection imports
        from services.database import (
            ConnectionManager, get_connection_manager
        )
        
        # Test migration imports
        from services.database import (
            run_all_migrations, get_all_migrations
        )
        
        # All imports successful
        assert True
    
    def test_configuration_validation(self):
        """Test configuration validation."""
        from services.database.config import validate_all_configurations
        
        results = validate_all_configurations()
        
        # Should have results for all environments
        assert "development" in results
        assert "testing" in results
        assert "production" in results
        
        # Each environment should have database and redis validation
        for env_result in results.values():
            assert "database" in env_result
            assert "redis" in env_result
            assert "environment" in env_result


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
