"""
Golden Set Testing for Isolate Feature

Tests the Isolate entity extraction against canonical examples from
starpower_isolate_golden_set.md to ensure 90% accuracy target.
"""

import pytest
import json
import asyncio
from typing import Dict, List, Any
from datetime import datetime

from services.llm_processor.llm_orchestrator import LLMOrchestrator
from services.llm_processor.entity_resolver import EntityResolver
from services.llm_processor.isolate_models import IsolateExtractionRequest
from services.llm_processor.models import LLMConfig, LLMProvider
from services.database.article_db_service import ArticleDBService
from services.database.celebrity_db_service import CelebrityDBService
from services.database.generic_entity_db_service import GenericEntityDBService
from services.database.entity_graph_db_service import EntityGraphDBService
from services.database.models import ArticleData, ProcessingStatus


class GoldenSetTestCase:
    """Represents a single golden set test case."""
    
    def __init__(self, name: str, article_text: str, expected_json: Dict[str, Any]):
        self.name = name
        self.article_text = article_text
        self.expected_json = expected_json
        self.actual_result = None
        self.accuracy_score = 0.0
        self.errors = []


class IsolateGoldenSetTester:
    """
    Test suite for validating Isolate extraction against golden set examples.
    """
    
    def __init__(self):
        """Initialize the golden set tester."""
        # Initialize LLM orchestrator
        llm_config = LLMConfig(
            primary_provider=LLMProvider.PERPLEXITY,
            fallback_providers=[LLMProvider.OPENAI, LLMProvider.OLLAMA],
            enable_fallback=True,
            max_retries=2,
            timeout_seconds=180
        )
        self.llm_orchestrator = LLMOrchestrator(llm_config)
        
        # Initialize database services
        self.article_db = ArticleDBService()
        self.celebrity_db = CelebrityDBService()
        self.generic_entity_db = GenericEntityDBService()
        self.entity_graph_db = EntityGraphDBService()
        
        # Initialize entity resolver
        self.entity_resolver = EntityResolver(
            celebrity_db_service=self.celebrity_db,
            article_db_service=self.article_db,
            generic_entity_db_service=self.generic_entity_db,
            entity_graph_db_service=self.entity_graph_db
        )
        
        # Test cases
        self.test_cases = []
        self.results = []
        
    def add_test_case(self, name: str, article_text: str, expected_json: Dict[str, Any]):
        """Add a test case to the suite."""
        test_case = GoldenSetTestCase(name, article_text, expected_json)
        self.test_cases.append(test_case)
        
    async def run_single_test(self, test_case: GoldenSetTestCase) -> float:
        """
        Run a single test case and return accuracy score.
        
        Args:
            test_case: Test case to run
            
        Returns:
            Accuracy score (0.0 to 1.0)
        """
        try:
            # Create extraction request
            request = IsolateExtractionRequest(
                article_title=f"Test Article: {test_case.name}",
                article_text=test_case.article_text,
                article_url="https://test.example.com/article",
                published_date=datetime.now().isoformat()
            )
            
            # Extract entities
            response = await self.llm_orchestrator.extract_isolate_entities(request)
            test_case.actual_result = response
            
            # Calculate accuracy
            accuracy = self._calculate_accuracy(test_case.expected_json, response.isolate_data.dict())
            test_case.accuracy_score = accuracy
            
            return accuracy
            
        except Exception as e:
            test_case.errors.append(f"Extraction failed: {str(e)}")
            test_case.accuracy_score = 0.0
            return 0.0
    
    def _calculate_accuracy(self, expected: Dict[str, Any], actual: Dict[str, Any]) -> float:
        """
        Calculate accuracy score between expected and actual results.
        
        Args:
            expected: Expected JSON structure
            actual: Actual extraction result
            
        Returns:
            Accuracy score (0.0 to 1.0)
        """
        total_score = 0.0
        max_score = 0.0
        
        # Score entities
        if 'entities' in expected:
            entity_score, entity_max = self._score_entities(
                expected.get('entities', []), 
                actual.get('entities', [])
            )
            total_score += entity_score
            max_score += entity_max
        
        # Score relationships
        if 'relationships' in expected:
            rel_score, rel_max = self._score_relationships(
                expected.get('relationships', []), 
                actual.get('relationships', [])
            )
            total_score += rel_score
            max_score += rel_max
        
        # Score actions
        if 'actions' in expected:
            action_score, action_max = self._score_actions(
                expected.get('actions', []), 
                actual.get('actions', [])
            )
            total_score += action_score
            max_score += action_max
        
        # Score endeavors
        if 'endeavors' in expected:
            endeavor_score, endeavor_max = self._score_endeavors(
                expected.get('endeavors', []), 
                actual.get('endeavors', [])
            )
            total_score += endeavor_score
            max_score += endeavor_max
        
        return total_score / max_score if max_score > 0 else 0.0
    
    def _score_entities(self, expected: List[Dict], actual: List[Dict]) -> tuple:
        """Score entity extraction accuracy."""
        if not expected:
            return 0.0, 0.0
            
        total_score = 0.0
        max_score = len(expected) * 4  # name, type, primary, inception_date
        
        for exp_entity in expected:
            best_match_score = 0.0
            
            for act_entity in actual:
                score = 0.0
                
                # Name match (most important)
                if exp_entity.get('name', '').lower() == act_entity.get('name', '').lower():
                    score += 2.0
                elif exp_entity.get('name', '').lower() in act_entity.get('name', '').lower():
                    score += 1.0
                
                # Type match
                if exp_entity.get('type') == act_entity.get('type'):
                    score += 1.0
                
                # Primary status match
                if exp_entity.get('primary') == act_entity.get('primary'):
                    score += 0.5
                
                # Inception date match (if provided)
                if exp_entity.get('inception_date') and act_entity.get('inception_date'):
                    if exp_entity['inception_date'] == act_entity['inception_date']:
                        score += 0.5
                
                best_match_score = max(best_match_score, score)
            
            total_score += best_match_score
        
        return total_score, max_score
    
    def _score_relationships(self, expected: List[Dict], actual: List[Dict]) -> tuple:
        """Score relationship extraction accuracy."""
        if not expected:
            return 0.0, 0.0
            
        total_score = 0.0
        max_score = len(expected) * 3  # entity1, entity2, relation_type
        
        for exp_rel in expected:
            best_match_score = 0.0
            
            for act_rel in actual:
                score = 0.0
                
                # Entity names match
                if (exp_rel.get('entity1', '').lower() == act_rel.get('entity1_name', '').lower() and
                    exp_rel.get('entity2', '').lower() == act_rel.get('entity2_name', '').lower()):
                    score += 2.0
                elif (exp_rel.get('entity1', '').lower() == act_rel.get('entity2_name', '').lower() and
                      exp_rel.get('entity2', '').lower() == act_rel.get('entity1_name', '').lower()):
                    score += 2.0  # Bidirectional match
                
                # Relationship type match
                if exp_rel.get('relation_type') == act_rel.get('relationship_type'):
                    score += 1.0
                
                best_match_score = max(best_match_score, score)
            
            total_score += best_match_score
        
        return total_score, max_score
    
    def _score_actions(self, expected: List[Dict], actual: List[Dict]) -> tuple:
        """Score action extraction accuracy."""
        if not expected:
            return 0.0, 0.0
            
        total_score = 0.0
        max_score = len(expected) * 3  # entity1, entity2, action_type
        
        for exp_action in expected:
            best_match_score = 0.0
            
            for act_action in actual:
                score = 0.0
                
                # Actor match
                if exp_action.get('entity1', '').lower() == act_action.get('actor_name', '').lower():
                    score += 1.0
                
                # Target match (if applicable)
                if exp_action.get('entity2') and act_action.get('target_name'):
                    if exp_action['entity2'].lower() == act_action['target_name'].lower():
                        score += 1.0
                
                # Action type match
                if exp_action.get('action_type') == act_action.get('action_type'):
                    score += 1.0
                
                best_match_score = max(best_match_score, score)
            
            total_score += best_match_score
        
        return total_score, max_score
    
    def _score_endeavors(self, expected: List[Dict], actual: List[Dict]) -> tuple:
        """Score endeavor extraction accuracy."""
        if not expected:
            return 0.0, 0.0
            
        total_score = 0.0
        max_score = len(expected) * 2  # entity, endeavor_type
        
        for exp_endeavor in expected:
            best_match_score = 0.0
            
            for act_endeavor in actual:
                score = 0.0
                
                # Entity name match
                if exp_endeavor.get('entity', '').lower() == act_endeavor.get('entity_name', '').lower():
                    score += 1.0
                
                # Endeavor type match
                if exp_endeavor.get('endeavor_type') == act_endeavor.get('endeavor_type'):
                    score += 1.0
                
                best_match_score = max(best_match_score, score)
            
            total_score += best_match_score
        
        return total_score, max_score
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """
        Run all test cases and return comprehensive results.
        
        Returns:
            Test results summary
        """
        print(f"Running {len(self.test_cases)} golden set test cases...")
        
        total_accuracy = 0.0
        passed_tests = 0
        
        for i, test_case in enumerate(self.test_cases):
            print(f"Running test {i+1}/{len(self.test_cases)}: {test_case.name}")
            
            accuracy = await self.run_single_test(test_case)
            total_accuracy += accuracy
            
            if accuracy >= 0.8:  # 80% threshold for individual test pass
                passed_tests += 1
                print(f"  ✓ PASSED (accuracy: {accuracy:.2%})")
            else:
                print(f"  ✗ FAILED (accuracy: {accuracy:.2%})")
                if test_case.errors:
                    for error in test_case.errors:
                        print(f"    Error: {error}")
        
        overall_accuracy = total_accuracy / len(self.test_cases) if self.test_cases else 0.0
        
        results = {
            'total_tests': len(self.test_cases),
            'passed_tests': passed_tests,
            'failed_tests': len(self.test_cases) - passed_tests,
            'overall_accuracy': overall_accuracy,
            'target_accuracy': 0.9,
            'meets_target': overall_accuracy >= 0.9,
            'individual_results': [
                {
                    'name': tc.name,
                    'accuracy': tc.accuracy_score,
                    'passed': tc.accuracy_score >= 0.8,
                    'errors': tc.errors
                }
                for tc in self.test_cases
            ]
        }
        
        return results


# Golden Set Test Cases
GOLDEN_SET_CASES = [
    {
        'name': 'Buddy Holly Plane Crash (1959)',
        'article_text': 'On February 3, 1959, rock and roll musicians Buddy Holly, Ritchie Valens, and J.P. "The Big Bopper" Richardson died in a plane crash near Clear Lake, Iowa, shortly after takeoff from Mason City airport. The three performers had been touring as part of the Winter Dance Party tour and chartered the small aircraft to reach their next venue in Moorhead, Minnesota. The tragedy occurred around 1:00 AM during a snowstorm, killing all three musicians and pilot Roger Peterson instantly.',
        'expected_json': {
            'entities': [
                {'name': 'Buddy Holly', 'type': 'person', 'primary': True, 'inception_date': '1936-09-07'},
                {'name': 'Ritchie Valens', 'type': 'person', 'primary': True, 'inception_date': '1941-05-13'},
                {'name': 'The Big Bopper', 'type': 'person', 'primary': True, 'inception_date': '1930-10-24'},
                {'name': 'Roger Peterson', 'type': 'person', 'primary': True}
            ],
            'actions': [
                {'entity1': 'Buddy Holly', 'entity2': 'Ritchie Valens', 'action_type': 'died_with'},
                {'entity1': 'Buddy Holly', 'entity2': 'The Big Bopper', 'action_type': 'died_with'},
                {'entity1': 'Buddy Holly', 'entity2': 'Roger Peterson', 'action_type': 'died_with'}
            ]
        }
    },
    {
        'name': 'Kanye West VMAs Interruption (2009)',
        'article_text': 'During the 2009 MTV Video Music Awards at Radio City Music Hall, Kanye West interrupted Taylor Swift\'s acceptance speech for Best Female Video. Swift, who won for "You Belong With Me," was giving her speech when West took the microphone and declared that Beyoncé had "one of the best videos of all time" with "Single Ladies." The incident occurred at 9:51 PM on September 13, 2009, shocking the audience and Swift, who appeared visibly upset.',
        'expected_json': {
            'entities': [
                {'name': 'Kanye West', 'type': 'person', 'primary': True, 'inception_date': '1977-06-08'},
                {'name': 'Taylor Swift', 'type': 'person', 'primary': True, 'inception_date': '1989-12-13'},
                {'name': 'Beyoncé', 'type': 'person', 'primary': True, 'inception_date': '1981-09-04'},
                {'name': 'You Belong With Me', 'type': 'endeavor', 'primary': True},
                {'name': 'Single Ladies', 'type': 'endeavor', 'primary': True}
            ],
            'actions': [
                {'entity1': 'Kanye West', 'entity2': 'Taylor Swift', 'action_type': 'interrupted'}
            ]
        }
    }
]


@pytest.mark.asyncio
async def test_golden_set_validation():
    """Test Isolate extraction against golden set examples."""
    tester = IsolateGoldenSetTester()
    
    # Add test cases
    for case in GOLDEN_SET_CASES:
        tester.add_test_case(case['name'], case['article_text'], case['expected_json'])
    
    # Run tests
    results = await tester.run_all_tests()
    
    # Print results
    print(f"\n=== Golden Set Test Results ===")
    print(f"Total Tests: {results['total_tests']}")
    print(f"Passed: {results['passed_tests']}")
    print(f"Failed: {results['failed_tests']}")
    print(f"Overall Accuracy: {results['overall_accuracy']:.2%}")
    print(f"Target Accuracy: {results['target_accuracy']:.2%}")
    print(f"Meets Target: {'✓' if results['meets_target'] else '✗'}")
    
    # Assert target accuracy
    assert results['overall_accuracy'] >= 0.9, f"Overall accuracy {results['overall_accuracy']:.2%} below 90% target"


if __name__ == "__main__":
    # Run the test directly
    async def main():
        await test_golden_set_validation()
    
    asyncio.run(main())
