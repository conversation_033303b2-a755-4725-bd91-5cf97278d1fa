"""
Tests for LLM Processor Service
"""

import asyncio
import json
import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from services.llm_processor import (
    LLMProvider, LLMTaskType, LLMConfig, LLMRequest, LLMResponse,
    CelebrityMatch, CelebrityIdentificationRequest, CelebrityIdentificationResponse,
    OllamaClient, OpenAIClient, ResponseProcessor, ResponseQualityAssessor,
    LLMOrchestrator, LLMError, LLMTimeoutError
)


class TestLLMModels:
    """Test LLM data models"""
    
    def test_llm_config_defaults(self):
        """Test LLM config default values"""
        config = LLMConfig()
        
        assert config.primary_provider == LLMProvider.OLLAMA
        assert config.primary_model == "dolphin-mixtral"
        assert config.fallback_provider == LLMProvider.OPENAI
        assert config.fallback_model == "gpt-4"
        assert config.quality_threshold == 0.7
        assert config.max_retries == 3
        assert config.timeout_seconds == 30
    
    def test_celebrity_identification_request(self):
        """Test celebrity identification request model"""
        request = CelebrityIdentificationRequest(
            article_title="Test Article",
            article_content="This is a test article about Taylor Swift.",
            known_entities=["<PERSON> Swift"]
        )
        
        assert request.task_type == LLMTaskType.CELEBRITY_IDENTIFICATION
        assert request.text == "This is a test article about Taylor Swift."
        assert request.article_title == "Test Article"
        assert "Taylor Swift" in request.known_entities
    
    def test_celebrity_match_model(self):
        """Test celebrity match model"""
        match = CelebrityMatch(
            name="Taylor Swift",
            confidence=0.95,
            context="mentioned in the article",
            aliases=["T-Swift"],
            profession="musician"
        )
        
        assert match.name == "Taylor Swift"
        assert match.confidence == 0.95
        assert match.context == "mentioned in the article"
        assert "T-Swift" in match.aliases
        assert match.profession == "musician"


class TestResponseProcessor:
    """Test response processor"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.processor = ResponseProcessor()
    
    def test_parse_json_response(self):
        """Test JSON response parsing"""
        json_response = {
            "celebrities": [
                {
                    "name": "Taylor Swift",
                    "confidence": 0.95,
                    "context": "mentioned in article"
                }
            ],
            "summary": "Article about Taylor Swift"
        }
        
        response = LLMResponse(
            raw_response=json.dumps(json_response),
            provider=LLMProvider.OLLAMA,
            model="test-model",
            task_type=LLMTaskType.CELEBRITY_IDENTIFICATION,
            confidence_score=0.8,
            quality_score=0.8,
            processing_time_ms=100.0
        )
        
        processed = self.processor.process_response(response)
        
        assert processed.parsed_content is not None
        assert "celebrities" in processed.parsed_content
        assert processed.extracted_data["extraction_method"] == "_parse_json"
    
    def test_parse_thinking_tags(self):
        """Test thinking tags parsing"""
        response_text = """<think>
        I need to identify celebrities in this article.
        </think>
        
        The article mentions Taylor Swift and her recent concert.
        """
        
        response = LLMResponse(
            raw_response=response_text,
            provider=LLMProvider.OLLAMA,
            model="test-model",
            task_type=LLMTaskType.CONTENT_ANALYSIS,
            confidence_score=0.8,
            quality_score=0.8,
            processing_time_ms=100.0
        )
        
        processed = self.processor.process_response(response)
        
        assert processed.parsed_content is not None
        assert "thinking" in processed.parsed_content
        assert len(processed.parsed_content["thinking"]) > 0
    
    def test_celebrity_response_creation(self):
        """Test celebrity identification response creation"""
        json_response = {
            "celebrities": [
                {
                    "name": "Taylor Swift",
                    "confidence": 0.95,
                    "context": "mentioned in article",
                    "profession": "musician"
                }
            ],
            "summary": "Article about Taylor Swift",
            "key_topics": ["music", "concert"]
        }
        
        response = LLMResponse(
            raw_response=json.dumps(json_response),
            provider=LLMProvider.OLLAMA,
            model="test-model",
            task_type=LLMTaskType.CELEBRITY_IDENTIFICATION,
            confidence_score=0.8,
            quality_score=0.8,
            processing_time_ms=100.0
        )
        
        processed = self.processor.process_response(response)
        
        assert isinstance(processed, CelebrityIdentificationResponse)
        assert len(processed.celebrities) == 1
        assert processed.celebrities[0].name == "Taylor Swift"
        assert processed.celebrities[0].confidence == 0.95
        assert processed.total_matches == 1
        assert processed.high_confidence_matches == 1


class TestQualityAssessor:
    """Test quality assessor"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.assessor = ResponseQualityAssessor()
    
    def test_assess_celebrity_identification_quality(self):
        """Test celebrity identification quality assessment"""
        celebrities = [
            CelebrityMatch(
                name="Taylor Swift",
                confidence=0.95,
                context="mentioned in the article about her concert",
                profession="musician"
            )
        ]
        
        response = CelebrityIdentificationResponse(
            raw_response='{"celebrities": [{"name": "Taylor Swift", "confidence": 0.95}]}',
            provider=LLMProvider.OLLAMA,
            model="test-model",
            task_type=LLMTaskType.CELEBRITY_IDENTIFICATION,
            confidence_score=0.8,
            quality_score=0.8,
            processing_time_ms=100.0,
            celebrities=celebrities,
            article_title="Test Article"
        )
        
        quality_score = self.assessor.assess_quality(response)
        
        assert 0.0 <= quality_score <= 1.0
        assert quality_score > 0.5  # Should be reasonably high quality
    
    def test_assess_format_quality(self):
        """Test format quality assessment"""
        # JSON response should score high
        json_response = LLMResponse(
            raw_response='{"test": "value"}',
            provider=LLMProvider.OLLAMA,
            model="test-model",
            task_type=LLMTaskType.CONTENT_ANALYSIS,
            confidence_score=0.8,
            quality_score=0.8,
            processing_time_ms=100.0
        )

        format_score = self.assessor._assess_format_quality(json_response)
        assert format_score >= 0.2  # JSON should score reasonably

        # Plain text should score lower
        plain_response = LLMResponse(
            raw_response="This is just plain text.",
            provider=LLMProvider.OLLAMA,
            model="test-model",
            task_type=LLMTaskType.CONTENT_ANALYSIS,
            confidence_score=0.8,
            quality_score=0.8,
            processing_time_ms=100.0
        )

        plain_score = self.assessor._assess_format_quality(plain_response)
        assert plain_score >= 0.0  # Should have some score


class TestOllamaClient:
    """Test Ollama client"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.client = OllamaClient(
            base_url="http://localhost:11434",
            model="test-model",
            timeout=10
        )
    
    @patch('services.llm_processor.ollama_client.AsyncClient')
    @pytest.mark.asyncio
    async def test_process_request_success(self, mock_client_class):
        """Test successful request processing"""
        # Mock the async client
        mock_client = AsyncMock()
        mock_client_class.return_value = mock_client

        # Mock responses
        mock_client.list.return_value = {
            "models": [{"name": "test-model"}]
        }
        mock_client.generate.return_value = {
            "response": "Test response from Ollama",
            "eval_count": 50
        }

        # Create client with mocked async client
        self.client.client = mock_client

        request = LLMRequest(
            text="Test input",
            task_type=LLMTaskType.CONTENT_ANALYSIS
        )

        response = await self.client.process_request(request)

        assert isinstance(response, LLMResponse)
        assert response.raw_response == "Test response from Ollama"
        assert response.provider == LLMProvider.OLLAMA
        assert response.model == "test-model"
        assert response.tokens_used == 50

    @patch('services.llm_processor.ollama_client.AsyncClient')
    @pytest.mark.asyncio
    async def test_health_check(self, mock_client_class):
        """Test health check"""
        mock_client = AsyncMock()
        mock_client_class.return_value = mock_client

        mock_client.list.return_value = {
            "models": [{"name": "test-model"}, {"name": "other-model"}]
        }

        self.client.client = mock_client

        health = await self.client.health_check()

        assert health["status"] == "healthy"
        assert health["model"] == "test-model"
        assert "test-model" in health["available_models"]


class TestOpenAIClient:
    """Test OpenAI client"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.client = OpenAIClient(
            api_key="test-key",
            model="gpt-4",
            timeout=10
        )
    
    @patch('services.llm_processor.openai_client.AsyncOpenAI')
    @pytest.mark.asyncio
    async def test_process_request_success(self, mock_openai_class):
        """Test successful request processing"""
        # Mock the OpenAI client
        mock_client = AsyncMock()
        mock_openai_class.return_value = mock_client

        # Mock response
        mock_response = MagicMock()
        mock_response.choices = [MagicMock()]
        mock_response.choices[0].message.content = "Test response from OpenAI"
        mock_response.usage.total_tokens = 75

        mock_client.chat.completions.create.return_value = mock_response

        # Set the mocked client
        self.client.client = mock_client

        request = LLMRequest(
            text="Test input",
            task_type=LLMTaskType.CONTENT_ANALYSIS
        )

        response = await self.client.process_request(request)

        assert isinstance(response, LLMResponse)
        assert response.raw_response == "Test response from OpenAI"
        assert response.provider == LLMProvider.OPENAI
        assert response.model == "gpt-4"
        assert response.tokens_used == 75

    @patch.dict('os.environ', {}, clear=True)
    def test_no_api_key(self):
        """Test client without API key"""
        client = OpenAIClient(api_key=None)
        # Client is created but will fail on requests

        request = LLMRequest(
            text="Test input",
            task_type=LLMTaskType.CONTENT_ANALYSIS
        )

        with pytest.raises(LLMError, match="not initialized"):
            asyncio.run(client.process_request(request))


class TestLLMOrchestrator:
    """Test LLM orchestrator"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.config = LLMConfig(
            quality_threshold=0.5,  # Lower threshold for testing
            timeout_seconds=10
        )
        self.orchestrator = LLMOrchestrator(self.config)
    
    @patch('services.llm_processor.llm_orchestrator.OllamaClient')
    @patch('services.llm_processor.llm_orchestrator.OpenAIClient')
    @pytest.mark.asyncio
    async def test_process_request_primary_success(self, mock_openai, mock_ollama):
        """Test successful processing with primary provider"""
        # Mock Ollama client (primary)
        mock_ollama_instance = AsyncMock()
        mock_ollama.return_value = mock_ollama_instance

        mock_response = LLMResponse(
            raw_response='{"test": "success", "analysis": "This is a detailed analysis with proper structure and content that meets quality standards."}',
            provider=LLMProvider.OLLAMA,
            model="test-model",
            task_type=LLMTaskType.CONTENT_ANALYSIS,
            confidence_score=0.8,
            quality_score=0.8,
            processing_time_ms=100.0
        )

        mock_ollama_instance.process_request.return_value = mock_response

        # Reinitialize orchestrator with mocked clients
        self.orchestrator.clients[LLMProvider.OLLAMA] = mock_ollama_instance

        request = LLMRequest(
            text="Test input",
            task_type=LLMTaskType.CONTENT_ANALYSIS
        )

        response = await self.orchestrator.process_request(request)

        assert isinstance(response, LLMResponse)
        assert response.provider == LLMProvider.OLLAMA
        assert self.orchestrator.stats["primary_successes"] == 1
        assert self.orchestrator.stats["fallback_uses"] == 0

    @pytest.mark.asyncio
    async def test_identify_celebrities(self):
        """Test celebrity identification method"""
        # Mock the process_request method
        mock_response = CelebrityIdentificationResponse(
            raw_response='{"celebrities": []}',
            provider=LLMProvider.OLLAMA,
            model="test-model",
            task_type=LLMTaskType.CELEBRITY_IDENTIFICATION,
            confidence_score=0.8,
            quality_score=0.8,
            processing_time_ms=100.0,
            celebrities=[],
            article_title="Test Article"
        )

        self.orchestrator.process_request = AsyncMock(return_value=mock_response)

        response = await self.orchestrator.identify_celebrities(
            article_title="Test Article",
            article_content="This is a test article."
        )

        assert isinstance(response, CelebrityIdentificationResponse)
        assert response.article_title == "Test Article"

    @pytest.mark.asyncio
    async def test_health_check(self):
        """Test orchestrator health check"""
        # Mock client health checks
        mock_ollama = AsyncMock()
        mock_ollama.health_check.return_value = {"status": "healthy"}

        self.orchestrator.clients[LLMProvider.OLLAMA] = mock_ollama

        health = await self.orchestrator.health_check()

        assert health["orchestrator"]["status"] == "healthy"
        assert "clients" in health
        assert LLMProvider.OLLAMA.value in health["clients"]
    
    def test_get_statistics(self):
        """Test statistics retrieval"""
        # Simulate some processing
        self.orchestrator.stats["requests_processed"] = 10
        self.orchestrator.stats["primary_successes"] = 8
        self.orchestrator.stats["fallback_uses"] = 2
        self.orchestrator.stats["total_processing_time"] = 5.0
        
        stats = self.orchestrator.get_statistics()
        
        assert stats["requests_processed"] == 10
        assert stats["primary_success_rate"] == 0.8
        assert stats["fallback_rate"] == 0.2
        assert stats["average_processing_time"] == 0.5
