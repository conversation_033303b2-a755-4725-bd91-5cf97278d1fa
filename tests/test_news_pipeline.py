"""
Test suite for news pipeline services.

Comprehensive tests for GNews client, content extractor, and news ingestion service
with mocking for external dependencies and error scenarios.
"""

import asyncio
import json
import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from typing import List

from services.news_pipeline import (
    GNewsClient,
    GNewsConfig,
    GNewsArticle,
    GNewsResponse,
    ContentExtractor,
    ExtractedContent,
    NewsIngestionService,
    NewsIngestionConfig,
    NewsIngestionStats,
    GeographicLocation,
    GeographicExtractor,
    ReadabilityExtractor,
    NewspaperExtractor
)


class TestGNewsClient:
    """Test cases for GNews API client."""
    
    @pytest.fixture
    def gnews_config(self):
        """Create test GNews configuration."""
        return GNewsConfig(
            api_key="test_api_key",
            max_articles=10,
            timeout=30
        )
    
    @pytest.fixture
    def mock_gnews_response(self):
        """Create mock GNews API response."""
        return {
            "totalArticles": 2,
            "articles": [
                {
                    "title": "Test Article 1",
                    "description": "Test description 1",
                    "content": "Test content 1",
                    "url": "https://example.com/article1",
                    "image": "https://example.com/image1.jpg",
                    "publishedAt": "2024-01-15T10:00:00Z",
                    "source": {"name": "Test Source 1", "url": "https://example.com"}
                },
                {
                    "title": "Test Article 2", 
                    "description": "Test description 2",
                    "content": "Test content 2",
                    "url": "https://example.com/article2",
                    "image": None,
                    "publishedAt": "2024-01-15T11:00:00Z",
                    "source": {"name": "Test Source 2", "url": "https://example2.com"}
                }
            ]
        }
    
    @pytest.mark.asyncio
    async def test_search_articles_success(self, gnews_config, mock_gnews_response):
        """Test successful article search."""
        with patch('aiohttp.ClientSession.get') as mock_get:
            # Mock successful response
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = mock_gnews_response
            mock_get.return_value.__aenter__.return_value = mock_response
            
            async with GNewsClient(gnews_config) as client:
                articles = await client.search_articles("test query")
            
            assert len(articles) == 2
            assert articles[0].title == "Test Article 1"
            assert articles[1].title == "Test Article 2"
            assert articles[0].url == "https://example.com/article1"
    
    @pytest.mark.asyncio
    async def test_search_articles_rate_limited(self, gnews_config):
        """Test handling of rate limit responses."""
        with patch('aiohttp.ClientSession.get') as mock_get:
            # Mock rate limited response
            mock_response = AsyncMock()
            mock_response.status = 429
            mock_get.return_value.__aenter__.return_value = mock_response
            
            async with GNewsClient(gnews_config) as client:
                with pytest.raises(Exception, match="All retry attempts failed"):
                    await client.search_articles("test query")
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_functionality(self, gnews_config):
        """Test circuit breaker opens after failures."""
        client = GNewsClient(gnews_config)
        
        # Simulate multiple failures to trigger circuit breaker
        for _ in range(6):  # More than failure_threshold (5)
            client.circuit_breaker.record_failure()
        
        assert not client.circuit_breaker.can_execute()
        assert client.circuit_breaker.state == "OPEN"
    
    @pytest.mark.asyncio
    async def test_rate_limiter(self, gnews_config):
        """Test rate limiter functionality."""
        from services.news_pipeline.gnews_client import RateLimiter
        import time

        # Create a rate limiter with very short period for testing
        rate_limiter = RateLimiter(calls=2, period=1)

        # Fill up the rate limiter
        now = time.time()
        rate_limiter.call_times = [now - 0.5, now - 0.3]  # Two recent calls

        # Check that we're at the limit
        assert len(rate_limiter.call_times) == 2

        # Mock sleep to avoid actual waiting
        with patch('asyncio.sleep') as mock_sleep:
            await rate_limiter.acquire()
            # Should have called sleep since we're at the limit
            mock_sleep.assert_called_once()


class TestContentExtractor:
    """Test cases for content extractor."""
    
    @pytest.fixture
    def sample_html(self):
        """Create sample HTML for testing."""
        return """
        <html>
        <head>
            <title>Test Article - News Site</title>
            <meta property="article:published_time" content="2024-01-15T10:00:00Z">
        </head>
        <body>
            <article>
                <h1>Test Article Title</h1>
                <div class="author">John Doe</div>
                <div class="entry-content">
                    <p>This is the first paragraph of the article content with sufficient length for testing.</p>
                    <p>This is the second paragraph with more detailed information about the topic being discussed.</p>
                    <p>This is the third paragraph concluding the article with additional context and information.</p>
                    <p>This is a fourth paragraph to ensure we have enough content for the extraction to succeed.</p>
                </div>
            </article>
        </body>
        </html>
        """
    
    @pytest.mark.asyncio
    async def test_extract_content_success(self, sample_html):
        """Test successful content extraction."""
        with patch('aiohttp.ClientSession.get') as mock_get:
            # Mock successful HTML fetch
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.text.return_value = sample_html
            mock_get.return_value.__aenter__.return_value = mock_response
            
            async with ContentExtractor() as extractor:
                result = await extractor.extract_content("https://example.com/article")
            
            assert result is not None
            # Title could be extracted differently by different extractors
            assert "Test Article" in result.title
            assert "first paragraph" in result.content
            # Author extraction may vary by extractor
            assert result.extraction_method in ["BeautifulSoup", "Newspaper", "Readability"]
            assert result.confidence_score > 0.3
    
    @pytest.mark.asyncio
    async def test_extract_content_http_error(self):
        """Test handling of HTTP errors."""
        with patch('aiohttp.ClientSession.get') as mock_get:
            # Mock HTTP error
            mock_response = AsyncMock()
            mock_response.status = 404
            mock_get.return_value.__aenter__.return_value = mock_response
            
            async with ContentExtractor() as extractor:
                result = await extractor.extract_content("https://example.com/notfound")
            
            assert result is None
    
    @pytest.mark.asyncio
    async def test_extract_content_malformed_html(self):
        """Test handling of malformed HTML."""
        malformed_html = "<html><head><title>Test</title></head><body><p>No article content</body></html>"
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.text.return_value = malformed_html
            mock_get.return_value.__aenter__.return_value = mock_response
            
            async with ContentExtractor() as extractor:
                result = await extractor.extract_content("https://example.com/malformed")
            
            # Should return None for poor quality content
            assert result is None
    
    def test_content_deduplication(self):
        """Test content deduplication functionality."""
        extractor = ContentExtractor()
        
        content1 = "This is some test content for deduplication testing."
        content2 = "This is some test content for deduplication testing."  # Same content
        content3 = "This is different content for testing."
        
        hash1 = extractor.extractors[0]._calculate_content_hash(content1)
        hash2 = extractor.extractors[0]._calculate_content_hash(content2)
        hash3 = extractor.extractors[0]._calculate_content_hash(content3)
        
        assert hash1 == hash2  # Same content should have same hash
        assert hash1 != hash3  # Different content should have different hash
        
        existing_hashes = {hash1}
        assert extractor.is_duplicate_content(hash2, existing_hashes)
        assert not extractor.is_duplicate_content(hash3, existing_hashes)


class TestNewsIngestionService:
    """Test cases for news ingestion service."""
    
    @pytest.fixture
    def ingestion_config(self):
        """Create test ingestion configuration."""
        return NewsIngestionConfig(
            gnews_api_key="test_api_key",
            max_articles_per_batch=5,
            content_extraction_timeout=10,
            batch_processing_delay=0.1,
            enable_content_extraction=True,
            enable_deduplication=True
        )
    
    @pytest.fixture
    def mock_gnews_articles(self):
        """Create mock GNews articles."""
        from datetime import datetime, timezone, timedelta

        # Use recent dates to avoid age filtering
        now = datetime.now(timezone.utc)
        recent_date1 = now.strftime("%Y-%m-%dT%H:%M:%SZ")
        recent_date2 = (now - timedelta(hours=1)).strftime("%Y-%m-%dT%H:%M:%SZ")

        return [
            GNewsArticle(
                title="Test Article 1",
                description="Test description 1",
                content="Test content 1",
                url="https://example.com/article1",
                publishedAt=recent_date1,
                source={"name": "Test Source 1"}
            ),
            GNewsArticle(
                title="Test Article 2",
                description="Test description 2",
                content="Test content 2",
                url="https://example.com/article2",
                publishedAt=recent_date2,
                source={"name": "Test Source 2"}
            )
        ]
    
    @pytest.mark.asyncio
    async def test_fetch_and_process_articles_success(self, ingestion_config, mock_gnews_articles):
        """Test successful article fetching and processing."""
        with patch.object(GNewsClient, 'search_articles', return_value=mock_gnews_articles), \
             patch.object(ContentExtractor, 'extract_content') as mock_extract, \
             patch('services.news_pipeline.news_ingestion_service.ArticleDBService') as mock_db:

            # Mock content extraction to return different content for each call
            mock_extract.side_effect = [
                ExtractedContent(
                    title="Extracted Title 1",
                    content="Extracted content with sufficient length for testing purposes article 1.",
                    extraction_method="BeautifulSoup",
                    confidence_score=0.8,
                    content_hash="test_hash_123",
                    word_count=11
                ),
                ExtractedContent(
                    title="Extracted Title 2",
                    content="Extracted content with sufficient length for testing purposes article 2.",
                    extraction_method="BeautifulSoup",
                    confidence_score=0.8,
                    content_hash="test_hash_456",
                    word_count=11
                )
            ]

            # Mock database service
            mock_db_instance = AsyncMock()
            mock_db.return_value = mock_db_instance

            service = NewsIngestionService(ingestion_config)
            service.article_db = mock_db_instance

            stats = await service.fetch_and_process_articles("test query")

            assert stats.articles_fetched == 2
            assert stats.articles_processed == 2
            assert stats.processing_time_seconds > 0
    
    @pytest.mark.asyncio
    async def test_duplicate_article_handling(self, ingestion_config, mock_gnews_articles):
        """Test duplicate article detection and handling."""
        with patch.object(GNewsClient, 'search_articles', return_value=mock_gnews_articles), \
             patch.object(ContentExtractor, 'extract_content') as mock_extract, \
             patch('services.news_pipeline.news_ingestion_service.ArticleDBService') as mock_db:
            
            # Mock content extraction to return same hash (duplicate)
            mock_extract.return_value = ExtractedContent(
                title="Duplicate Article",
                content="This is duplicate content for testing.",
                extraction_method="BeautifulSoup",
                confidence_score=0.8,
                content_hash="duplicate_hash",
                word_count=8
            )
            
            mock_db_instance = AsyncMock()
            mock_db.return_value = mock_db_instance
            
            service = NewsIngestionService(ingestion_config)
            service.article_db = mock_db_instance
            
            # Pre-populate processed hashes to simulate duplicates
            service._processed_hashes.add("duplicate_hash")
            
            stats = await service.fetch_and_process_articles("test query")
            
            assert stats.articles_fetched == 2
            assert stats.duplicate_articles > 0
            assert stats.articles_processed < stats.articles_fetched
    
    @pytest.mark.asyncio
    async def test_content_extraction_failure_handling(self, ingestion_config, mock_gnews_articles):
        """Test handling of content extraction failures."""
        with patch.object(GNewsClient, 'search_articles', return_value=mock_gnews_articles), \
             patch.object(ContentExtractor, 'extract_content', side_effect=Exception("Extraction failed")), \
             patch('services.news_pipeline.news_ingestion_service.ArticleDBService') as mock_db:

            mock_db_instance = AsyncMock()
            mock_db.return_value = mock_db_instance

            service = NewsIngestionService(ingestion_config)
            service.article_db = mock_db_instance

            stats = await service.fetch_and_process_articles("test query")

            assert stats.articles_fetched == 2
            # When content extraction fails, articles are skipped (not counted as extraction failures)
            # because the exception is caught in _process_single_article and returns None
            assert stats.articles_skipped == 2
            assert stats.articles_processed == 0
    
    def test_health_status(self, ingestion_config):
        """Test service health status reporting."""
        service = NewsIngestionService(ingestion_config)
        
        health = service.get_health_status()
        
        assert health["service"] == "news_ingestion"
        assert health["status"] == "healthy"
        assert "gnews_circuit_breaker" in health
        assert "gnews_rate_limiter" in health


@pytest.mark.asyncio
async def test_integration_news_pipeline():
    """Integration test for complete news pipeline."""
    config = NewsIngestionConfig(
        gnews_api_key="test_key",
        max_articles_per_batch=2,
        enable_content_extraction=False,  # Disable to avoid external calls
        enable_deduplication=True
    )
    
    # Mock all external dependencies
    with patch.object(GNewsClient, 'search_articles') as mock_search, \
         patch('services.news_pipeline.news_ingestion_service.ArticleDBService') as mock_db:
        
        # Mock GNews response
        from datetime import datetime, timezone
        recent_date = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")

        mock_search.return_value = [
            GNewsArticle(
                title="Integration Test Article",
                description="Test description for integration testing",
                content="Test content",
                url="https://example.com/integration",
                publishedAt=recent_date,
                source={"name": "Integration Source"}
            )
        ]
        
        # Mock database
        mock_db_instance = AsyncMock()
        mock_db.return_value = mock_db_instance
        
        service = NewsIngestionService(config)
        service.article_db = mock_db_instance
        
        stats = await service.fetch_and_process_articles("integration test")
        
        assert stats.articles_fetched == 1
        assert stats.articles_processed == 1
        assert stats.processing_time_seconds > 0


class TestEnhancedContentExtraction:
    """Test cases for enhanced content extraction features."""

    @pytest.mark.asyncio
    async def test_geographic_location_extraction(self):
        """Test geographic location extraction from content."""
        geo_extractor = GeographicExtractor()

        # Test content with various location patterns
        test_content = """
        The earthquake struck near San Francisco, California, causing widespread damage.
        Reports are coming in from New York and London about the economic impact.
        The conference will be held in Tokyo next month.
        """

        locations = await geo_extractor.extract_locations(test_content)

        # Should extract some locations (exact number depends on geocoding success)
        assert isinstance(locations, list)
        for location in locations:
            assert isinstance(location, GeographicLocation)
            assert location.name
            assert 0.0 <= location.confidence <= 1.0

    @pytest.mark.asyncio
    async def test_content_extractor_with_multiple_strategies(self):
        """Test content extractor with multiple extraction strategies."""
        extractor = ContentExtractor(enable_geographic_extraction=False)  # Disable geo for faster testing

        # Test that extractor has multiple strategies
        stats = extractor.get_extractor_stats()
        assert stats["total_extractors"] >= 1  # At least BeautifulSoup should be available
        assert "BeautifulSoup" in stats["available_extractors"]

        # Test with sample HTML
        sample_html = """
        <html>
        <head><title>Test Article</title></head>
        <body>
            <article>
                <h1>Breaking News: Important Event</h1>
                <div class="entry-content">
                    <p>This is a comprehensive news article with substantial content for testing purposes.</p>
                    <p>The article contains multiple paragraphs to ensure proper content extraction.</p>
                    <p>Geographic locations like New York and California are mentioned throughout.</p>
                    <p>This ensures we have enough content for quality assessment and extraction confidence.</p>
                </div>
            </article>
        </body>
        </html>
        """

        with patch.object(extractor, '_fetch_html', return_value=sample_html):
            result = await extractor.extract_content("https://example.com/test")

            assert result is not None
            assert result.title == "Breaking News: Important Event"
            assert len(result.content) > 100
            assert result.word_count > 0
            assert result.confidence_score > 0.0
            assert result.content_hash

    @pytest.mark.asyncio
    async def test_batch_content_extraction(self):
        """Test batch content extraction with concurrency control."""
        extractor = ContentExtractor(enable_geographic_extraction=False)

        urls = [
            "https://example.com/article1",
            "https://example.com/article2",
            "https://example.com/article3"
        ]

        # Mock the individual extraction method
        mock_results = [
            ExtractedContent(
                title=f"Article {i}",
                content=f"Content for article {i} with sufficient length for testing purposes.",
                extraction_method="Mock",
                confidence_score=0.8,
                content_hash=f"hash_{i}",
                word_count=10
            ) for i in range(1, 4)
        ]

        with patch.object(extractor, 'extract_content', side_effect=mock_results):
            results = await extractor.extract_content_batch(urls, max_concurrent=2)

            assert len(results) == 3
            assert all(result is not None for result in results)
            assert results[0].title == "Article 1"
            assert results[1].title == "Article 2"
            assert results[2].title == "Article 3"

    @pytest.mark.asyncio
    async def test_content_extraction_with_geographic_enhancement(self):
        """Test content extraction with geographic location enhancement."""
        extractor = ContentExtractor(enable_geographic_extraction=True)

        sample_html = """
        <html>
        <head><title>Travel News</title></head>
        <body>
            <article>
                <h1>Tourism Boom in Paris</h1>
                <div class="entry-content">
                    <p>Tourism in Paris, France has reached record levels this year.</p>
                    <p>Visitors from New York and London are particularly numerous.</p>
                    <p>The city's attractions continue to draw millions of international tourists.</p>
                </div>
            </article>
        </body>
        </html>
        """

        # Mock geographic extraction to avoid actual API calls
        mock_locations = [
            GeographicLocation(name="Paris", country="France", confidence=0.9),
            GeographicLocation(name="New York", country="USA", confidence=0.8)
        ]

        with patch.object(extractor, '_fetch_html', return_value=sample_html), \
             patch.object(extractor.geo_extractor, 'extract_locations', return_value=mock_locations):

            result = await extractor.extract_content("https://example.com/travel")

            assert result is not None
            assert result.title == "Tourism Boom in Paris"
            assert len(result.locations) == 2
            assert result.locations[0].name == "Paris"
            assert result.locations[1].name == "New York"

    def test_extractor_stats(self):
        """Test extractor statistics reporting."""
        extractor = ContentExtractor()
        stats = extractor.get_extractor_stats()

        assert "available_extractors" in stats
        assert "total_extractors" in stats
        assert "geographic_extraction_enabled" in stats
        assert "cache_size" in stats
        assert "dependencies" in stats

        assert isinstance(stats["available_extractors"], list)
        assert stats["total_extractors"] >= 1
        assert isinstance(stats["geographic_extraction_enabled"], bool)
        assert stats["cache_size"] >= 0

        # Check dependency status
        deps = stats["dependencies"]
        assert "readability" in deps
        assert "newspaper" in deps
        assert "geopy" in deps
