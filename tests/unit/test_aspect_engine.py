"""
Test suite for the Aspect Engine

This test suite validates the aspect detection functionality for multiple
astrological traditions including Placidus and Vibrational systems.
"""

import pytest
import json
from pathlib import Path
from typing import Dict, List

from services.astrology_engine.aspect_engine import (
    AspectEngine, 
    AspectDefinition, 
    DetectedAspect
)


class TestAspectEngineBasics:
    """Test basic aspect engine functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.engine = AspectEngine()
        
        # Test chart data
        self.test_chart = {
            "planet_positions": {
                "sun": 0.0,      # 0° Aries
                "moon": 120.0,   # 0° Leo (trine to Sun)
                "mars": 90.0,    # 0° Cancer (square to Sun)
                "venus": 60.0,   # 0° Gemini (sextile to Sun)
                "mercury": 1.0   # 1° Aries (conjunction to Sun)
            }
        }
    
    def test_engine_initialization(self):
        """Test that the aspect engine initializes correctly."""
        assert self.engine is not None
        assert len(self.engine.aspect_definitions) > 0
        assert 'conjunction' in self.engine.aspect_definitions
        assert 'trine' in self.engine.aspect_definitions
        assert 'square' in self.engine.aspect_definitions
    
    def test_default_aspect_definitions(self):
        """Test default Placidus aspect definitions."""
        aspects = self.engine.get_aspect_definitions()
        
        # Check major aspects
        assert aspects['conjunction'].angle == 0
        assert aspects['opposition'].angle == 180
        assert aspects['trine'].angle == 120
        assert aspects['square'].angle == 90
        assert aspects['sextile'].angle == 60
        
        # Check orbs
        assert aspects['conjunction'].orb == 8
        assert aspects['sextile'].orb == 6
        assert aspects['semisquare'].orb == 2
    
    def test_calculate_angular_separation(self):
        """Test angular separation calculation."""
        # Test basic separation
        assert self.engine._calculate_angular_separation(0, 90) == 90
        assert self.engine._calculate_angular_separation(0, 180) == 180
        assert self.engine._calculate_angular_separation(0, 270) == 90  # Shortest path
        
        # Test wrap-around
        assert self.engine._calculate_angular_separation(350, 10) == 20
        assert self.engine._calculate_angular_separation(10, 350) == 20
    
    def test_is_aspect_method(self):
        """Test the is_aspect method."""
        # Test exact aspects
        assert self.engine.is_aspect(0, 'conjunction') == True
        assert self.engine.is_aspect(120, 'trine') == True
        assert self.engine.is_aspect(90, 'square') == True
        
        # Test within orb
        assert self.engine.is_aspect(5, 'conjunction') == True  # Within 8° orb
        assert self.engine.is_aspect(125, 'trine') == True     # Within 8° orb
        
        # Test outside orb
        assert self.engine.is_aspect(15, 'conjunction') == False  # Outside 8° orb
        assert self.engine.is_aspect(140, 'trine') == False      # Outside 8° orb
        
        # Test custom orb
        assert self.engine.is_aspect(15, 'conjunction', orb_tolerance=20) == True


class TestAspectDetection:
    """Test aspect detection functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.engine = AspectEngine()
    
    def test_basic_aspect_detection(self):
        """Test basic aspect detection between planets."""
        chart_data = {
            "planet_positions": {
                "sun": 0.0,
                "moon": 120.0,  # Trine to Sun
                "mars": 90.0    # Square to Sun
            }
        }
        
        aspects = self.engine.calculate_aspects(chart_data)

        # Should find 3 aspects: Sun-Moon trine, Sun-Mars square, Moon-Mars semisextile
        assert len(aspects) == 3

        # Check specific aspects
        aspect_types = [aspect.aspect_name for aspect in aspects]
        assert 'trine' in aspect_types
        assert 'square' in aspect_types
        assert 'semisextile' in aspect_types
    
    def test_conjunction_detection(self):
        """Test conjunction detection."""
        chart_data = {
            "planet_positions": {
                "sun": 0.0,
                "mercury": 1.0  # 1° conjunction
            }
        }
        
        aspects = self.engine.calculate_aspects(chart_data)
        
        assert len(aspects) == 1
        assert aspects[0].aspect_name == 'conjunction'
        assert aspects[0].exact_angle == 1.0
        assert aspects[0].orb_difference == 1.0
        assert aspects[0].strength > 0.8  # Very tight orb
    
    def test_no_aspects_found(self):
        """Test when no aspects are within orb."""
        chart_data = {
            "planet_positions": {
                "sun": 0.0,
                "moon": 45.0  # No major aspect at 45°
            }
        }
        
        aspects = self.engine.calculate_aspects(chart_data)
        
        # Should find semisquare if using default config
        # Or no aspects if semisquare orb is too tight
        assert isinstance(aspects, list)
    
    def test_multiple_planets_aspects(self):
        """Test aspect detection with multiple planets."""
        chart_data = {
            "planet_positions": {
                "sun": 0.0,
                "moon": 120.0,   # Trine to Sun
                "venus": 240.0,  # Trine to Moon, Trine to Sun (Grand Trine)
                "mars": 90.0     # Square to Sun
            }
        }
        
        aspects = self.engine.calculate_aspects(chart_data)
        
        # Should find multiple aspects
        assert len(aspects) >= 3
        
        # Check for grand trine aspects
        trine_aspects = [a for a in aspects if a.aspect_name == 'trine']
        assert len(trine_aspects) == 3  # Grand trine has 3 trine aspects


class TestConfigurationSystems:
    """Test different astrological tradition configurations."""
    
    def test_custom_configuration(self):
        """Test loading custom aspect configuration."""
        custom_config = {
            "default_orb": 5.0,
            "aspects": [
                {"name": "conjunction", "angle": 0, "orb": 10, "nature": "neutral"},
                {"name": "opposition", "angle": 180, "orb": 10, "nature": "challenging"},
                {"name": "trine", "angle": 120, "orb": 8, "nature": "harmonious"}
            ]
        }
        
        engine = AspectEngine()
        engine.configure_aspects(custom_config)
        
        aspects = engine.get_aspect_definitions()
        assert len(aspects) == 3
        assert aspects['conjunction'].orb == 10
        assert aspects['trine'].orb == 8
    
    def test_chart_data_override(self):
        """Test aspect definition override from chart data."""
        chart_data = {
            "planet_positions": {
                "sun": 0.0,
                "moon": 120.0
            },
            "aspect_definitions": {
                "conjunction": 0,
                "trine": 120,
                "square": 90
            },
            "orb": 5
        }
        
        engine = AspectEngine()
        aspects = engine.calculate_aspects(chart_data)
        
        # Should use the overridden configuration
        assert len(aspects) == 1  # Only trine should be found
        assert aspects[0].aspect_name == 'trine'


class TestGoldenSetValidation:
    """Test against golden set test cases."""
    
    def test_placidus_golden_set(self):
        """Test Placidus tradition against golden set."""
        # Example golden set test case
        golden_chart = {
            "planet_positions": {
                "sun": 134.0,
                "moon": 220.5,
                "mars": 135.0
            },
            "expected_aspects": [
                {"pair": ["sun", "mars"], "type": "conjunction", "orb": 1.0},
                {"pair": ["moon", "mars"], "type": "square", "orb": 4.5}
            ]
        }
        
        engine = AspectEngine()
        aspects = engine.calculate_aspects(golden_chart)
        
        # Validate against expected results
        assert len(aspects) >= 1
        
        # Check for expected conjunction
        conjunctions = [a for a in aspects if a.aspect_name == 'conjunction']
        assert len(conjunctions) >= 1
    
    def test_vibrational_golden_set(self):
        """Test Vibrational tradition against golden set."""
        # Load vibrational configuration
        config_path = "services/astrology_engine/aspect_configs/vibrational.json"
        
        if Path(config_path).exists():
            engine = AspectEngine(config_path)
            
            # Test with tight orbs - use 1.5° which is outside 1° orb
            chart_data = {
                "planet_positions": {
                    "sun": 0.0,
                    "moon": 121.5  # 1.5° outside exact trine, outside 1° orb
                }
            }

            aspects = engine.calculate_aspects(chart_data)

            # With tight vibrational orbs, this should not be an aspect
            trine_aspects = [a for a in aspects if a.aspect_name == 'trine']
            assert len(trine_aspects) == 0  # Outside 1° orb


class TestJSONOutput:
    """Test JSON output formatting."""
    
    def test_json_output_format(self):
        """Test JSON output format matches specification."""
        chart_data = {
            "planet_positions": {
                "sun": 0.0,
                "moon": 120.0
            }
        }
        
        engine = AspectEngine()
        aspects = engine.calculate_aspects(chart_data)
        json_output = engine.to_json(aspects)
        
        assert isinstance(json_output, list)
        
        if len(json_output) > 0:
            aspect_json = json_output[0]
            
            # Check required fields
            assert 'pair' in aspect_json
            assert 'type' in aspect_json
            assert 'angle' in aspect_json
            assert 'orb' in aspect_json
            
            # Check format
            assert isinstance(aspect_json['pair'], list)
            assert len(aspect_json['pair']) == 2
            assert isinstance(aspect_json['angle'], (int, float))
            assert isinstance(aspect_json['orb'], (int, float))


if __name__ == "__main__":
    pytest.main([__file__])
