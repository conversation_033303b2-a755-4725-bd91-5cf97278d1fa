"""
Test suite for Celebrity Data Validator

This test suite validates the celebrity data enhancement and validation functionality
including multi-source validation, birth data confidence scoring, and context validation.
"""

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

from services.llm_processor.celebrity_data_validator import CelebrityDataValidator
from services.llm_processor.models import Celebrity<PERSON><PERSON>
from services.database.models import CelebrityData, DataSource


class TestCelebrityDataValidator:
    """Test celebrity data validator functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = CelebrityDataValidator()
        
        # Mock celebrity database service
        self.validator.celebrity_db = AsyncMock()
        
        # Test celebrity match
        self.test_celebrity_match = CelebrityMatch(
            name="<PERSON>",
            confidence=0.8,
            context="Actor mentioned in environmental article",
            aliases=["<PERSON>", "Leonardo <PERSON>"],
            birth_date="1974-11-11",
            birth_location="Los Angeles, California",
            profession="Actor"
        )
        
        # Test article content
        self.test_article = """
        <PERSON>, the famous actor known for his environmental activism,
        spoke at the climate summit yesterday. The Oscar-winning performer has
        been a vocal advocate for environmental causes for many years.
        """
    
    @pytest.mark.asyncio
    async def test_expand_name_aliases(self):
        """Test name alias expansion functionality."""
        aliases = await self.validator._expand_name_aliases("<PERSON>")
        
        # Should include original name and variations
        assert "william smith" in aliases
        assert "bill smith" in aliases
        assert "will smith" in aliases
        assert "W. Smith" in aliases
        assert "Smith William" in aliases  # Reversed order
        
        # Test single name
        single_aliases = await self.validator._expand_name_aliases("<PERSON>")
        assert "madonna" in single_aliases
        assert len(single_aliases) >= 1
    
    @pytest.mark.asyncio
    async def test_validate_birth_date_valid(self):
        """Test birth date validation with valid dates."""
        # Test various valid formats
        valid_dates = [
            "1974-11-11",
            "11/11/1974",
            "November 11, 1974",
            "11 November 1974",
            "1974"
        ]
        
        for date_str in valid_dates:
            validation = await self.validator._validate_birth_date(date_str)
            assert validation['valid'] == True
            assert validation['confidence'] > 0.0
            assert validation['parsed_date'] is not None
    
    @pytest.mark.asyncio
    async def test_validate_birth_date_invalid(self):
        """Test birth date validation with invalid dates."""
        # Test invalid dates
        invalid_dates = [
            "1800-01-01",  # Too old
            "2020-01-01",  # Too young
            "invalid-date",  # Unparseable
            "13/45/1974"  # Invalid format
        ]
        
        for date_str in invalid_dates:
            validation = await self.validator._validate_birth_date(date_str)
            assert validation['valid'] == False or validation['confidence'] < 0.5
            assert len(validation['issues']) > 0
    
    @pytest.mark.asyncio
    async def test_validate_context_high_confidence(self):
        """Test context validation with high confidence scenario."""
        confidence = await self.validator._validate_context(
            self.test_celebrity_match, 
            self.test_article
        )
        
        # Should have high confidence due to:
        # - Name appears in article
        # - Profession context (actor)
        # - Celebrity keywords present
        assert confidence > 0.7
    
    @pytest.mark.asyncio
    async def test_validate_context_low_confidence(self):
        """Test context validation with low confidence scenario."""
        unrelated_article = """
        This article is about quantum physics and has nothing to do with
        entertainment or celebrities. It discusses particle interactions
        and theoretical frameworks.
        """
        
        confidence = await self.validator._validate_context(
            self.test_celebrity_match,
            unrelated_article
        )
        
        # Should have low confidence due to no relevant context
        assert confidence < 0.3
    
    @pytest.mark.asyncio
    async def test_score_birth_data_confidence(self):
        """Test birth data confidence scoring."""
        # Mock validation results with good birth data
        validation_results = {
            'birth_data_sources': [{
                'validation': {
                    'valid': True,
                    'confidence': 0.9
                }
            }],
            'database_matches': [{
                'celebrity': CelebrityData(
                    name="Test Celebrity",
                    birth_date=datetime(1974, 11, 11),
                    confidence_score=0.8
                ),
                'similarity': 0.9
            }],
            'confidence_factors': ['birth_location_provided', 'profession_provided']
        }
        
        confidence = await self.validator._score_birth_data_confidence(
            self.test_celebrity_match,
            validation_results
        )
        
        # Should have high confidence due to multiple factors
        assert confidence > 0.7
    
    @pytest.mark.asyncio
    async def test_calculate_overall_confidence(self):
        """Test overall confidence calculation."""
        validation_results = {
            'database_matches': [{
                'celebrity': CelebrityData(name="Test", confidence_score=0.8),
                'similarity': 0.9
            }]
        }
        
        overall_confidence = self.validator._calculate_overall_confidence(
            llm_confidence=0.8,
            birth_confidence=0.7,
            context_confidence=0.9,
            validation_results=validation_results
        )
        
        # Should be weighted average with bonus for multiple sources
        assert 0.7 <= overall_confidence <= 1.0
    
    @pytest.mark.asyncio
    async def test_validate_from_multiple_sources(self):
        """Test multi-source validation."""
        # Mock database matches
        mock_celebrity = CelebrityData(
            name="Leonardo DiCaprio",
            birth_date=datetime(1974, 11, 11),
            confidence_score=0.9
        )
        
        self.validator.celebrity_db.find_celebrity_by_name.return_value = [
            (mock_celebrity, 0.95)
        ]
        
        expanded_names = ["leonardo dicaprio", "leo dicaprio"]
        
        validation_results = await self.validator._validate_from_multiple_sources(
            self.test_celebrity_match,
            expanded_names
        )
        
        # Should find database matches
        assert len(validation_results['database_matches']) > 0
        assert validation_results['database_matches'][0]['similarity'] == 0.95
        
        # Should have confidence factors
        assert 'birth_location_provided' in validation_results['confidence_factors']
        assert 'profession_provided' in validation_results['confidence_factors']
    
    @pytest.mark.asyncio
    async def test_create_enhanced_celebrity_data_new(self):
        """Test creating enhanced celebrity data for new celebrity."""
        validation_results = {
            'database_matches': [],  # No existing matches
            'birth_data_sources': [{
                'validation': {
                    'valid': True,
                    'parsed_date': datetime(1974, 11, 11)
                }
            }]
        }
        
        enhanced_celebrity = await self.validator._create_enhanced_celebrity_data(
            self.test_celebrity_match,
            validation_results,
            0.85
        )
        
        # Should create new celebrity with enhanced data
        assert enhanced_celebrity.name == "Leonardo DiCaprio"
        assert enhanced_celebrity.birth_date == datetime(1974, 11, 11)
        assert enhanced_celebrity.birth_location == "Los Angeles, California"
        assert enhanced_celebrity.confidence_score == 0.85
        assert enhanced_celebrity.enhanced == True
        assert DataSource.LLM_ENHANCED in enhanced_celebrity.data_sources
    
    @pytest.mark.asyncio
    async def test_create_enhanced_celebrity_data_existing(self):
        """Test enhancing existing celebrity data."""
        existing_celebrity = CelebrityData(
            id="existing-id",
            name="Leonardo DiCaprio",
            birth_date=datetime(1974, 11, 11),
            confidence_score=0.7,
            data_sources=[DataSource.ASTRO_DATABANK],
            enhanced=False,
            verified=True,
            aliases=["Leo"]
        )
        
        validation_results = {
            'database_matches': [{
                'celebrity': existing_celebrity,
                'similarity': 0.95
            }],
            'birth_data_sources': []
        }
        
        enhanced_celebrity = await self.validator._create_enhanced_celebrity_data(
            self.test_celebrity_match,
            validation_results,
            0.85
        )
        
        # Should enhance existing celebrity
        assert enhanced_celebrity.id == "existing-id"
        assert enhanced_celebrity.confidence_score == 0.85  # Updated to higher score
        assert enhanced_celebrity.enhanced == True
        assert DataSource.LLM_ENHANCED in enhanced_celebrity.data_sources
        assert DataSource.ASTRO_DATABANK in enhanced_celebrity.data_sources
        assert "Leo" in enhanced_celebrity.aliases
        assert "Leonardo Wilhelm DiCaprio" in enhanced_celebrity.aliases
    
    @pytest.mark.asyncio
    async def test_validate_and_enhance_celebrity_full_workflow(self):
        """Test the complete validation and enhancement workflow."""
        # Mock database service
        self.validator.celebrity_db.find_celebrity_by_name.return_value = []
        
        # Test the full workflow
        enhanced_celebrity, confidence = await self.validator.validate_and_enhance_celebrity(
            self.test_celebrity_match,
            self.test_article
        )
        
        # Should return enhanced celebrity with reasonable confidence
        assert isinstance(enhanced_celebrity, CelebrityData)
        assert enhanced_celebrity.name == "Leonardo DiCaprio"
        assert enhanced_celebrity.enhanced == True
        assert 0.0 <= confidence <= 1.0
        
        # Should have processed birth date
        assert enhanced_celebrity.birth_date is not None
        assert enhanced_celebrity.birth_location == "Los Angeles, California"


class TestCelebrityDataValidatorEdgeCases:
    """Test edge cases and error handling."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = CelebrityDataValidator()
        self.validator.celebrity_db = AsyncMock()
    
    @pytest.mark.asyncio
    async def test_empty_celebrity_match(self):
        """Test handling of minimal celebrity match data."""
        minimal_match = CelebrityMatch(
            name="Unknown Person",
            confidence=0.3,
            context="Mentioned briefly"
        )
        
        enhanced_celebrity, confidence = await self.validator.validate_and_enhance_celebrity(
            minimal_match,
            "Brief article with no context."
        )
        
        # Should handle minimal data gracefully
        assert enhanced_celebrity.name == "Unknown Person"
        assert confidence < 0.5  # Low confidence due to minimal data


class TestCelebrityDataValidatorExternalSources:
    """Test external source validation features."""

    def setup_method(self):
        """Set up test fixtures."""
        self.validator = CelebrityDataValidator()
        self.validator.celebrity_db = AsyncMock()

        # Mock celebrity match for testing
        self.test_celebrity_match = CelebrityMatch(
            name="Leonardo DiCaprio",
            confidence=0.85,
            birth_date="1974-11-11",
            birth_location="Los Angeles, CA",
            context="Academy Award-winning actor",
            aliases=["Leo DiCaprio"]
        )

    @pytest.mark.asyncio
    async def test_validate_from_external_sources(self):
        """Test external source validation coordination."""
        expanded_names = ["leonardo dicaprio", "leo dicaprio"]

        # Mock the individual validation methods
        self.validator._validate_wikipedia = AsyncMock(return_value={
            'found': True, 'confidence': 0.9, 'data': {'title': 'Leonardo DiCaprio'}
        })
        self.validator._validate_astrodatabank = AsyncMock(return_value={
            'found': False, 'confidence': 0.0, 'data': {}
        })
        self.validator._validate_imdb = AsyncMock(return_value={
            'found': False, 'confidence': 0.0, 'data': {}
        })

        external_results = await self.validator._validate_from_external_sources(
            self.test_celebrity_match,
            expanded_names
        )

        # Verify structure
        assert 'wikipedia' in external_results
        assert 'astrodatabank' in external_results
        assert 'imdb' in external_results

        # Verify Wikipedia result
        assert external_results['wikipedia']['found'] == True
        assert external_results['wikipedia']['confidence'] == 0.9

    @pytest.mark.asyncio
    async def test_wikipedia_validation_no_session(self):
        """Test Wikipedia validation when no session is available."""
        expanded_names = ["leonardo dicaprio"]

        # No session set
        self.validator.session = None

        result = await self.validator._validate_wikipedia(
            self.test_celebrity_match,
            expanded_names
        )

        assert result['found'] == False
        assert result['confidence'] == 0.0

    @pytest.mark.asyncio
    async def test_extract_birth_date_from_text(self):
        """Test birth date extraction from text."""
        test_cases = [
            ("Leonardo DiCaprio (born November 11, 1974) is an actor.", "November 11, 1974"),
            ("Born 11 November 1974 in Los Angeles.", "11 November 1974"),
            ("Birth date: January 1, 1990", "January 1, 1990"),
            ("No birth date in this text.", None)
        ]

        for text, expected in test_cases:
            result = self.validator._extract_birth_date_from_text(text)
            assert result == expected

    @pytest.mark.asyncio
    async def test_data_quality_metrics(self):
        """Test data quality monitoring."""
        # Mock database statistics
        self.validator.celebrity_db.get_database_statistics.return_value = {
            'total_celebrities': 100,
            'enhanced_celebrities': 80,
            'verified_celebrities': 60
        }

        # Mock confidence distribution
        self.validator.celebrity_db.get_celebrities_by_confidence_range.side_effect = [
            [1, 2, 3],  # high confidence (3 celebrities)
            [4, 5],     # medium confidence (2 celebrities)
            [6]         # low confidence (1 celebrity)
        ]

        metrics = await self.validator.get_data_quality_metrics()

        assert metrics['total_celebrities'] == 100
        assert metrics['enhanced_celebrities'] == 80
        assert metrics['verified_celebrities'] == 60
        assert metrics['enhancement_rate'] == 0.8
        assert metrics['verification_rate'] == 0.6
        assert 'confidence_distribution' in metrics
        assert metrics['confidence_distribution']['high_confidence'] == 3

    @pytest.mark.asyncio
    async def test_enhanced_confidence_calculation_with_external_sources(self):
        """Test confidence calculation including external sources."""
        validation_results = {
            'database_matches': [{'similarity': 0.9}],
            'external_sources': {
                'wikipedia': {'found': True, 'confidence': 0.85},
                'astrodatabank': {'found': False, 'confidence': 0.0},
                'imdb': {'found': False, 'confidence': 0.0}
            }
        }

        confidence = self.validator._calculate_overall_confidence(
            llm_confidence=0.8,
            birth_confidence=0.9,
            context_confidence=0.7,
            validation_results=validation_results
        )

        # Should be higher than without external sources
        assert confidence > 0.7
        assert confidence <= 1.0
    
    @pytest.mark.asyncio
    async def test_no_article_content(self):
        """Test handling when no article content is provided."""
        celebrity_match = CelebrityMatch(
            name="Test Celebrity",
            confidence=0.7,
            context="Test context"
        )
        
        enhanced_celebrity, confidence = await self.validator.validate_and_enhance_celebrity(
            celebrity_match,
            ""  # Empty article content
        )
        
        # Should handle empty content gracefully
        assert isinstance(enhanced_celebrity, CelebrityData)
        assert confidence > 0.0  # Should still have some confidence
    
    @pytest.mark.asyncio
    async def test_database_error_handling(self):
        """Test handling of database errors."""
        # Mock database to raise exception
        self.validator.celebrity_db.find_celebrity_by_name.side_effect = Exception("Database error")
        
        celebrity_match = CelebrityMatch(
            name="Test Celebrity",
            confidence=0.7,
            context="Test context"
        )
        
        # Should handle database errors gracefully
        enhanced_celebrity, confidence = await self.validator.validate_and_enhance_celebrity(
            celebrity_match,
            "Test article content"
        )
        
        assert isinstance(enhanced_celebrity, CelebrityData)
        assert confidence > 0.0


if __name__ == "__main__":
    pytest.main([__file__])
