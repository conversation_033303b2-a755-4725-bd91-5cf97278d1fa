"""
Test suite for Enhanced Celebrity Database Service

This test suite validates the enhanced celebrity database functionality including
advanced search, performance optimization, and historical accuracy tracking.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

from services.database.celebrity_db_service import (
    CelebrityDBService, 
    CelebrityMatchResult, 
    SearchMetrics
)
from services.database.models import CelebrityData, DataSource


class TestEnhancedCelebrityDBService:
    """Test enhanced celebrity database service functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.service = CelebrityDBService()
        
        # Mock connection manager
        self.mock_connection_manager = AsyncMock()
        self.mock_session = AsyncMock()

        # Properly mock the async context manager
        mock_session_context = AsyncMock()
        mock_session_context.__aenter__ = AsyncMock(return_value=self.mock_session)
        mock_session_context.__aexit__ = AsyncMock(return_value=None)
        self.mock_connection_manager.database.get_session.return_value = mock_session_context

        self.mock_connection_manager.cache = AsyncMock()
        
        # Test celebrity data
        self.test_celebrity = CelebrityData(
            id="test-id-1",
            name="<PERSON>",
            birth_date=datetime(1974, 11, 11),
            birth_location="Los Angeles, California",
            confidence_score=0.95,
            data_sources=[DataSource.ASTRO_DATABANK],
            enhanced=True,
            verified=True,
            aliases=["Leo", "Leonardo Wilhelm DiCaprio"],
            professional_name="Leonardo DiCaprio"
        )
    
    @pytest.mark.asyncio
    async def test_advanced_celebrity_search_exact_match(self):
        """Test advanced celebrity search with exact match."""
        with patch('services.database.celebrity_db_service.get_connection_manager', 
                  return_value=self.mock_connection_manager):
            
            # Mock exact search results
            mock_row = MagicMock()
            mock_row.id = "test-id-1"
            mock_row.name = "Leonardo DiCaprio"
            mock_row.birth_date = datetime(1974, 11, 11)
            mock_row.birth_location = "Los Angeles, California"
            mock_row.confidence_score = 0.95
            mock_row.data_sources = '["astro_databank"]'
            mock_row.enhanced = True
            mock_row.verified = True
            mock_row.aliases = '["Leo", "Leonardo Wilhelm DiCaprio"]'
            mock_row.professional_name = "Leonardo DiCaprio"
            mock_row.birth_time = None
            mock_row.birth_latitude = None
            mock_row.birth_longitude = None
            mock_row.birth_timezone = None
            mock_row.created_at = datetime.utcnow()
            mock_row.updated_at = datetime.utcnow()
            mock_row.last_verified_at = None
            
            self.mock_session.execute.return_value.fetchall.return_value = [mock_row]
            self.mock_connection_manager.cache.get.return_value = None  # No cache
            
            results = await self.service.advanced_celebrity_search("Leonardo DiCaprio")
            
            assert len(results) == 1
            assert isinstance(results[0], CelebrityMatchResult)
            assert results[0].celebrity.name == "Leonardo DiCaprio"
            assert results[0].similarity_score == 1.0
            assert results[0].match_type == 'exact'
            assert 'exact_name_match' in results[0].confidence_factors
    
    @pytest.mark.asyncio
    async def test_enhanced_similarity_calculation(self):
        """Test enhanced similarity calculation with multiple factors."""
        query_name = "leo dicaprio"
        
        match_result = self.service._calculate_enhanced_similarity(query_name, self.test_celebrity)
        
        assert isinstance(match_result, CelebrityMatchResult)
        assert match_result.celebrity == self.test_celebrity
        assert match_result.similarity_score > 0.7  # Should be reasonably high due to alias match
        assert 'verified_celebrity' in match_result.confidence_factors
        assert 'enhanced_data' in match_result.confidence_factors
        assert 'high_confidence_score' in match_result.confidence_factors
    
    @pytest.mark.asyncio
    async def test_search_caching(self):
        """Test search result caching functionality."""
        with patch('services.database.celebrity_db_service.get_connection_manager', 
                  return_value=self.mock_connection_manager):
            
            # Test cache miss and set
            self.mock_connection_manager.cache.get.return_value = None
            
            # Mock search results
            mock_row = MagicMock()
            mock_row.id = "test-id-1"
            mock_row.name = "Leonardo DiCaprio"
            mock_row.confidence_score = 0.95
            mock_row.data_sources = '["astro_databank"]'
            mock_row.enhanced = True
            mock_row.verified = True
            mock_row.aliases = '["Leo"]'
            mock_row.professional_name = "Leonardo DiCaprio"
            # Add other required fields
            for field in ['birth_date', 'birth_time', 'birth_location', 'birth_latitude', 
                         'birth_longitude', 'birth_timezone', 'created_at', 'updated_at', 'last_verified_at']:
                setattr(mock_row, field, None)
            
            self.mock_session.execute.return_value.fetchall.return_value = [mock_row]
            
            results = await self.service.advanced_celebrity_search("Leonardo DiCaprio")
            
            # Verify cache.set was called
            assert self.mock_connection_manager.cache.set.called
            assert len(results) == 1
    
    @pytest.mark.asyncio
    async def test_search_metrics_tracking(self):
        """Test search metrics tracking functionality."""
        with patch('services.database.celebrity_db_service.get_connection_manager', 
                  return_value=self.mock_connection_manager):
            
            await self.service._track_search_metrics("test query", 5, 150.5)
            
            # Verify metrics were stored in cache
            assert self.mock_connection_manager.cache.set.called
            call_args = self.mock_connection_manager.cache.set.call_args
            assert "search_metrics:" in call_args[0][0]  # Key contains prefix
            
            # Verify metrics data structure
            import json
            metrics_data = json.loads(call_args[0][1])
            assert metrics_data['query'] == "test query"
            assert metrics_data['results_count'] == 5
            assert metrics_data['search_time_ms'] == 150.5
    
    @pytest.mark.asyncio
    async def test_batch_create_celebrities(self):
        """Test batch celebrity creation."""
        # Mock get_connection_manager as an async function
        async def mock_get_connection_manager():
            return self.mock_connection_manager

        with patch('services.database.celebrity_db_service.get_connection_manager',
                  side_effect=mock_get_connection_manager):

            celebrities = [
                CelebrityData(
                    name="Celebrity 1",
                    confidence_score=0.8,
                    data_sources=[DataSource.LLM_ENHANCED],
                    enhanced=False,
                    verified=False,
                    aliases=[]
                ),
                CelebrityData(
                    name="Celebrity 2",
                    confidence_score=0.9,
                    data_sources=[DataSource.ASTRO_DATABANK],
                    enhanced=True,
                    verified=True,
                    aliases=["Celeb2"]
                )
            ]

            created = await self.service.batch_create_celebrities(celebrities)

            assert len(created) == 2
            assert all(celeb.id is not None for celeb in created)
            assert all(celeb.created_at is not None for celeb in created)
            assert self.mock_session.execute.call_count == 2  # Two inserts
            assert self.mock_session.commit.called
    
    @pytest.mark.asyncio
    async def test_get_celebrities_by_confidence_range(self):
        """Test getting celebrities by confidence range."""
        # Mock get_connection_manager as an async function
        async def mock_get_connection_manager():
            return self.mock_connection_manager

        with patch('services.database.celebrity_db_service.get_connection_manager',
                  side_effect=mock_get_connection_manager):
            
            # Mock database results
            mock_row = MagicMock()
            mock_row.id = "test-id-1"
            mock_row.name = "High Confidence Celebrity"
            mock_row.confidence_score = 0.95
            mock_row.data_sources = '["astro_databank"]'
            mock_row.enhanced = True
            mock_row.verified = True
            mock_row.aliases = '[]'
            mock_row.professional_name = None
            # Add other required fields
            for field in ['birth_date', 'birth_time', 'birth_location', 'birth_latitude', 
                         'birth_longitude', 'birth_timezone', 'created_at', 'updated_at', 'last_verified_at']:
                setattr(mock_row, field, None)
            
            self.mock_session.execute.return_value.fetchall.return_value = [mock_row]
            
            results = await self.service.get_celebrities_by_confidence_range(0.9, 1.0)
            
            assert len(results) == 1
            assert results[0].name == "High Confidence Celebrity"
            assert results[0].confidence_score == 0.95
    
    @pytest.mark.asyncio
    async def test_get_database_statistics(self):
        """Test database statistics retrieval."""
        # Mock get_connection_manager as an async function
        async def mock_get_connection_manager():
            return self.mock_connection_manager

        with patch('services.database.celebrity_db_service.get_connection_manager',
                  side_effect=mock_get_connection_manager):
            
            # Mock statistics query results
            mock_stats = MagicMock()
            mock_stats.total_celebrities = 100
            mock_stats.verified_count = 75
            mock_stats.enhanced_count = 80
            mock_stats.avg_confidence = 0.85
            mock_stats.min_confidence = 0.3
            mock_stats.max_confidence = 1.0
            mock_stats.with_birth_date = 90
            mock_stats.with_birth_location = 85
            mock_stats.with_aliases = 60
            
            # Mock data source distribution
            mock_source_rows = [
                MagicMock(source="astro_databank", count=50),
                MagicMock(source="llm_enhanced", count=30),
                MagicMock(source="wikipedia", count=20)
            ]
            
            self.mock_session.execute.return_value.fetchone.return_value = mock_stats
            self.mock_session.execute.return_value.fetchall.return_value = mock_source_rows
            
            stats = await self.service.get_database_statistics()
            
            assert stats['total_celebrities'] == 100
            assert stats['verified_count'] == 75
            assert stats['enhanced_count'] == 80
            assert stats['avg_confidence_score'] == 0.85
            assert stats['data_completeness_percentage'] == 90.0
            assert 'data_source_distribution' in stats
            assert stats['data_source_distribution']['astro_databank'] == 50
    
    @pytest.mark.asyncio
    async def test_search_accuracy_feedback(self):
        """Test search accuracy feedback tracking."""
        with patch('services.database.celebrity_db_service.get_connection_manager', 
                  return_value=self.mock_connection_manager):
            
            await self.service.update_search_accuracy_feedback(
                "leonardo dicaprio", 
                "test-id-1", 
                True
            )
            
            # Verify feedback was stored
            assert self.mock_connection_manager.cache.set.called
            call_args = self.mock_connection_manager.cache.set.call_args
            assert "feedback:" in call_args[0][0]  # Key contains feedback prefix
            
            # Verify feedback data
            import json
            feedback_data = json.loads(call_args[0][1])
            assert feedback_data['query'] == "leonardo dicaprio"
            assert feedback_data['celebrity_id'] == "test-id-1"
            assert feedback_data['was_correct'] == True
    
    @pytest.mark.asyncio
    async def test_get_search_performance_stats(self):
        """Test search performance statistics retrieval."""
        with patch('services.database.celebrity_db_service.get_connection_manager', 
                  return_value=self.mock_connection_manager):
            
            # Mock cache keys and data
            mock_keys = [
                "search_metrics:2024-01-01T10:00:00",
                "search_metrics:2024-01-01T11:00:00",
                "search_metrics:2024-01-01T12:00:00"
            ]
            
            # Use recent timestamps to pass the date filter
            recent_time = datetime.utcnow().isoformat()
            mock_metrics_data = [
                f'{{"query": "leonardo dicaprio", "results_count": 3, "search_time_ms": 150.0, "timestamp": "{recent_time}"}}',
                f'{{"query": "brad pitt", "results_count": 2, "search_time_ms": 120.0, "timestamp": "{recent_time}"}}',
                f'{{"query": "leonardo dicaprio", "results_count": 3, "search_time_ms": 140.0, "timestamp": "{recent_time}"}}'
            ]
            
            self.mock_connection_manager.cache.keys.return_value = mock_keys
            self.mock_connection_manager.cache.get.side_effect = mock_metrics_data
            
            stats = await self.service.get_search_performance_stats(7)
            
            assert stats['total_searches'] == 3
            assert stats['avg_search_time_ms'] == 136.67  # (150 + 120 + 140) / 3
            assert stats['avg_results_count'] == 2.67  # (3 + 2 + 3) / 3
            assert 'top_queries' in stats
            assert stats['period_days'] == 7


class TestCelebrityMatchResult:
    """Test CelebrityMatchResult functionality."""
    
    def test_celebrity_match_result_creation(self):
        """Test creating CelebrityMatchResult objects."""
        celebrity = CelebrityData(
            name="Test Celebrity",
            confidence_score=0.8,
            data_sources=[DataSource.LLM_ENHANCED],
            enhanced=False,
            verified=False,
            aliases=[]
        )
        
        match_result = CelebrityMatchResult(
            celebrity=celebrity,
            similarity_score=0.95,
            match_type='exact',
            confidence_factors=['exact_name_match', 'verified_celebrity'],
            search_metadata={'query': 'test celebrity', 'search_strategy': 'exact'}
        )
        
        assert match_result.celebrity == celebrity
        assert match_result.similarity_score == 0.95
        assert match_result.match_type == 'exact'
        assert len(match_result.confidence_factors) == 2
        assert match_result.search_metadata['query'] == 'test celebrity'


if __name__ == "__main__":
    pytest.main([__file__])
