"""
Unit Tests for Ephemeris Calculator Service

Tests for Swiss Ephemeris integration with test mode fallback.
"""

import pytest
from datetime import datetime
from unittest.mock import patch, MagicMock

from services.astrology_engine import (
    EphemerisCalculator, EphemerisDataManager, EphemerisConfig,
    ChartCalculationRequest, Location, EphemerisData, CelestialBodyPosition
)


class TestEphemerisDataManager:
    """Test ephemeris data management functionality."""
    
    def test_initialization(self):
        """Test ephemeris data manager initialization."""
        manager = EphemerisDataManager()
        
        assert manager.config is not None
        assert len(manager.data_paths) > 0
        assert len(manager.required_files) > 0
        assert 'sepl_18.se1' in manager.required_files
    
    def test_custom_config(self):
        """Test initialization with custom configuration."""
        config = EphemerisConfig(test_mode=True, data_path="/custom/path")
        manager = EphemerisDataManager(config)
        
        assert manager.config.test_mode is True
        assert "/custom/path" in manager.data_paths
    
    def test_environment_path_detection(self):
        """Test environment variable path detection."""
        with patch.dict('os.environ', {'SWISSEPH_PATH': '/env/path'}):
            manager = EphemerisDataManager()
            assert '/env/path' in manager.data_paths
    
    def test_ensure_ephemeris_availability_test_mode(self):
        """Test ephemeris availability check in test mode."""
        config = EphemerisConfig(test_mode=True)
        manager = EphemerisDataManager(config)
        
        status = manager.ensure_ephemeris_availability()
        
        assert status.status == 'test_mode'
        assert 'test mode' in status.message.lower()
    
    @patch('os.path.exists')
    @patch('os.path.isdir')
    @patch('os.path.getsize')
    @patch('os.path.isfile')
    def test_validate_ephemeris_installation_success(self, mock_isfile, mock_getsize, mock_isdir, mock_exists):
        """Test successful ephemeris installation validation."""
        mock_exists.return_value = True
        mock_isdir.return_value = True
        mock_isfile.return_value = True
        mock_getsize.return_value = 10000  # Large enough file

        manager = EphemerisDataManager()
        result = manager._validate_ephemeris_installation('/test/path')

        assert result['valid'] is True
        assert len(result['found_files']) > 0
    
    @patch('os.path.exists')
    def test_validate_ephemeris_installation_missing_path(self, mock_exists):
        """Test ephemeris validation with missing path."""
        mock_exists.return_value = False
        
        manager = EphemerisDataManager()
        result = manager._validate_ephemeris_installation('/missing/path')
        
        assert result['valid'] is False
        assert 'does not exist' in result['reason']
    
    def test_get_installation_instructions(self):
        """Test installation instructions generation."""
        manager = EphemerisDataManager()
        instructions = manager.get_installation_instructions()
        
        assert 'Swiss Ephemeris' in instructions
        assert 'sepl_18.se1' in instructions
        assert 'astro.com' in instructions
    
    def test_is_test_mode(self):
        """Test test mode detection."""
        manager = EphemerisDataManager()
        
        # Should be test mode when no valid data found
        assert manager.is_test_mode() is True


class TestSwissEphemerisClient:
    """Test Swiss Ephemeris client wrapper."""
    
    def test_initialization_without_swisseph(self):
        """Test initialization when swisseph library is not available."""
        from services.astrology_engine.ephemeris_calculator import SwissEphemerisClient

        with patch('builtins.__import__', side_effect=ImportError):
            client = SwissEphemerisClient()
            result = client.initialize()

            assert result is False
            assert client._initialized is False
    
    def test_planet_mapping(self):
        """Test planet ID mapping."""
        from services.astrology_engine.ephemeris_calculator import SwissEphemerisClient
        
        client = SwissEphemerisClient()
        
        assert client._planet_mapping['Sun'] == 0
        assert client._planet_mapping['Moon'] == 1
        assert client._planet_mapping['Mercury'] == 2
        assert 'North Node' in client._planet_mapping
    
    def test_datetime_to_julian_day_fallback(self):
        """Test Julian day conversion fallback."""
        from services.astrology_engine.ephemeris_calculator import SwissEphemerisClient
        
        client = SwissEphemerisClient()
        test_date = datetime(2000, 1, 1, 12, 0, 0)
        
        jd = client.datetime_to_julian_day(test_date)
        
        # Should be approximately J2000.0 (2451545.0)
        assert 2451544.0 < jd < 2451546.0


class TestTestModeEphemeris:
    """Test test mode ephemeris functionality."""
    
    def test_initialization(self):
        """Test test mode ephemeris initialization."""
        from services.astrology_engine.ephemeris_calculator import TestModeEphemeris
        
        test_ephemeris = TestModeEphemeris()
        
        assert len(test_ephemeris._base_positions) > 0
        assert len(test_ephemeris._daily_motions) > 0
        assert 'Sun' in test_ephemeris._base_positions
    
    def test_calculate_position(self):
        """Test position calculation in test mode."""
        from services.astrology_engine.ephemeris_calculator import TestModeEphemeris
        
        test_ephemeris = TestModeEphemeris()
        test_date = datetime(2000, 1, 1, 12, 0, 0)
        
        position = test_ephemeris.calculate_position(test_date, 'Sun')
        
        assert isinstance(position, CelestialBodyPosition)
        assert 0.0 <= position.lon <= 360.0
        assert position.speed > 0  # Sun should have positive motion
    
    def test_calculate_position_unknown_body(self):
        """Test position calculation for unknown celestial body."""
        from services.astrology_engine.ephemeris_calculator import TestModeEphemeris
        
        test_ephemeris = TestModeEphemeris()
        test_date = datetime(2000, 1, 1, 12, 0, 0)
        
        position = test_ephemeris.calculate_position(test_date, 'UnknownPlanet')
        
        assert isinstance(position, CelestialBodyPosition)
        assert 0.0 <= position.lon <= 360.0


class TestEphemerisCalculator:
    """Test main ephemeris calculator service."""
    
    def test_initialization(self):
        """Test ephemeris calculator initialization."""
        calculator = EphemerisCalculator()
        
        assert calculator.config is not None
        assert calculator.data_manager is not None
        assert calculator.test_mode is not None
    
    def test_initialization_with_config(self):
        """Test initialization with custom configuration."""
        config = EphemerisConfig(test_mode=True)
        calculator = EphemerisCalculator(config)
        
        assert calculator.config.test_mode is True
    
    def test_is_test_mode(self):
        """Test test mode detection."""
        calculator = EphemerisCalculator()
        
        # Should be in test mode when Swiss Ephemeris not available
        assert calculator.is_test_mode() is True
    
    def test_calculate_chart(self):
        """Test complete chart calculation."""
        calculator = EphemerisCalculator()
        
        request = ChartCalculationRequest(
            datetime=datetime(2000, 1, 1, 12, 0, 0),
            location=Location(latitude=40.7128, longitude=-74.0060, timezone="America/New_York"),
            celestial_bodies=['Sun', 'Moon', 'Mercury']
        )
        
        chart = calculator.calculate_chart(request)
        
        assert isinstance(chart, EphemerisData)
        assert len(chart.celestial_bodies) == 3
        assert 'Sun' in chart.celestial_bodies
        assert 'Moon' in chart.celestial_bodies
        assert 'Mercury' in chart.celestial_bodies
    
    def test_calculate_chart_performance(self):
        """Test chart calculation performance."""
        calculator = EphemerisCalculator()
        
        request = ChartCalculationRequest(
            datetime=datetime(2000, 1, 1, 12, 0, 0),
            location=Location(latitude=0.0, longitude=0.0, timezone="UTC"),
            celestial_bodies=['Sun', 'Moon', 'Mercury', 'Venus', 'Mars']
        )
        
        import time
        start_time = time.time()
        chart = calculator.calculate_chart(request)
        calculation_time = time.time() - start_time
        
        # Should complete within performance target
        assert calculation_time < 0.2  # 200ms target
        assert len(chart.celestial_bodies) == 5
    
    def test_get_performance_stats(self):
        """Test performance statistics tracking."""
        calculator = EphemerisCalculator()
        
        # Perform a calculation
        request = ChartCalculationRequest(
            datetime=datetime(2000, 1, 1, 12, 0, 0),
            location=Location(latitude=0.0, longitude=0.0, timezone="UTC"),
            celestial_bodies=['Sun', 'Moon']
        )
        calculator.calculate_chart(request)
        
        stats = calculator.get_performance_stats()
        
        assert stats['calculations'] >= 1
        assert stats['total_time'] > 0
        assert 'average_time' in stats
    
    def test_validate_accuracy(self):
        """Test calculation accuracy validation."""
        calculator = EphemerisCalculator()
        
        validation = calculator.validate_accuracy()
        
        assert 'calculation_successful' in validation
        assert 'sun_position_reasonable' in validation
        assert 'moon_position_reasonable' in validation
        assert 'performance_acceptable' in validation
    
    def test_get_status_info(self):
        """Test status information retrieval."""
        calculator = EphemerisCalculator()
        
        status = calculator.get_status_info()
        
        assert 'ephemeris_status' in status
        assert 'test_mode' in status
        assert 'swiss_ephemeris_available' in status
        assert 'calculations_performed' in status
        assert status['test_mode'] is True  # Should be test mode without Swiss Ephemeris
    
    def test_calculate_single_position_fallback(self):
        """Test single position calculation with fallback."""
        calculator = EphemerisCalculator()
        
        position = calculator._calculate_single_position(
            datetime(2000, 1, 1, 12, 0, 0), 
            'Sun'
        )
        
        assert isinstance(position, CelestialBodyPosition)
        assert 0.0 <= position.lon <= 360.0
        assert position.speed > 0


class TestIntegrationWithVAAlgorithm:
    """Test integration between ephemeris calculator and VA algorithm."""
    
    def test_va_algorithm_with_ephemeris_calculator(self):
        """Test VA algorithm using ephemeris calculator data."""
        from services.astrology_engine import VACircuitDetector
        
        calculator = EphemerisCalculator()
        detector = VACircuitDetector()
        
        # Create chart using ephemeris calculator
        request = ChartCalculationRequest(
            datetime=datetime(2000, 1, 1, 12, 0, 0),
            location=Location(latitude=40.7128, longitude=-74.0060, timezone="America/New_York"),
            celestial_bodies=['Sun', 'Moon', 'Mercury', 'Venus', 'Mars', 'Jupiter']
        )
        
        chart = calculator.calculate_chart(request)
        
        # Run VA analysis
        result = detector._analyze_vibrational_astrology(chart)
        
        assert result['total_circuits_found'] >= 0
        assert len(result['harmonics_analyzed']) == 7
        assert 'summary' in result
    
    def test_performance_integration(self):
        """Test performance of integrated ephemeris + VA analysis."""
        from services.astrology_engine import VACircuitDetector
        
        calculator = EphemerisCalculator()
        detector = VACircuitDetector()
        
        request = ChartCalculationRequest(
            datetime=datetime(2000, 1, 1, 12, 0, 0),
            location=Location(latitude=0.0, longitude=0.0, timezone="UTC"),
            celestial_bodies=['Sun', 'Moon', 'Mercury', 'Venus', 'Mars', 'Jupiter', 'Saturn']
        )
        
        import time
        start_time = time.time()
        
        # Full pipeline: ephemeris calculation + VA analysis
        chart = calculator.calculate_chart(request)
        va_result = detector._analyze_vibrational_astrology(chart)
        
        total_time = time.time() - start_time
        
        # Should meet performance targets
        assert total_time < 2.0  # VA analysis target
        assert len(chart.celestial_bodies) == 7
        assert va_result['total_circuits_found'] >= 0
