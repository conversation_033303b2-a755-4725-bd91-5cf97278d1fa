"""
Test suite for News Service Modernization

This test suite validates the modernized news services including
multi-source aggregation, content deduplication, and quality filtering.
"""

import pytest
import asyncio
from datetime import datetime, timezone, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

from services.news_pipeline.multi_source_news_client import (
    MultiSourceNewsClient,
    MultiSourceNewsConfig,
    NewsSourceConfig,
    NewsSource,
    UnifiedNewsArticle,
    ContentQualityAssessor,
    QualityMetrics,
    ContentQuality
)
from services.news_pipeline.content_deduplication_service import (
    ContentDeduplicationService,
    DeduplicationConfig,
    ContentFingerprint,
    DuplicateMatch,
    SimilarityMethod,
    DuplicateType
)
from services.news_pipeline.gnews_client import GNewsArticle


class TestMultiSourceNewsClient:
    """Test multi-source news client functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.config = MultiSourceNewsConfig(
            sources=[
                NewsSourceConfig(
                    source=NewsSource.GNEWS,
                    enabled=True,
                    api_key="test_key",
                    priority=1
                )
            ],
            max_articles_per_source=10,
            min_quality_score=0.5
        )
        
        # Mock the GNews client initialization
        with patch('services.news_pipeline.multi_source_news_client.GNewsClient'):
            self.client = MultiSourceNewsClient(self.config)
        
        # Test articles
        self.test_gnews_article = GNewsArticle(
            title="Test Celebrity News",
            description="This is a test article about celebrities",
            content="This is the full content of the test article about celebrities.",
            url="https://example.com/test",
            source={"name": "Test Source", "url": "https://testsource.com"},
            publishedAt="2024-01-01T12:00:00Z",
            image="https://example.com/image.jpg"
        )
    
    def test_client_initialization(self):
        """Test multi-source news client initialization."""
        assert self.client.config == self.config
        assert len(self.client.source_clients) >= 0  # May be 0 due to mocking
        assert self.client.stats.total_articles_fetched == 0
    
    def test_gnews_article_conversion(self):
        """Test conversion of GNews article to unified format."""
        unified_article = self.client._convert_gnews_article(self.test_gnews_article)
        
        assert isinstance(unified_article, UnifiedNewsArticle)
        assert unified_article.title == self.test_gnews_article.title
        assert unified_article.description == self.test_gnews_article.description
        assert unified_article.content == self.test_gnews_article.content
        assert unified_article.url == self.test_gnews_article.url
        assert unified_article.source_name == "Test Source"  # Extracted from source dict
        assert unified_article.source_type == NewsSource.GNEWS
        assert unified_article.image_url == self.test_gnews_article.image
        assert unified_article.id.startswith("gnews_")
    
    @pytest.mark.asyncio
    async def test_quality_filtering(self):
        """Test quality filtering functionality."""
        # Create test articles with different quality levels
        articles = [
            UnifiedNewsArticle(
                id="high_quality",
                title="High Quality News Article",
                description="This is a well-written article with substantial content and credible source.",
                url="https://reuters.com/test",
                source_name="Reuters",
                source_type=NewsSource.GNEWS,
                published_at=datetime.now(timezone.utc)
            ),
            UnifiedNewsArticle(
                id="low_quality",
                title="Click here for shocking news!",
                description="Short",
                url="https://spam-site.com/test",
                source_name="Spam Site",
                source_type=NewsSource.GNEWS,
                published_at=datetime.now(timezone.utc) - timedelta(days=30)
            )
        ]
        
        filtered_articles = await self.client._apply_quality_filtering(articles)
        
        # High quality article should pass, low quality should be filtered
        assert len(filtered_articles) == 1
        assert filtered_articles[0].id == "high_quality"
        assert filtered_articles[0].quality_metrics is not None
        assert filtered_articles[0].quality_metrics.overall_quality >= self.config.min_quality_score
    
    @pytest.mark.asyncio
    async def test_deduplication(self):
        """Test content deduplication functionality."""
        # Create duplicate articles
        articles = [
            UnifiedNewsArticle(
                id="original",
                title="Breaking News Story",
                description="This is the original news story",
                url="https://example.com/original",
                source_name="Source 1",
                source_type=NewsSource.GNEWS,
                published_at=datetime.now(timezone.utc)
            ),
            UnifiedNewsArticle(
                id="duplicate",
                title="Breaking News Story",
                description="This is the original news story",
                url="https://example.com/duplicate",
                source_name="Source 2",
                source_type=NewsSource.GNEWS,
                published_at=datetime.now(timezone.utc)
            ),
            UnifiedNewsArticle(
                id="unique",
                title="Different News Story",
                description="This is a completely different story",
                url="https://example.com/unique",
                source_name="Source 3",
                source_type=NewsSource.GNEWS,
                published_at=datetime.now(timezone.utc)
            )
        ]
        
        deduplicated_articles = await self.client._apply_deduplication(articles)
        
        # Should have 2 unique articles (duplicate removed)
        assert len(deduplicated_articles) == 2
        article_ids = {article.id for article in deduplicated_articles}
        assert "unique" in article_ids
        # Either original or duplicate should remain, but not both
        assert len(article_ids.intersection({"original", "duplicate"})) == 1
    
    def test_stats_tracking(self):
        """Test statistics tracking."""
        stats = self.client.get_stats()
        
        assert stats.total_articles_fetched == 0
        assert stats.articles_processed == 0
        assert stats.average_quality_score == 0.0
        assert isinstance(stats.articles_by_source, dict)
    
    def test_health_status(self):
        """Test health status monitoring."""
        health = self.client.get_health_status()
        
        assert health["service"] == "multi_source_news_client"
        assert health["status"] == "healthy"
        assert "sources_enabled" in health
        assert "articles_processed" in health


class TestContentQualityAssessor:
    """Test content quality assessment functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.assessor = ContentQualityAssessor()
    
    def test_high_quality_assessment(self):
        """Test assessment of high-quality content."""
        article = UnifiedNewsArticle(
            id="test",
            title="Breaking: Important News Development",
            description="A comprehensive analysis of recent developments in the field with expert commentary and detailed background information.",
            url="https://reuters.com/news/test",
            source_name="Reuters",
            source_type=NewsSource.GNEWS,
            published_at=datetime.now(timezone.utc),
            content="This is a well-researched article with substantial content that provides valuable insights into current events. The article includes expert quotes, statistical data, and comprehensive analysis of the situation."
        )
        
        metrics = self.assessor.assess_quality(article)
        
        assert isinstance(metrics, QualityMetrics)
        assert metrics.overall_quality > 0.6
        assert metrics.source_credibility > 0.8  # Reuters is credible
        assert metrics.freshness_score > 0.8  # Recent article
        assert metrics.quality_level in [ContentQuality.HIGH, ContentQuality.MEDIUM]
    
    def test_low_quality_assessment(self):
        """Test assessment of low-quality content."""
        article = UnifiedNewsArticle(
            id="test",
            title="You won't believe this shocking truth!",
            description="Click here for amazing results!",
            url="https://spam-blog.wordpress.com/test",
            source_name="Spam Blog",
            source_type=NewsSource.GNEWS,
            published_at=datetime.now(timezone.utc) - timedelta(days=30),
            content="Short content with buy now and get rich quick schemes."
        )
        
        metrics = self.assessor.assess_quality(article)
        
        assert metrics.overall_quality < 0.5
        assert metrics.source_credibility < 0.6  # Blog source
        assert metrics.freshness_score < 0.8  # Older article
        assert metrics.quality_level in [ContentQuality.LOW, ContentQuality.SPAM]
    
    def test_source_credibility_assessment(self):
        """Test source credibility assessment."""
        # Test credible source
        credible_score = self.assessor._assess_source_credibility("https://reuters.com/test", "Reuters")
        assert credible_score >= 0.8
        
        # Test unknown source
        unknown_score = self.assessor._assess_source_credibility("https://unknown-site.com/test", "Unknown")
        assert 0.4 <= unknown_score <= 0.7
        
        # Test suspicious source
        suspicious_score = self.assessor._assess_source_credibility("https://spam.blog.com/test", "Blog")
        assert suspicious_score <= 0.5
    
    def test_freshness_assessment(self):
        """Test content freshness assessment."""
        now = datetime.now(timezone.utc)
        
        # Very fresh content (1 hour old)
        fresh_score = self.assessor._assess_freshness(now - timedelta(hours=1))
        assert fresh_score >= 0.9
        
        # Day-old content
        day_old_score = self.assessor._assess_freshness(now - timedelta(days=1))
        assert 0.6 <= day_old_score <= 0.9
        
        # Week-old content
        week_old_score = self.assessor._assess_freshness(now - timedelta(weeks=1))
        assert week_old_score <= 0.5


class TestContentDeduplicationService:
    """Test content deduplication service functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.config = DeduplicationConfig(
            enable_exact_matching=True,
            enable_fuzzy_matching=True,
            enable_tfidf_matching=True,
            exact_threshold=1.0,
            fuzzy_threshold=0.95,
            tfidf_threshold=0.85
        )
        self.service = ContentDeduplicationService(self.config)
    
    def test_service_initialization(self):
        """Test deduplication service initialization."""
        assert self.service.config == self.config
        assert len(self.service.fingerprints) == 0
        assert self.service.stats.total_articles_processed == 0
    
    @pytest.mark.asyncio
    async def test_fingerprint_creation(self):
        """Test content fingerprint creation."""
        article = {
            'id': 'test_article',
            'title': 'Test News Article',
            'content': 'This is a test article with sufficient content for fingerprinting and analysis.'
        }
        
        fingerprint = await self.service._create_fingerprint(article)
        
        assert fingerprint is not None
        assert fingerprint.article_id == 'test_article'
        assert fingerprint.exact_hash is not None
        assert fingerprint.fuzzy_hash is not None
        assert fingerprint.title_hash is not None
        assert len(fingerprint.content_tokens) > 0
        assert fingerprint.created_at is not None
    
    @pytest.mark.asyncio
    async def test_exact_duplicate_detection(self):
        """Test exact duplicate detection."""
        articles = [
            {
                'id': 'article_1',
                'title': 'Same Title',
                'content': 'Exactly the same content for testing duplicate detection.'
            },
            {
                'id': 'article_2',
                'title': 'Same Title',
                'content': 'Exactly the same content for testing duplicate detection.'
            }
        ]
        
        unique_articles, duplicate_matches = await self.service.process_articles(articles)

        assert len(unique_articles) == 1  # One duplicate removed
        assert len(duplicate_matches) >= 1  # At least one duplicate match found

        # Check for exact duplicate match
        exact_matches = [m for m in duplicate_matches if m.method == SimilarityMethod.EXACT_HASH]
        assert len(exact_matches) >= 1
        assert exact_matches[0].similarity_score == 1.0
    
    @pytest.mark.asyncio
    async def test_near_duplicate_detection(self):
        """Test near duplicate detection."""
        articles = [
            {
                'id': 'article_1',
                'title': 'News Article Title',
                'content': 'This is the original news article with specific content and details.'
            },
            {
                'id': 'article_2',
                'title': 'News Article Title',
                'content': 'This is the original news article with specific content and some details.'
            }
        ]
        
        unique_articles, duplicate_matches = await self.service.process_articles(articles)
        
        # Should detect similarity even with slight differences
        assert len(duplicate_matches) >= 1
        
        # Check for fuzzy or title matches
        fuzzy_or_title_matches = [
            m for m in duplicate_matches 
            if m.method in [SimilarityMethod.FUZZY_HASH, SimilarityMethod.TITLE_SIMILARITY]
        ]
        assert len(fuzzy_or_title_matches) >= 1
    
    @pytest.mark.asyncio
    async def test_unique_content_preservation(self):
        """Test that unique content is preserved."""
        articles = [
            {
                'id': 'article_1',
                'title': 'First News Story',
                'content': 'This is the first unique news story with distinct content and information.'
            },
            {
                'id': 'article_2',
                'title': 'Second News Story',
                'content': 'This is the second unique news story with completely different content.'
            },
            {
                'id': 'article_3',
                'title': 'Third News Story',
                'content': 'This is the third unique news story with its own distinct information.'
            }
        ]
        
        unique_articles, duplicate_matches = await self.service.process_articles(articles)
        
        # All articles should be unique
        assert len(unique_articles) == 3
        assert len(duplicate_matches) == 0
    
    def test_content_preprocessing(self):
        """Test content preprocessing functionality."""
        original_content = "  This is   CONTENT with   Extra    Whitespace!!! @#$%  "
        processed_content = self.service._preprocess_content(original_content)
        
        assert processed_content == "this is content with extra whitespace"
        assert "  " not in processed_content  # No double spaces
        assert processed_content.islower()  # Lowercase
    
    def test_fuzzy_hash_generation(self):
        """Test fuzzy hash generation."""
        content = "this is a test article with some repeated words test article words"
        fuzzy_hash = self.service._generate_fuzzy_hash(content)
        
        assert fuzzy_hash is not None
        assert len(fuzzy_hash) == 32  # MD5 hash length
        
        # Similar content should produce similar fuzzy hashes
        similar_content = "this is a test article with some repeated words test article"
        similar_hash = self.service._generate_fuzzy_hash(similar_content)
        
        # Should be similar but not necessarily identical
        assert fuzzy_hash is not None
        assert similar_hash is not None
    
    def test_stats_tracking(self):
        """Test statistics tracking."""
        stats = self.service.get_stats()
        
        assert stats.total_articles_processed == 0
        assert stats.exact_duplicates_found == 0
        assert stats.fingerprints_in_memory == 0
        assert isinstance(stats.processing_time_seconds, float)
    
    def test_health_status(self):
        """Test health status monitoring."""
        health = self.service.get_health_status()
        
        assert health["service"] == "content_deduplication"
        assert health["status"] == "healthy"
        assert "fingerprints_in_memory" in health
        assert "duplicate_detection_rate" in health


if __name__ == "__main__":
    pytest.main([__file__])
