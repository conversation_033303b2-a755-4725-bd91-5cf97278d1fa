"""
Test suite for Simple Pipeline Orchestrator

This test suite validates the pipeline orchestration functionality including
workflow coordination, error handling, retry logic, and status monitoring.
"""

import pytest
import asyncio
from datetime import datetime, timezone
from unittest.mock import AsyncMock, MagicMock, patch

from services.pipeline_orchestrator import (
    SimplePipelineOrchestrator, 
    PipelineConfig, 
    PipelineJob, 
    PipelineStage,
    RetryPolicy
)
from services.database.models import ArticleData, CelebrityData, ProcessingStatus
from services.llm_processor.models import CelebrityMatch, CelebrityIdentificationResponse


class TestPipelineOrchestrator:
    """Test pipeline orchestrator functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.config = PipelineConfig(
            news_batch_size=5,
            news_processing_timeout=60,  # Within valid range
            max_retries=2,
            celebrity_confidence_threshold=0.7
        )
        
        # Mock all services to avoid actual initialization
        with patch.multiple(
            'services.pipeline_orchestrator',
            NewsIngestionService=MagicMock(),
            LLMOrchestrator=MagicMock(),
            CelebrityDataValidator=MagicMock(),
            EphemerisCalculator=MagicMock(),
            AspectEngine=MagicMock(),
            ArticleDBService=MagicMock(),
            CelebrityDBService=MagicMock()
        ):
            self.orchestrator = SimplePipelineOrchestrator(self.config)
        
        # Test data
        self.test_article = ArticleData(
            id="test-article-1",
            title="Celebrity News Test",
            content="This is a test article about Leonardo DiCaprio winning an Oscar.",
            summary="Leonardo DiCaprio wins Oscar",
            url="https://example.com/test",
            published_at=datetime.now(timezone.utc),
            source="test_source",
            status=ProcessingStatus.PENDING
        )
        
        self.test_celebrity = CelebrityData(
            name="Leonardo DiCaprio",
            birth_date=datetime(1974, 11, 11),
            confidence_score=0.95,
            data_sources=[],
            enhanced=True,
            verified=True,
            aliases=["Leo"]
        )
    
    def test_orchestrator_initialization(self):
        """Test orchestrator initialization."""
        assert self.orchestrator.config == self.config
        assert len(self.orchestrator.active_jobs) == 0
        assert len(self.orchestrator.completed_jobs) == 0
        assert self.orchestrator.stats.jobs_processed == 0
    
    def test_pipeline_config_defaults(self):
        """Test pipeline configuration defaults."""
        default_config = PipelineConfig()
        
        assert default_config.news_batch_size == 20
        assert default_config.max_retries == 3
        assert default_config.retry_policy == RetryPolicy.EXPONENTIAL_BACKOFF
        assert default_config.celebrity_confidence_threshold == 0.7
        assert default_config.enable_detailed_logging == True
    
    def test_pipeline_job_creation(self):
        """Test pipeline job creation and tracking."""
        job = PipelineJob(article_data=self.test_article)
        
        assert job.job_id is not None
        assert job.stage == PipelineStage.NEWS_INGESTION
        assert job.article_data == self.test_article
        assert job.retry_count == 0
        assert len(job.celebrities) == 0
        assert job.astrology_results is None
    
    @pytest.mark.asyncio
    async def test_celebrity_extraction_stage(self):
        """Test celebrity extraction stage processing."""
        # Create test job
        job = PipelineJob(article_data=self.test_article)
        
        # Mock LLM response
        mock_celebrity_match = CelebrityMatch(
            name="Leonardo DiCaprio",
            confidence=0.95,
            context="Oscar winner",
            aliases=["Leo"]
        )
        
        mock_response = CelebrityIdentificationResponse(
            raw_response='{"celebrities": [{"name": "Leonardo DiCaprio", "confidence": 0.95}]}',
            provider="ollama",
            model="test-model",
            task_type="celebrity_identification",
            confidence_score=0.95,
            quality_score=0.85,
            processing_time_ms=150.0,
            celebrities=[mock_celebrity_match],
            article_title=self.test_article.title
        )
        
        # Mock the LLM orchestrator
        self.orchestrator.llm_orchestrator.identify_celebrities = AsyncMock(return_value=mock_response)
        
        # Process celebrity extraction
        await self.orchestrator._process_celebrity_extraction(job)
        
        # Verify results
        assert job.stage == PipelineStage.CELEBRITY_EXTRACTION
        assert len(job.celebrities) == 1
        assert job.celebrities[0].name == "Leonardo DiCaprio"
        assert job.celebrities[0].confidence_score == 0.95
        assert 'celebrity_extraction' in job.stage_timings
    
    @pytest.mark.asyncio
    async def test_celebrity_validation_stage(self):
        """Test celebrity validation stage processing."""
        # Create test job with celebrities
        job = PipelineJob(
            article_data=self.test_article,
            celebrities=[self.test_celebrity]
        )
        
        # Mock celebrity validator
        enhanced_celebrity = CelebrityData(
            name="Leonardo DiCaprio",
            birth_date=datetime(1974, 11, 11),
            confidence_score=0.98,
            data_sources=["astro_databank"],
            enhanced=True,
            verified=True,
            aliases=["Leo", "Leonardo Wilhelm DiCaprio"]
        )
        
        self.orchestrator.celebrity_validator.validate_and_enhance_celebrity = AsyncMock(
            return_value=(enhanced_celebrity, 0.98)
        )
        
        # Process celebrity validation
        await self.orchestrator._process_celebrity_validation(job)
        
        # Verify results
        assert job.stage == PipelineStage.CELEBRITY_VALIDATION
        assert len(job.celebrities) == 1
        assert job.celebrities[0].confidence_score == 0.98
        assert job.celebrities[0].enhanced == True
        assert 'celebrity_validation' in job.stage_timings
    
    @pytest.mark.asyncio
    async def test_astrology_analysis_stage(self):
        """Test astrology analysis stage processing."""
        # Create test job with validated celebrities
        job = PipelineJob(
            article_data=self.test_article,
            celebrities=[self.test_celebrity]
        )
        
        # Mock astrology services
        mock_chart_data = {
            'sun': {'longitude': 225.5, 'sign': 'Scorpio'},
            'moon': {'longitude': 45.2, 'sign': 'Taurus'}
        }
        
        mock_aspects = [
            {'planet1': 'sun', 'planet2': 'moon', 'aspect': 'opposition', 'orb': 0.3}
        ]
        
        self.orchestrator.ephemeris_calculator.calculate_chart = AsyncMock(return_value=mock_chart_data)
        self.orchestrator.aspect_engine.calculate_aspects = MagicMock(return_value=mock_aspects)
        
        # Process astrology analysis
        await self.orchestrator._process_astrology_analysis(job)
        
        # Verify results
        assert job.stage == PipelineStage.ASTROLOGY_ANALYSIS
        assert job.astrology_results is not None
        assert job.astrology_results['total_celebrities'] == 1
        assert job.astrology_results['analyzed_celebrities'] == 1
        assert len(job.astrology_results['results']) == 1
        assert 'astrology_analysis' in job.stage_timings
    
    @pytest.mark.asyncio
    async def test_error_handling_and_retry(self):
        """Test error handling and retry logic."""
        job = PipelineJob(article_data=self.test_article)
        
        # Simulate error
        error_message = "Test error for retry logic"
        
        # Test retry calculation
        delay1 = self.orchestrator._calculate_retry_delay(1)
        delay2 = self.orchestrator._calculate_retry_delay(2)
        
        # Exponential backoff should increase delay
        assert delay2 > delay1
        assert delay1 == self.config.base_retry_delay
        assert delay2 == self.config.base_retry_delay * 2
        
        # Test error handling
        await self.orchestrator._handle_job_error(job, error_message)
        
        # Verify error tracking
        assert job.last_error == error_message
        assert len(job.error_history) == 1
        assert error_message in job.error_history[0]
    
    def test_retry_policy_calculations(self):
        """Test different retry policy calculations."""
        # Test linear backoff
        config_linear = PipelineConfig(retry_policy=RetryPolicy.LINEAR_BACKOFF, base_retry_delay=2.0)
        orchestrator_linear = SimplePipelineOrchestrator(config_linear)
        
        assert orchestrator_linear._calculate_retry_delay(1) == 2.0
        assert orchestrator_linear._calculate_retry_delay(2) == 4.0
        assert orchestrator_linear._calculate_retry_delay(3) == 6.0
        
        # Test exponential backoff
        config_exp = PipelineConfig(retry_policy=RetryPolicy.EXPONENTIAL_BACKOFF, base_retry_delay=1.0)
        orchestrator_exp = SimplePipelineOrchestrator(config_exp)
        
        assert orchestrator_exp._calculate_retry_delay(1) == 1.0
        assert orchestrator_exp._calculate_retry_delay(2) == 2.0
        assert orchestrator_exp._calculate_retry_delay(3) == 4.0
        
        # Test no retry
        config_none = PipelineConfig(retry_policy=RetryPolicy.NO_RETRY)
        orchestrator_none = SimplePipelineOrchestrator(config_none)
        
        assert orchestrator_none._calculate_retry_delay(1) == 0.0
        assert orchestrator_none._calculate_retry_delay(5) == 0.0
    
    def test_job_status_tracking(self):
        """Test job status tracking and retrieval."""
        job = PipelineJob(article_data=self.test_article)
        job.started_at = datetime.now(timezone.utc)
        job.stage = PipelineStage.CELEBRITY_EXTRACTION
        job.celebrities = [self.test_celebrity]
        job.stage_timings = {'celebrity_extraction': 1.5}
        
        self.orchestrator.active_jobs[job.job_id] = job
        
        # Get job status
        status = self.orchestrator.get_job_status(job.job_id)
        
        assert status is not None
        assert status['job_id'] == job.job_id
        assert status['stage'] == PipelineStage.CELEBRITY_EXTRACTION.value
        assert status['celebrities_found'] == 1
        assert status['astrology_completed'] == False
        assert 'celebrity_extraction' in status['stage_timings']
    
    def test_pipeline_statistics(self):
        """Test pipeline statistics tracking."""
        # Simulate some processing
        self.orchestrator.stats.jobs_processed = 10
        self.orchestrator.stats.jobs_completed = 8
        self.orchestrator.stats.jobs_failed = 2
        self.orchestrator.stats.total_processing_time = 45.0
        self.orchestrator.stats.average_processing_time = 45.0 / 8  # Calculate manually for test
        self.orchestrator.stats.celebrity_extraction_count = 15
        
        stats = self.orchestrator.get_pipeline_stats()
        
        assert stats['jobs_processed'] == 10
        assert stats['jobs_completed'] == 8
        assert stats['jobs_failed'] == 2
        assert stats['success_rate'] == 0.8
        assert stats['average_processing_time'] == 45.0 / 8
        assert stats['stage_counts']['celebrity_extraction'] == 15
    
    def test_health_status_monitoring(self):
        """Test health status monitoring."""
        # Add some active jobs
        job1 = PipelineJob(article_data=self.test_article)
        job2 = PipelineJob(article_data=self.test_article)
        self.orchestrator.active_jobs[job1.job_id] = job1
        self.orchestrator.active_jobs[job2.job_id] = job2
        
        # Add completed job
        completed_job = PipelineJob(article_data=self.test_article)
        completed_job.stage = PipelineStage.COMPLETED
        self.orchestrator.completed_jobs.append(completed_job)
        
        health = self.orchestrator.get_health_status()
        
        assert health['service'] == 'pipeline_orchestrator'
        assert health['status'] == 'healthy'
        assert health['active_jobs'] == 2
        assert health['completed_jobs'] == 1
        assert 'services' in health
        assert health['services']['news_service'] == 'healthy'
    
    @pytest.mark.asyncio
    async def test_confidence_threshold_filtering(self):
        """Test celebrity confidence threshold filtering."""
        job = PipelineJob(article_data=self.test_article)
        
        # Mock LLM response with mixed confidence celebrities
        low_confidence_celebrity = CelebrityMatch(
            name="Unknown Actor",
            confidence=0.5,  # Below threshold
            context="Minor mention",
            aliases=[]
        )
        
        high_confidence_celebrity = CelebrityMatch(
            name="Leonardo DiCaprio",
            confidence=0.95,  # Above threshold
            context="Main subject",
            aliases=["Leo"]
        )
        
        mock_response = CelebrityIdentificationResponse(
            raw_response='{"celebrities": []}',
            provider="ollama",
            model="test-model",
            task_type="celebrity_identification",
            confidence_score=0.8,
            quality_score=0.8,
            processing_time_ms=150.0,
            celebrities=[low_confidence_celebrity, high_confidence_celebrity],
            article_title=self.test_article.title
        )
        
        self.orchestrator.llm_orchestrator.identify_celebrities = AsyncMock(return_value=mock_response)
        
        # Process celebrity extraction
        await self.orchestrator._process_celebrity_extraction(job)
        
        # Verify only high-confidence celebrity is kept
        assert len(job.celebrities) == 1
        assert job.celebrities[0].name == "Leonardo DiCaprio"
        assert job.celebrities[0].confidence_score == 0.95
    
    def test_stage_timing_tracking(self):
        """Test stage timing tracking."""
        job = PipelineJob(article_data=self.test_article)
        
        # Simulate stage timings
        job.stage_timings = {
            'celebrity_extraction': 2.5,
            'celebrity_validation': 1.2,
            'astrology_analysis': 0.8
        }
        
        total_stage_time = sum(job.stage_timings.values())
        assert total_stage_time == 4.5
        
        # Verify all expected stages are tracked
        expected_stages = ['celebrity_extraction', 'celebrity_validation', 'astrology_analysis']
        for stage in expected_stages:
            assert stage in job.stage_timings


if __name__ == "__main__":
    pytest.main([__file__])
