{"webhook_name": "star-power-frontend-sync", "description": "Automated synchronization between star-power and star-power-fe repositories", "version": "1.0.0", "webhooks": {"star_power_fe_to_main": {"description": "Sync layout changes from star-power-fe to main repo", "trigger": {"repository": "VerbalClaymore/star-power-fe", "events": ["push"], "branches": ["main"], "file_patterns": ["client/src/components/**/*.tsx", "client/src/components/**/*.ts", "client/src/styles/**/*", "tailwind.config.ts", "postcss.config.js", "components.json", "client/src/index.css", "client/src/lib/utils.ts", "client/src/hooks/use-mobile.tsx", "client/src/hooks/use-toast.ts", "client/src/pages/profile.tsx", "client/src/pages/saved.tsx", "client/src/pages/not-found.tsx"]}, "action": {"type": "sync_files", "target_repository": "VerbalClaymore/star-power", "target_directory": "frontend-integration", "sync_strategy": "selective_overwrite", "preserve_ownership": true, "backup_before_sync": true}, "filters": {"ownership_check": true, "only_layout_files": true, "preserve_logic_files": ["client/src/services/**/*", "client/src/hooks/useStarPowerApi.ts", "server/**/*"]}}, "main_to_star_power_fe": {"description": "Sync logic changes from main repo to star-power-fe", "trigger": {"repository": "VerbalClaymore/star-power", "events": ["push"], "branches": ["main"], "file_patterns": ["frontend-integration/client/src/services/**/*", "frontend-integration/client/src/hooks/useStarPowerApi.ts", "frontend-integration/server/**/*", "frontend-integration/client/src/types/api.ts", "frontend-integration/shared/schema.ts", "frontend-integration/client/src/pages/home.tsx", "frontend-integration/client/src/pages/article.tsx", "frontend-integration/client/src/pages/actor-profile.tsx", "frontend-integration/client/src/pages/search.tsx", "frontend-integration/client/src/pages/api-test.tsx"]}, "action": {"type": "sync_files", "target_repository": "VerbalClaymore/star-power-fe", "source_directory": "frontend-integration", "sync_strategy": "selective_overwrite", "preserve_ownership": true, "backup_before_sync": true}, "filters": {"ownership_check": true, "only_logic_and_shared_files": true, "preserve_layout_files": ["client/src/components/ui/**/*", "client/src/components/ArticleCard.tsx", "client/src/components/TopNavigation.tsx", "client/src/components/BottomNavigation.tsx", "client/src/components/PlanetIcon.tsx", "tailwind.config.ts", "postcss.config.js", "components.json"]}}}, "conflict_resolution": {"strategy": "ownership_based", "rules": [{"condition": "file_has_layout_ownership", "action": "preserve_in_layout_repo", "source": "star-power-fe"}, {"condition": "file_has_logic_ownership", "action": "preserve_in_main_repo", "source": "star-power"}, {"condition": "file_has_shared_ownership", "action": "manual_review_required", "notification": "slack_channel"}], "manual_review_threshold": "shared_files", "auto_merge_threshold": "ownership_clear"}, "notification_settings": {"success_notifications": {"enabled": true, "channels": ["slack", "email"], "template": "Sync completed: {{files_synced}} files updated between {{source_repo}} and {{target_repo}}"}, "conflict_notifications": {"enabled": true, "channels": ["slack", "email"], "template": "Manual review required: {{conflict_files}} files need attention in {{repository}}"}, "error_notifications": {"enabled": true, "channels": ["slack", "email", "github_issue"], "template": "Sync failed: {{error_message}} in {{repository}}"}}, "security": {"verify_ownership_headers": true, "require_signed_commits": false, "allowed_file_extensions": [".tsx", ".ts", ".css", ".json", ".js", ".md"], "blocked_file_patterns": ["**/.env*", "**/node_modules/**", "**/.git/**", "**/dist/**", "**/build/**"], "max_file_size": "1MB", "rate_limiting": {"max_syncs_per_hour": 10, "max_files_per_sync": 100}}, "testing": {"dry_run_mode": true, "test_repositories": {"source": "VerbalClaymore/star-power-test", "target": "VerbalClaymore/star-power-fe-test"}, "validation_checks": ["ownership_headers_present", "no_syntax_errors", "typescript_compilation", "file_structure_intact"]}, "backup": {"enabled": true, "retention_days": 30, "backup_location": "webhooks/backups/{{timestamp}}", "include_metadata": true}, "monitoring": {"health_checks": {"interval": "5m", "endpoints": ["https://api.github.com/repos/VerbalClaymore/star-power", "https://api.github.com/repos/VerbalClaymore/star-power-fe"]}, "metrics": {"track_sync_frequency": true, "track_conflict_rate": true, "track_file_sizes": true, "track_processing_time": true}}}